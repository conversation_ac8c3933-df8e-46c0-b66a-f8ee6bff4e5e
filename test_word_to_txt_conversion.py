#!/usr/bin/env python3
"""
Test script to demonstrate Word to TXT conversion functionality
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.utils.file_utils.word_utils import (
    extract_text_from_word_document, 
    convert_word_to_txt_bytes,
    convert_word_to_txt
)

def create_sample_docx():
    """Create a sample .docx file for testing"""
    from docx import Document
    from io import BytesIO
    
    # Create a test document
    doc = Document()
    doc.add_heading('Sample Document for Testing', 0)
    doc.add_paragraph('This is a sample paragraph to test Word to TXT conversion.')
    doc.add_paragraph('Here is another paragraph with some additional content.')
    doc.add_paragraph('This document contains multiple paragraphs to verify that the conversion preserves the text structure.')
    
    # Add a table
    table = doc.add_table(rows=3, cols=2)
    table.cell(0, 0).text = 'Name'
    table.cell(0, 1).text = 'Value'
    table.cell(1, 0).text = 'Test Item 1'
    table.cell(1, 1).text = 'Sample Value 1'
    table.cell(2, 0).text = 'Test Item 2'
    table.cell(2, 1).text = 'Sample Value 2'
    
    doc.add_paragraph('This text comes after the table.')
    
    # Save to BytesIO
    docx_stream = BytesIO()
    doc.save(docx_stream)
    return docx_stream.getvalue()

def test_text_extraction():
    """Test basic text extraction"""
    print("=" * 60)
    print("Testing Word Document Text Extraction")
    print("=" * 60)
    
    # Create sample document
    docx_content = create_sample_docx()
    
    # Test extraction
    extracted_text = extract_text_from_word_document(docx_content, 'docx')
    
    if extracted_text:
        print("✅ Text extraction successful!")
        print("\nExtracted text:")
        print("-" * 40)
        print(extracted_text)
        print("-" * 40)
        print(f"Text length: {len(extracted_text)} characters")
        return True
    else:
        print("❌ Text extraction failed!")
        return False

def test_txt_conversion_bytes():
    """Test conversion to TXT bytes"""
    print("\n" + "=" * 60)
    print("Testing Word to TXT Conversion (Bytes)")
    print("=" * 60)
    
    # Create sample document
    docx_content = create_sample_docx()
    
    # Test conversion
    txt_bytes = convert_word_to_txt_bytes(docx_content, 'docx')
    
    if txt_bytes:
        print("✅ TXT bytes conversion successful!")
        print(f"TXT content size: {len(txt_bytes)} bytes")
        
        # Decode and show first 200 characters
        txt_content = txt_bytes.decode('utf-8')
        print("\nFirst 200 characters of TXT content:")
        print("-" * 40)
        print(txt_content[:200] + "..." if len(txt_content) > 200 else txt_content)
        print("-" * 40)
        return True
    else:
        print("❌ TXT bytes conversion failed!")
        return False

def test_txt_conversion_file():
    """Test conversion to TXT file"""
    print("\n" + "=" * 60)
    print("Testing Word to TXT Conversion (File)")
    print("=" * 60)
    
    # Create sample document
    docx_content = create_sample_docx()
    
    # Test conversion to file
    output_path = "/tmp/test_converted_document.txt"
    extracted_text, txt_file_path = convert_word_to_txt(docx_content, 'docx', output_path)
    
    if extracted_text and txt_file_path and os.path.exists(txt_file_path):
        print("✅ TXT file conversion successful!")
        print(f"TXT file saved to: {txt_file_path}")
        
        # Read and verify file content
        with open(txt_file_path, 'r', encoding='utf-8') as f:
            file_content = f.read()
        
        print(f"File size: {os.path.getsize(txt_file_path)} bytes")
        print(f"Content matches: {file_content == extracted_text}")
        
        # Clean up
        try:
            os.remove(txt_file_path)
            print("✅ Test file cleaned up")
        except:
            print("⚠️ Could not clean up test file")
        
        return True
    else:
        print("❌ TXT file conversion failed!")
        return False

def main():
    """Run all tests"""
    print("Word Document to TXT Conversion Test Suite")
    print("=" * 60)
    
    test1 = test_text_extraction()
    test2 = test_txt_conversion_bytes()
    test3 = test_txt_conversion_file()
    
    print("\n" + "=" * 60)
    print("Test Results Summary:")
    print("=" * 60)
    print(f"  Text Extraction:     {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"  TXT Bytes Conversion: {'✅ PASS' if test2 else '❌ FAIL'}")
    print(f"  TXT File Conversion:  {'✅ PASS' if test3 else '❌ FAIL'}")
    
    if all([test1, test2, test3]):
        print("\n🎉 All tests passed! Word to TXT conversion is working perfectly.")
        print("\nAvailable API endpoints:")
        print("  - POST /file/convert-word-to-txt (returns TXT file download)")
        print("  - POST /file/convert-word-to-txt-json (returns JSON with extracted text)")
        print("  - POST /file/upload-attachment?convert_word_to_txt=true (uploads with optional TXT conversion)")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
