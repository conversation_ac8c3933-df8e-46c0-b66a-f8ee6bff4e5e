#!/usr/bin/env python3
"""
Simple test cases for Word to TXT conversion
"""

import sys
import os
sys.path.insert(0, 'app')

def test_case_1_basic_import():
    """Test Case 1: Basic import functionality"""
    print("🧪 Test Case 1: Basic Import Test")
    print("-" * 40)
    
    try:
        from app.utils.file_utils.word_utils import (
            extract_text_from_word_document, 
            convert_word_to_txt_bytes,
            convert_word_to_txt
        )
        print("✅ All imports successful")
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_case_2_create_docx():
    """Test Case 2: Create a simple DOCX document"""
    print("\n🧪 Test Case 2: Create DOCX Document")
    print("-" * 40)
    
    try:
        from docx import Document
        from io import BytesIO
        
        # Create a simple document
        doc = Document()
        doc.add_paragraph('Hello World!')
        doc.add_paragraph('This is a test document.')
        
        # Save to bytes
        docx_stream = BytesIO()
        doc.save(docx_stream)
        docx_content = docx_stream.getvalue()
        
        print(f"✅ DOCX document created successfully ({len(docx_content)} bytes)")
        return docx_content
    except Exception as e:
        print(f"❌ DOCX creation failed: {e}")
        return None

def test_case_3_extract_text(docx_content):
    """Test Case 3: Extract text from DOCX"""
    print("\n🧪 Test Case 3: Extract Text from DOCX")
    print("-" * 40)
    
    if not docx_content:
        print("❌ No DOCX content to test")
        return False
    
    try:
        from app.utils.file_utils.word_utils import extract_text_from_word_document
        
        extracted_text = extract_text_from_word_document(docx_content, 'docx')
        
        if extracted_text:
            print("✅ Text extraction successful")
            print(f"📝 Extracted text: '{extracted_text.strip()}'")
            print(f"📏 Text length: {len(extracted_text)} characters")
            return extracted_text
        else:
            print("❌ Text extraction returned None")
            return None
    except Exception as e:
        print(f"❌ Text extraction failed: {e}")
        return None

def test_case_4_convert_to_bytes(docx_content):
    """Test Case 4: Convert DOCX to TXT bytes"""
    print("\n🧪 Test Case 4: Convert to TXT Bytes")
    print("-" * 40)
    
    if not docx_content:
        print("❌ No DOCX content to test")
        return False
    
    try:
        from app.utils.file_utils.word_utils import convert_word_to_txt_bytes
        
        txt_bytes = convert_word_to_txt_bytes(docx_content, 'docx')
        
        if txt_bytes:
            print("✅ TXT bytes conversion successful")
            print(f"📦 TXT bytes length: {len(txt_bytes)} bytes")
            
            # Decode and show content
            txt_content = txt_bytes.decode('utf-8')
            print(f"📝 TXT content: '{txt_content.strip()}'")
            return True
        else:
            print("❌ TXT bytes conversion returned None")
            return False
    except Exception as e:
        print(f"❌ TXT bytes conversion failed: {e}")
        return False

def test_case_5_convert_to_file(docx_content):
    """Test Case 5: Convert DOCX to TXT file"""
    print("\n🧪 Test Case 5: Convert to TXT File")
    print("-" * 40)
    
    if not docx_content:
        print("❌ No DOCX content to test")
        return False
    
    try:
        from app.utils.file_utils.word_utils import convert_word_to_txt
        
        output_path = "/tmp/test_conversion.txt"
        extracted_text, txt_file_path = convert_word_to_txt(docx_content, 'docx', output_path)
        
        if extracted_text and txt_file_path and os.path.exists(txt_file_path):
            print("✅ TXT file conversion successful")
            print(f"📁 File saved to: {txt_file_path}")
            
            # Read file and verify
            with open(txt_file_path, 'r', encoding='utf-8') as f:
                file_content = f.read()
            
            print(f"📝 File content: '{file_content.strip()}'")
            print(f"✅ Content matches: {file_content == extracted_text}")
            
            # Clean up
            os.remove(txt_file_path)
            print("🧹 Test file cleaned up")
            return True
        else:
            print("❌ TXT file conversion failed")
            return False
    except Exception as e:
        print(f"❌ TXT file conversion failed: {e}")
        return False

def test_case_6_complex_document():
    """Test Case 6: Test with a more complex document"""
    print("\n🧪 Test Case 6: Complex Document Test")
    print("-" * 40)
    
    try:
        from docx import Document
        from io import BytesIO
        from app.utils.file_utils.word_utils import extract_text_from_word_document
        
        # Create a complex document
        doc = Document()
        doc.add_heading('Test Document', 0)
        doc.add_paragraph('This is paragraph 1.')
        doc.add_paragraph('This is paragraph 2 with more content.')
        
        # Add a table
        table = doc.add_table(rows=2, cols=2)
        table.cell(0, 0).text = 'Name'
        table.cell(0, 1).text = 'Value'
        table.cell(1, 0).text = 'Test'
        table.cell(1, 1).text = '123'
        
        doc.add_paragraph('Text after table.')
        
        # Save to bytes
        docx_stream = BytesIO()
        doc.save(docx_stream)
        docx_content = docx_stream.getvalue()
        
        # Extract text
        extracted_text = extract_text_from_word_document(docx_content, 'docx')
        
        if extracted_text:
            print("✅ Complex document processing successful")
            print(f"📝 Extracted text preview:")
            print("-" * 30)
            print(extracted_text[:200] + "..." if len(extracted_text) > 200 else extracted_text)
            print("-" * 30)
            
            # Check if table content is included
            has_table_content = 'Name' in extracted_text and 'Value' in extracted_text
            print(f"📊 Table content extracted: {'✅' if has_table_content else '❌'}")
            
            return True
        else:
            print("❌ Complex document processing failed")
            return False
    except Exception as e:
        print(f"❌ Complex document test failed: {e}")
        return False

def run_all_tests():
    """Run all test cases"""
    print("🚀 Starting Word to TXT Conversion Test Suite")
    print("=" * 60)
    
    results = []
    
    # Test Case 1: Basic imports
    results.append(test_case_1_basic_import())
    
    # Test Case 2: Create DOCX
    docx_content = test_case_2_create_docx()
    results.append(docx_content is not None)
    
    # Test Case 3: Extract text
    extracted_text = test_case_3_extract_text(docx_content)
    results.append(extracted_text is not None)
    
    # Test Case 4: Convert to bytes
    results.append(test_case_4_convert_to_bytes(docx_content))
    
    # Test Case 5: Convert to file
    results.append(test_case_5_convert_to_file(docx_content))
    
    # Test Case 6: Complex document
    results.append(test_case_6_complex_document())
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    test_names = [
        "Basic Import",
        "Create DOCX",
        "Extract Text",
        "Convert to Bytes",
        "Convert to File",
        "Complex Document"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results), 1):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  Test {i}: {name:<20} {status}")
    
    passed = sum(results)
    total = len(results)
    
    print(f"\n📈 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Word to TXT conversion is working perfectly!")
        return 0
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(run_all_tests())
