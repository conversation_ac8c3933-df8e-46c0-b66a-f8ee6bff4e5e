# app/services/supabase_management_service.py
import httpx
import os
from typing import Dict, List, Optional
from datetime import datetime
from app.services.supabase_database_service import SupabaseDatabaseService

supabase_data_service = SupabaseDatabaseService()

class SupabaseManagementService:
    def __init__(self):
        self.base_url = "https://api.supabase.com/v1"
    
    def get_headers(self, access_token: str) -> Dict[str, str]:
        """Get headers with user's access token"""
        return {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
    
    async def create_project(
        self,
        user_id:str,
        project_id:str,
        name: str, 
        organization_id: str,
        db_password: str,
        region: str = "us-east-1"
    ) -> Dict:
        """Create a new Supabase project"""
        async with httpx.AsyncClient() as client:
            try:
                tokens = await supabase_data_service.get_user_supabase_tokens(user_id,project_id)
                access_token = tokens['access_token']
                response = await client.post(
                    f"{self.base_url}/projects",
                    headers=self.get_headers(access_token),
                    json={
                        "name": name,
                        "organization_id": organization_id,
                        "db_pass": db_password,
                        "region": region
                    },
                    timeout=30.0
                )
                
                if response.status_code == 201:
                    return {"success": True, "data": response.json()}
                else:
                    return {
                        "success": False, 
                        "error": f"Failed to create project: {response.text}",
                        "status_code": response.status_code
                    }
                    
            except httpx.TimeoutException:
                return {"success": False, "error": "Request timeout"}
            except Exception as e:
                return {"success": False, "error": str(e)}
    
    async def list_projects(self,  user_id,project_id,organization_id: Optional[str] = None) -> Dict:
        """List all projects or projects for a specific organization"""
        async with httpx.AsyncClient() as client:
            try:
                tokens = await supabase_data_service.get_user_supabase_tokens(user_id,project_id)
                access_token = tokens['access_token']
                url = f"{self.base_url}/projects"
                params = {}
                if organization_id:
                    params["organization_id"] = organization_id
                
                response = await client.get(
                    url,
                    headers=self.get_headers(access_token),
                    params=params,
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    projects = response.json()
                    return {
                        "success": True, 
                        "data": projects,
                        "count": len(projects)
                    }
                else:
                    return {
                        "success": False,
                        "error": f"Failed to fetch projects: {response.text}",
                        "status_code": response.status_code
                    }
                    
            except httpx.TimeoutException:
                return {"success": False, "error": "Request timeout"}
            except Exception as e:
                return {"success": False, "error": str(e)}
    
    async def get_project_details(self, user_id,project_id,project_id_supabase: str) -> Dict:
        """Get detailed project information including API keys"""
        async with httpx.AsyncClient() as client:
            try:
                tokens = await supabase_data_service.get_user_supabase_tokens(user_id,project_id)
                access_token = tokens['access_token']
                response = await client.get(
                    f"{self.base_url}/projects/{project_id_supabase}",
                    headers=self.get_headers(access_token),
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    return {"success": True, "data": response.json()}
                else:
                    return {
                        "success": False,
                        "error": f"Failed to fetch project details: {response.text}",
                        "status_code": response.status_code
                    }
                    
            except Exception as e:
                return {"success": False, "error": str(e)}

    async def get_project_api_keys(self, user_id,project_id,supabase_project_id: str) -> Dict:
        """Get project API keys (anon and service_role)"""
        async with httpx.AsyncClient() as client:
            try:
                tokens = await supabase_data_service.get_user_supabase_tokens(user_id,project_id)
                access_token = tokens['access_token']
                response = await client.get(
                    f"{self.base_url}/projects/{supabase_project_id}/api-keys",
                    headers=self.get_headers(access_token),
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    return {"success": True, "data": response.json()}
                else:
                    return {
                        "success": False,
                        "error": f"Failed to fetch API keys: {response.text}",
                        "status_code": response.status_code
                    }
                    
            except Exception as e:
                return {"success": False, "error": str(e)}
    
    async def get_organizations(self,user_id,project_id) -> Dict:
        """Get user organizations"""
        async with httpx.AsyncClient() as client:
            try:
                tokens = await supabase_data_service.get_user_supabase_tokens(user_id,project_id)
                access_token = tokens['access_token']
                response = await client.get(
                    f"{self.base_url}/organizations",
                    headers=self.get_headers(access_token),
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    return {"success": True, "data": response.json()}
                else:
                    return {
                        "success": False,
                        "error": f"Failed to fetch organizations: {response.text}",
                        "status_code": response.status_code
                    }
                    
            except Exception as e:
                return {"success": False, "error": str(e)}

    async def get_project_config(self,user_id,project_id, supabase_project_id: str) -> Dict:
        """Get project configuration including settings"""
        async with httpx.AsyncClient() as client:
            try:
                tokens = await supabase_data_service.get_user_supabase_tokens(user_id,project_id)
                access_token = tokens['access_token']
                response = await client.get(
                    f"{self.base_url}/projects/{supabase_project_id}/config",
                    headers=self.get_headers(access_token),
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    return {"success": True, "data": response.json()}
                else:
                    return {
                        "success": False,
                        "error": f"Failed to fetch project config: {response.text}",
                        "status_code": response.status_code
                    }
                    
            except Exception as e:
                return {"success": False, "error": str(e)}