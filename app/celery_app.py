from celery import Celery, Task
from contextvars import ContextVar
from functools import wraps
from app.core.Settings import settings
from app.connection.tenant_middleware import get_tenant_id, tenant_context
from app.telemetry.logger_config import setup_logging
import os
from app.core.websocket import websocket_manager
# Create tenant context
user_context = ContextVar("user_id", default="admin")

celery_websocket_client_sessions = ContextVar("celery_websocket_client_sessions", default={})

celery_task_id = ContextVar("celery_task_id", default="")

os.environ['CELERY_BROKER_URL'] = settings.CELERY_BROKER_URL
# Update Celery configuration
celery_app = Celery('kavia', broker=settings.CELERY_BROKER_URL)
celery_app.conf.update(
    broker_url=settings.CELERY_BROKER_URL,
    broker_connection_retry_on_startup=True,
    task_cls='app.celery_config:TenantTask'  # Use custom Task class
)
print("Celery app configured with url", settings.CELERY_BROKER_URL)

setup_logging()

# Utility functions for working with WebSocket sessions
def get_websocket_session(task_id: str):
    """Get the WebSocket client for a specific task"""
    return websocket_manager.get_session(task_id)

def create_websocket_session(task_id: str, metadata: dict = None):
    """Create a new WebSocket session for a task"""
    return websocket_manager.create_session(task_id, metadata=metadata)

def cleanup_websocket_session(task_id: str):
    """Clean up a task's WebSocket session"""
    return websocket_manager.delete_session(task_id)

async def handle_message(message): 
    print("Handling message ", message)
    pass
