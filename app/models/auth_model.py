from pydantic import BaseModel, EmailStr, Field
from app.core.Settings import settings
from app.utils.hash import encrypt_tenant_id
from typing import Optional


class CognitoUser(BaseModel):
    organization_id: str = Field(default=encrypt_tenant_id(settings.KAVIA_ROOT_TENANT_ID), description="Organization ID")
    email: EmailStr = Field(..., description="User's email address (used as the username in Cognito)")
    password: str = Field(..., description="User's password")
    
class SignUpUser(CognitoUser):
    name: str = Field(..., description="User's full name")
    designation: str = Field(..., description="User's designation")
    department: str = Field(..., description="User's department")

class GoogleAuthUser(BaseModel):
    organization_id: str = Field(default=encrypt_tenant_id(settings.KAVIA_ROOT_TENANT_ID), description="Organization ID")
    email: EmailStr = Field(..., description="User's email address")
    name: str = Field(..., description="User's full name")
    picture: Optional[str] = Field(None, description="User's profile picture URL")
    sub: Optional[str] = Field(None, description="Google subject identifier")
    designation: str = Field("", description="User's designation")
    department: str = Field("", description="User's department")
