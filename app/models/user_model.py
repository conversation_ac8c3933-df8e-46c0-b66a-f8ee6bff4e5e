from pydantic import <PERSON>Mode<PERSON>, <PERSON>,  validator
from typing import Optional, List, Dict
from datetime import datetime
import json
from app.utils.datetime_utils import generate_timestamp

# LLM Preferences for user 

from enum import Enum
from pydantic import BaseModel, <PERSON>, validator
from app.utils.aws import secrets_loader

VERTEX_SECRET = secrets_loader.get_vertex_secret()


class EnumEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Enum):
            return obj.value
        return super().default(obj)

class LLMModel(Enum):
    ### GPT Models
    ################## GPT MODEL START ##################
    gpt_4o_mini = "gpt-4o-mini"
    gpt_4o = "gpt-4o"
    gpt_4_1 = "gpt-4.1"
    gpt_4_turbo = "gpt-4-turbo" 
    gpt_3dot5 = "gpt-3.5-turbo"
    gpt_4_1_nano = "gpt-4.1-nano"
    
    ################## GPT MODEL END ##################

    ### Claude Models
    ################## CLAUDE MODEL START ##################
    
    claude_3_5_sonnet = "claude-3-5-sonnet-20241022"
    claude_3_7_sonnet = "claude-3-7-sonnet-20250219"
    bedrock_claude_3_5_sonnet = "bedrock/converse/us.anthropic.claude-3-5-sonnet-20241022-v2:0"
    bedrock_claude_3_7_sonnet = "bedrock/converse/us.anthropic.claude-3-7-sonnet-20250219-v1:0"
    bedrock_claude_3_5_haiku = "bedrock/us.anthropic.claude-3-5-haiku-20241022-v1:0"
    vertex_ai = "vertex_ai/claude-3-7-sonnet"
    vertex_claude_3_5_sonnet = "vertex_ai/claude-3-5-sonnet"
    vertex_claude_3_7_sonnet = "vertex_ai/claude-3-7-sonnet"
    ################## CLAUDE MODEL END ##################
    
    ### Gemini Models
    ################## GEMINI MODEL START ##################
    
    gemini_2_5_pro = "vertex_ai/gemini-2.5-pro-preview-05-06"
    
    ###################### GEMINI MODEL END ##################

class VertexLocation(Enum):
    US_CENTRAL1 = "us-central1"
    US_EAST5 = "us-east5"


def get_location(llm_model: LLMModel) -> str:
    if llm_model == LLMModel.gemini_2_5_pro:
        return VertexLocation.US_CENTRAL1.value
    if llm_model in [
        LLMModel.vertex_claude_3_5_sonnet,
        LLMModel.vertex_claude_3_7_sonnet,
        LLMModel.vertex_ai,
    ]:
        return VertexLocation.US_EAST5.value
    return VertexLocation.US_CENTRAL1.value
    
class Modules(Enum):
    DISCUSSION = {
        "name": "discussion",
        "display_name": "Discussion Framework",
        "available_models": [LLMModel.gpt_4o_mini, LLMModel.gpt_4o, LLMModel.gpt_4_turbo, LLMModel.gpt_3dot5,]
    }

    CODE_GENERATION = {
        "name": "code_generation",
        "display_name": "Code Generation",
        "available_models": [
            LLMModel.bedrock_claude_3_5_sonnet,
            LLMModel.bedrock_claude_3_7_sonnet,
            LLMModel.bedrock_claude_3_5_haiku,
            LLMModel.gpt_4o_mini,
            LLMModel.gpt_4o
        ]
    }

    CODE_INSPECTION = {
        "name": "code_inspection",
        "display_name": "Code Inspection",
        "available_models": [LLMModel.gpt_4o_mini, LLMModel.gpt_4o, LLMModel.gpt_4_turbo, LLMModel.claude_3_5_sonnet]
    }
    CONVERSATIONAL_CHAT = {
        "name": "conversational_chat",
        "display_name": "Conversational Chat",
        "available_models": [LLMModel.gpt_4o_mini, LLMModel.gpt_4o, LLMModel.gpt_4_turbo, LLMModel.claude_3_5_sonnet]
    }

class ModuleConfig(BaseModel):
    llm_model: LLMModel = Field(default=LLMModel.gpt_4o_mini, description="The selected Large Language Model")
    temperature: Optional[float] = Field(default=None, description="The temperature parameter for sampling")

class Module(BaseModel):
    name: str
    display_name: str
    current_model: LLMModel = Field(default=LLMModel.claude_3_5_sonnet, description="The current Large Language Model")
    available_models: List[LLMModel]

class UserConfig(BaseModel):
    modules: Dict[str, ModuleConfig] = Field(default_factory=dict, description="Module configurations")

    def model_dump_json(self, **kwargs):
        return json.dumps(self.model_dump(), cls=EnumEncoder, **kwargs)

available_modules = {
    Modules.DISCUSSION.value["name"]: Module(**Modules.DISCUSSION.value),
    Modules.CODE_GENERATION.value["name"]: Module(**Modules.CODE_GENERATION.value),
    Modules.CODE_INSPECTION.value["name"]: Module(**Modules.CODE_INSPECTION.value),
    Modules.CONVERSATIONAL_CHAT.value["name"]: Module(**Modules.CONVERSATIONAL_CHAT.value)

}


class AddRemoveUserDTO(BaseModel):
    """
    Model for adding or removing users from a discussion.
    """
    user_id: str = Field(..., description="The unique identifier for the user (Cognito Username or sub).")
    
class UserUpdate(BaseModel):
    Name: Optional[str] = ""
    Designation: Optional[str] = ""
    Department: Optional[str] = ""


    @validator('*', pre=True)  
    def check_if_empty(cls, value):
        if value == '':
            return None  # Treat empty strings as None
        return value
    
class ProjectMember(BaseModel):
    user_id: str = Field(..., description="Unique identifier for the user")
    role: str = Field(..., description="Role of the user in the project (e.g., 'developer', 'manager', 'qa')")
    responsibilities: Optional[List[str]] = Field(
        default=[], description="List of responsibilities for this user in the project"
    )

class ProjectUpdate(BaseModel):
    members: List[ProjectMember] = Field(..., description="List of members to add to the project")

class TaskAssignment(BaseModel):
    node_id: int = Field(..., description="ID of the WorkItem node")
    user_id: str = Field(..., description="Username of the User node")

class TaskAssignmentResponse(BaseModel):
    success: bool = Field(..., description="Indicates whether the assignment was successful")
    message: str = Field(..., description="Message providing additional details about the assignment")



#Activity Tracking Model
class ProjectUsageModel(BaseModel):
    user_id: str = Field(..., description="Unique identifier of the user")
    project_id: int = Field(..., description="ID of the project accessed")
    timestamp: datetime = Field(default=generate_timestamp(), description="Timestamp of the last access")
