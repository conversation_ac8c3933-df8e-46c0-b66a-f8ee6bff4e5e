from pydantic import BaseModel, ConfigDict
from enum import Enum
from typing import List, Dict, Optional
from datetime import datetime
from uuid import uuid4
from app.utils.datetime_utils import generate_timestamp

class BaseStepRequest(BaseModel):
    model_config = ConfigDict(extra='allow')
    node_type: str
    node_id: int
    step_name: str
    discussion_id: int
    discussion_type: Optional[str] = None

class ExecuteStepRequest(BaseStepRequest):
    pass


class FileAttachment(BaseModel):
    model_config = ConfigDict(extra='allow')
    
    file_name: str
    file_type: str
    file_size: int
    
    extracted_content: Optional[str] = None
    image_url: Optional[str] = None

class RepeatStepRequest(BaseStepRequest):
    usercomment: Optional[str] = None
    modification_index: Optional[int] = None
    file_attachments: Optional[List[FileAttachment]] = None

class StartProjectInit(BaseModel):
    usercomment: str

class CommentDTO(BaseModel):
    user_id: str
    comment: str
    created_at: str = generate_timestamp()
    edited: bool
    
class ModificationStatus(str, Enum):
    IDLE = "idle"
    PENDING = "pending"
    REJECTED = "rejected"
    MERGED = "merged"
    
    def __str__(self):
        return self.value.lower()

    def __repr__(self):
        return f"{self.__class__.__name__}.{self.name}"

class ModificationFeedbackDTO(BaseModel):
    status: ModificationStatus
    modified_by: str
    approver_id: Optional[str] = None
    created_at: str = generate_timestamp()