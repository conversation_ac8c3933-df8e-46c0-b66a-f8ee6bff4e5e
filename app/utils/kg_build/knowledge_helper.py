import os
import base64
import shlex

from app.utils.kg_build.knowledge import Knowledge ,KnowledgeHelpers, KnowledgeReporter


class Reporter(KnowledgeReporter):
    def __init__(self):
        self.ready = False
        pass

    def send_agent_message(self, message):
        if 'Knowledge creation complete' in message:
            self.ready = True
        pass

    def cost_update_callback(self, all_costs, total_cost ):
        pass

    def is_ready(self):
        return self.ready

class Helpers(KnowledgeHelpers):
    def __init__(self, base_path):
        self.base_path = base_path

    def execute_cmd(self, cmd):
        import subprocess
        try:
            result = subprocess.run(cmd, capture_output=True, shell=True, text=True)
            return result.stdout, result.returncode
        except Exception as e:
            print(f"Error: cmd {cmd} had exception {e}")
        return None, None

    def check_file_exists(self, filename):
        exists = False
        if os.path.exists(filename):
            exists = True
        return exists
    
    def read_file(self, filename):
        contents = None
        try:
            with open(filename, 'r') as file:
                contents = file.read()
        except FileNotFoundError:
            pass
        except UnicodeDecodeError:
            pass
        return contents
    
    def write_file(self, filename, content):
        escaped_path = shlex.quote(filename)
        encoded_new_content = base64.b64encode(content.encode('utf-8')).decode('utf-8')
        cmd = f"mkdir -p $(dirname {escaped_path}) && echo '{encoded_new_content}' | base64 -d > {escaped_path}"
        output, returncode = self.execute_cmd(cmd)
        return returncode

    def list_directory(self, directory):
        list = []
        try:
            list = os.listdir(directory)
        except FileNotFoundError:
            pass
        except NotADirectoryError:
            pass
        return list

class Knowledge_Helper:
    def __init__(self, _id, reporter, base_path, code_bases = None, user_id=None, project_id=None, service="github"):
        configuration = {
            "base_path" : base_path,
            "model" : "gpt-4.1-nano-2025-04-14",
            "timeout": 120,
            "cost_tracer" : None,
            "reporter" : reporter,
            "helpers" : Helpers(base_path),
            "user_id": user_id, 
            "project_id": project_id,
            "service": service
        }
        if code_bases:
            configuration['code_bases'] = code_bases

        self.knowledge = Knowledge.getKnowledge(configuration, _id)

    @classmethod
    def cleanup(cls, _id):
        Knowledge._instances.pop(_id, None)