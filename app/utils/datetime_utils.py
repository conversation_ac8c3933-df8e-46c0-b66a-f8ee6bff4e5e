from datetime import datetime, timezone

# PUBLIC_INTERFACE
def generate_timestamp():
    """Generate a current timestamp in UTC.
    
    Returns:
        str: ISO formatted UTC timestamp with timezone information
    """
    return datetime.now(timezone.utc).isoformat()

# PUBLIC_INTERFACE
def to_utc(dt):
    """Convert a datetime object to UTC.
    
    Args:
        dt (datetime): The datetime object to convert
        
    Returns:
        datetime: UTC-aware datetime object
    """
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)
    return dt.astimezone(timezone.utc)

# PUBLIC_INTERFACE
def from_isoformat(iso_str):
    """Parse an ISO format string into a UTC datetime object.
    
    Args:
        iso_str (str): ISO format datetime string
        
    Returns:
        datetime: UTC-aware datetime object
    """
    dt = datetime.fromisoformat(iso_str)
    return to_utc(dt)