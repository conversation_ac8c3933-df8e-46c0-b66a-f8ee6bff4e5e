

import re
from app.core.Settings import settings, Settings
from urllib.parse import urlparse, urlunparse

def change_url_port(url, new_port):
    """
    Changes the port in a URL if a port is present, maintaining the exact string format.
    
    Args:
        url (str): The original URL
        new_port (int or str): The new port number to set
        
    Returns:
        str: The URL with the updated port as a string
    """
    try:
        # Parse the URL
        parsed_url = urlparse(url)
        
        # Check if the URL has a port
        if ':' in parsed_url.netloc:
            # Split the host and port
            host = parsed_url.netloc.split(':')[0]
            
            # Construct the new URL with the new port
            if parsed_url.path or parsed_url.params or parsed_url.query or parsed_url.fragment:
                # Include the rest of the URL if it exists
                result = f"{parsed_url.scheme}:{'//' if parsed_url.scheme.endswith('://') else '/'}{host}:{new_port}{parsed_url.path}{('?' + parsed_url.query) if parsed_url.query else ''}{('#' + parsed_url.fragment) if parsed_url.fragment else ''}"
            else:
                # Just the base URL with new port
                result = f"{parsed_url.scheme}:{'//' if parsed_url.scheme.endswith('://') else '/'}{host}:{new_port}"
            
            return result
        else:
            # No port found, return the original URL
            return url
    except Exception as e:
        print(f"Error changing URL port: {e}")
        return None


def change_host(url, new_host, upgrade_to_https=False):
    """
    Changes the host of a URL and optionally upgrades the protocol to HTTPS.
    
    Args:
        url (str): The original URL
        new_host (str): The new host to set
        upgrade_to_https (bool, optional): Whether to upgrade the URL to HTTPS. Defaults to False.
        
    Returns:
        str: The URL with the updated host as a string, or None if an error occurs
    """
    try:
        # Parse the URL
        parsed_url = urlparse(url)
        
        # Determine the scheme
        scheme = "https" if upgrade_to_https else parsed_url.scheme
        
        # Extract port if it exists in the original netloc
        port = ""
        if ':' in parsed_url.netloc:
            _, port_part = parsed_url.netloc.split(':', 1)
            port = f":{port_part}"
        
        # Extract port if it exists in the new_host
        if ':' in new_host:
            # If new_host already includes a port, use it and ignore the original port
            port = ""
        
        # Construct the new netloc with the new host and original port if applicable
        new_netloc = f"{new_host}{port}" if port and ':' not in new_host else new_host
        
        # Create the new URL parts
        new_parts = (
            scheme,
            new_netloc,
            parsed_url.path,
            parsed_url.params,
            parsed_url.query,
            parsed_url.fragment
        )
        
        # Construct and return the new URL
        print("New parts", new_parts)
        print("Returning new url", urlunparse(new_parts))
        return urlunparse(new_parts)
        
    except Exception as e:
        print(f"Error changing URL host: {e}")
        return None


def find_host(url):
    """
    Extracts the host from a URL, without the port.
    
    Args:
        url (str): The URL to extract the host from
        
    Returns:
        str: The host part of the URL, or None if an error occurs
    """
    try:
        # Parse the URL
        parsed_url = urlparse(url)
        
        # Extract the host part (without port if present)
        host = parsed_url.netloc.split(':')[0] if ':' in parsed_url.netloc else parsed_url.netloc
        print("Host extracted", host)
        return host
        
    except Exception as e:
        print(f"Error extracting host from URL: {e}")
        return None
 
 
def safe_name(name):
    """Converts a name to a safe casefold removing special characters and whitespace."""
    return re.sub(r'[^\w\s-]', '', name).strip().casefold().replace(' ', '-')

def name_to_slug(name):
    """Converts a name to a lowercase slug with dashes, removing special characters."""
    return re.sub(r'[^\w\s-]', '', name).strip().lower().replace(' ', '-')

def camel_to_snake(name):
    pattern = re.compile(r'(?<!^)(?=[A-Z])')
    return pattern.sub('_', name).upper()

def get_stage(settings:Settings = settings):
    stage = "dev"
    if settings.STAGE == "develop":
        stage = "dev"
    elif settings.STAGE == "prod":
        stage = "qa"
    elif settings.STAGE == "experimental":
        stage = "experimental"   
    elif settings.STAGE == "production":
        stage = "prod"
    else:
        stage = settings.STAGE or "dev"

    return stage
