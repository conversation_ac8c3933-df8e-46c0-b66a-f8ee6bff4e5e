import os
import git
from typing import Dict, Optional
from app.routes.scm_route import scm_manager
from app.utils.hash import decrypt_string
from git import Repo
import tempfile
import json
from pathlib import Path
from app.utils.code_generation_utils import get_codegeneration_path

from app.utils.node_utils import get_node_type
from app.utils.project_utils import name_to_slug
from fastapi import HTT<PERSON>Ex<PERSON>, Depends
from app.connection.establish_db_connection import get_node_db, NodeDB
from app.core.constants import GIT_IGNORE_CONTENT
import logging
from github import Github
from app.core.Settings import settings
from app.models.scm import ACCESS_TOKEN_PATH
import github
import asyncio
import uuid

logger = logging.getLogger(__name__)

def get_github_client():
    return Github(settings.GITHUB_ACCESS_TOKEN)

def ensure_git_safe_directory(repo_path):
    """
    Configure Git to trust the repository directory if ownership issues are detected.
    This addresses the 'dubious ownership' error that can occur when Git's
    ownership security checks fail.
    
    Args:
        repo_path (str): Path to the git repository
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        import subprocess
        # Add the directory to Git's safe.directory configuration
        cmd = ["git", "config", "--global", "--add", "safe.directory", repo_path]
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(f"Added repository to Git safe directories: {repo_path}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error adding repository to Git safe directories: {e}")
        print(f"Command stderr: {e.stderr}")
        return False
    except Exception as e:
        print(f"Unexpected error configuring Git safe directory: {e}")
        return False

async def create_repository_in_workspace(project_id: int, container_id: int ,db: NodeDB = Depends(get_node_db)):
    project = await db.get_node_by_id(project_id)
    project_details = project.get("properties")
    unique_id=uuid.uuid4().hex[:8]
    try:
        print("Creating repository in workspace")
        # Generate repository name based on project and container
        repository_name = name_to_slug(
            f"{project_details.get('Title', project_details.get('Name'))}"
        )
        repository_name = f'{repository_name}-{unique_id}'

        github_client = get_github_client()

        # Rename 'repo' to 'github_repo' to avoid confusion
        github_repo = github_client.get_user().create_repo(
            name=repository_name,
            private=False,
            auto_init=True,
            gitignore_template="Python"
        )
        
        # Create kavia-main branch from main branch
        try:
            main_branch = github_repo.get_branch("main")
            github_repo.create_git_ref(ref="refs/heads/kavia-main", sha=main_branch.commit.sha)
            
            # Set kavia-main as default branch
            github_repo.edit(default_branch="kavia-main")
            
        except Exception as e:
            print(f"Warning: Could not set kavia-main as default branch: {str(e)}")
            # Continue with the repository creation even if branch operations fail
        
        repository_metadata = {
            'service': 'github',
            'repositoryName': github_repo.name,
            'repositoryId': str(github_repo.id),
            'cloneUrlHttp': github_repo.clone_url,
            'cloneUrlSsh': github_repo.ssh_url,
            'organization': github_client.get_user().login,
            'access_token_path': ACCESS_TOKEN_PATH.KAVIA_MANANGED.value,
            'repositoryStatus': 'initialized'
        }

        gitignore = github_repo.get_contents(".gitignore")
        github_repo.update_file(
            path=".gitignore",
            message="Update .gitignore",
            content=GIT_IGNORE_CONTENT,
            sha=gitignore.sha,
            branch="kavia-main"
        )
        
        # Update project repositories
        project_repositories = json.loads(project_details.get("repositories", "{}"))
        project_repositories[str(container_id)] = repository_metadata
        await db.update_node_by_id(
            project_id,
            {"repositories": json.dumps(project_repositories)}
        )
        
        return repository_metadata
        
    except Exception as e:
        print(f"Error creating local repository: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create local repository: {str(e)}")
    
async def fork_repository_in_workspace(
    project_id: int, 
    container_id: int, 
    db: NodeDB = Depends(get_node_db)
):
    """
    Fork an existing repository into the workspace.
    
    Args:
        project_id: ID of the project
        container_id: ID of the container
        source_repo_url: URL of the repository to fork (e.g., 'owner/repo-name')
        db: Database dependency
        
    Returns:
        dict: Repository metadata of the forked repository
        
    Raises:
        HTTPException: If forking fails
    """
    try:
        # Fetch project and container details
        project, container = await asyncio.gather(
            db.get_node_by_id(project_id),
            db.get_node_by_id(container_id)
        )
        
        project_details = project.get("properties", {})
        container_details = container.get("properties", {})
        
        repo_info = project_details.get('repositories','{}')
        data = json.loads(repo_info)
        # Collect all cloneUrlHttp values
        clone_urls = [value["cloneUrlHttp"] for value in data.values()]
        if clone_urls :
            source_repo_url = clone_urls[0]
        else:
            raise HTTPException(status_code=400, detail="no source_repo_url found to fork")    
        print(f"Forking repository {source_repo_url} in workspace")
        
        # Parse source repository URL to extract owner and repo name
        source_owner, source_repo_name = _parse_github_url(source_repo_url)

        # Generate new repository name for the fork
        project_title = project_details.get('Title') or project_details.get('Name', 'project')
        fork_name = name_to_slug(f"{project_title}-{source_repo_name}")
        fork_name = f'{fork_name}-{container_id}'
        
        github_client = get_github_client()
        user = github_client.get_user()
        
        # Get the source repository
        source_repo = github_client.get_repo(f"{source_owner}/{source_repo_name}")
        
        # Fork the repository
        forked_repo = github_client.get_user().create_fork(source_repo)
        # forked_repo = user.create_fork(source_repo)
        
        # Rename the forked repository if needed
        if forked_repo.name != fork_name:
            forked_repo.edit(name=fork_name)
        
        # Prepare repository metadata
        repository_metadata = {
            'service': 'github',
            'repositoryName': forked_repo.name,
            'repositoryId': str(forked_repo.id),
            'cloneUrlHttp': forked_repo.clone_url,
            'cloneUrlSsh': forked_repo.ssh_url,
            'organization': user.login,
            'access_token_path': ACCESS_TOKEN_PATH.KAVIA_MANANGED.value,
            'repositoryStatus': 'forked',
            'sourceRepository': {
                'owner': source_owner,
                'name': source_repo_name,
                'url': source_repo.html_url
            },
            'forkedAt': forked_repo.created_at.isoformat()
        }
        
        # Update project repositories
        project_repositories = json.loads(project_details.get("repositories", "{}"))
        project_repositories[str(container_id)] = repository_metadata
        
        await db.update_node_by_id(
            project_id,
            {"repositories": json.dumps(project_repositories)}
        )
        
        print(f"Successfully forked repository: {forked_repo.full_name}")
        return repository_metadata
        
    except ValueError as ve:
        error_msg = f"Invalid repository URL format: {str(ve)}"
        raise HTTPException(status_code=400, detail=error_msg)
    except Exception as e:
        error_message = str(e).lower()
        # Determine appropriate HTTP status based on error message
        if "not found" in error_message or "404" in error_message:
            status_code = 404
            detail = f"Source repository not found: {source_repo_url}"
        elif "forbidden" in error_message or "403" in error_message:
            status_code = 403
            detail = "Access forbidden: Check repository permissions and API token"
        elif "unauthorized" in error_message or "401" in error_message:
            status_code = 401
            detail = "Unauthorized: Invalid or missing GitHub token"
        elif "rate limit" in error_message:
            status_code = 429
            detail = "GitHub API rate limit exceeded"
        else:
            status_code = 500
            detail = f"Failed to fork repository: {str(e)}"
        
        print(f"Error forking repository: {detail}")
        raise HTTPException(status_code=status_code, detail=detail)

def _parse_github_url(repo_url: str) -> tuple[str, str]:
    """
    Parse GitHub repository URL to extract owner and repository name.
    """
    # Clean the URL - remove .git suffix if present
    clean_url = repo_url.rstrip('/').rstrip('.git')  # This should remove .git
    
    if clean_url.startswith(('http://', 'https://')):
        # Extract from full URL: https://github.com/owner/repo
        parts = clean_url.split('/')
        if len(parts) >= 2 and 'github.com' in clean_url:
            owner = parts[-2]
            repo_name = parts[-1]
            
            # Double-check to ensure .git is removed
            if repo_name.endswith('.git'):
                repo_name = repo_name[:-4]
                
            return owner, repo_name
        else:
            raise ValueError(f"Invalid GitHub URL format: {repo_url}")
    elif '/' in clean_url:
        # Handle 'owner/repo' format
        parts = clean_url.split('/')
        if len(parts) == 2:
            owner = parts[0]
            repo_name = parts[1]
            
            # Double-check to ensure .git is removed
            if repo_name.endswith('.git'):
                repo_name = repo_name[:-4]
                
            return owner, repo_name
        else:
            raise ValueError(f"Invalid repository format: {repo_url}. Expected 'owner/repo'")
    else:
        raise ValueError(f"Invalid repository URL: {repo_url}")

def process_files(input_dir: str, stop_point: str, new_base: str):
    """
    Process files in the input directory by modifying the filename and content.
    The filename is modified by replacing the path up to the stop_point with new_base.
    The content is modified by replacing the path up to the stop_point with new_base.
    """
    # Walk through all files in the input directory
    for root, _, files in os.walk(input_dir):
        for file in files:
            file_path = os.path.join(root, file)
            
            try:
                # Read the file content
                with open(file_path, 'r') as f:
                    content = f.read()
                
                # Parse JSON content
                try:
                    data = json.loads(content)
                except json.JSONDecodeError:
                    print(f"Skipping {file}: Not a valid JSON file")
                    continue
                
                # Process filename modification
                print(f"Processing: {file}")
                old_filename = file
                
                # Find the index of stop_point in the filename
                stop_idx = old_filename.find(stop_point)
                if stop_idx == -1:
                    print(f"Stop point '{stop_point}' not found in {file}")
                    continue
                
                # Extract the part after stop_point
                remaining_path = old_filename[stop_idx:]
                
                # Create new filename with new_base
                new_filename = "_".join(new_base.split("/")) + remaining_path
                
                # Process content modification
                if 'filename' in data:
                    # Get the old path up to stop_point
                    old_path = data['filename']
                    stop_idx_content = old_path.rfind(stop_point)
                    
                    if stop_idx_content != -1:
                        # Replace the path up to stop_point with new_base
                        # Add a slash between new_base and the remaining path
                        if not new_base.endswith('/'):
                            new_base = new_base + '/'
                        data['filename'] = new_base + old_path[stop_idx_content:]
                
                # Create new file path
                new_file_path = os.path.join(root, new_filename)
                
                # Write modified content to new file
                with open(new_file_path, 'w') as f:
                    json.dump(data, f, indent=2)
                
                # Remove old file if new filename is different
                if new_file_path != file_path:
                    os.remove(file_path)
                
                print(f"Processed: {file} -> {new_filename}")
                
            except Exception as e:
                print(f"Error processing {file}: {str(e)}")
                
def setup_git_credentials(repo_path: str, access_token: str, remote_url: str):
    """Configure git credentials for the repository"""
    # First, add the repository to Git's safe directories
    # Import the ensure_git_safe_directory function if available, or define it inline
    try:
        ensure_git_safe_directory(repo_path)
    except Exception as e:
        print(f"Error ensuring Git safe directory: {e}")
        # Define the function inline if import fails
        import subprocess
        try:
            cmd = ["git", "config", "--global", "--add", "safe.directory", repo_path]
            subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(f"Added repository to Git safe directories: {repo_path}")
        except Exception as e:
            print(f"Error adding repository to Git safe directories: {e}")
    
    # Now continue with the original function
    repo = Repo(repo_path)
    
    # Set remote URL with embedded credentials
    if 'https://' in remote_url:
        # For HTTPS URLs, embed the token in the URL
        parsed_url = remote_url.replace('https://', '')
        new_url = f'https://oauth2:{access_token}@{parsed_url}'
        repo.git.remote('set-url', 'origin', new_url)
    else:
        # For SSH URLs, we might need to configure SSH keys
        # This would be handled by the system's SSH configuration
        pass

def clone_repository(repository_metadata: Dict, tenant_id: str, task_id: str = None) -> str:
    """
    Clone repository with proper authentication and optionally create a task-specific branch
    
    Args:
        repository_metadata: Repository details dictionary
        tenant_id: Tenant ID for SCM configuration lookup
        task_id: Optional task ID to create a task-specific branch
    
    Returns:
        str: Path to cloned repository
    """
    if not repository_metadata:
        raise Exception("Repository metadata is required")
    
    service = repository_metadata.get('service', '').lower()
    repo_name = repository_metadata.get('repositoryName')
    access_token = repository_metadata.get('access_token')
    if not access_token:
        encrypted_scm_id = repository_metadata.get('encrypted_scm_id')
        decrypted_scm_id = decrypt_string(encrypted_scm_id)
        config = scm_manager.get_configuration(tenant_id, scm_id=decrypted_scm_id)
        if not config:
            raise Exception("Failed to get SCM configuration")
        access_token = config.credentials.access_token
    
    if not all([service, repo_name, access_token]):
        raise Exception("Invalid repository metadata: missing required fields")

    # Generate clone path
    clone_path = os.path.join(get_codegeneration_path(), repo_name)
    
    # Return existing path if already cloned
    if os.path.exists(clone_path):
        # Update credentials for existing repo
        setup_git_credentials(clone_path, access_token, 
                            repository_metadata.get('cloneUrlHttp'))
        
        # If task_id is provided, create and checkout task-specific branch
        if task_id:
            try:
                repo = Repo(clone_path)
                task_branch_name = f"cga-{task_id}"
                
                # Check if the task branch already exists
                existing_branches = [ref.name.split('/')[-1] for ref in repo.refs if ref.name.startswith('refs/heads/')]
                
                if task_branch_name not in existing_branches:
                    # Ensure we're on kavia-main branch first
                    try:
                        repo.git.checkout('kavia-main')
                        print(f"Switched to kavia-main branch")
                    except Exception as e:
                        print(f"Warning: Could not switch to kavia-main branch: {str(e)}")
                        # Try to checkout main if kavia-main doesn't exist
                        try:
                            repo.git.checkout('main')
                            print(f"Switched to main branch as fallback")
                        except Exception as e2:
                            print(f"Warning: Could not switch to main branch either: {str(e2)}")
                    
                    # Create and checkout the task-specific branch
                    repo.git.checkout('-b', task_branch_name)
                    print(f"Created and switched to branch: {task_branch_name}")
                else:
                    # Branch exists, just checkout
                    repo.git.checkout(task_branch_name)
                    print(f"Switched to existing branch: {task_branch_name}")
                    
            except Exception as e:
                print(f"Warning: Could not create/checkout task branch: {str(e)}")
                # Continue without the task branch
        
        return clone_path
        
    try:
        repo = None
        if service == 'github':
            # Use HTTPS URL with token authentication
            clone_url = repository_metadata.get('cloneUrlHttp')
            if not clone_url:
                raise Exception("No HTTPS clone URL found")
                
            # Create a temporary config to use during clone
            with tempfile.NamedTemporaryFile(mode='w+', delete=False) as f:
                f.write(f'[credential]\n\thelper=store\n')
                git.Git().config('--global', 'credential.helper', f'store --file {f.name}')
                
            # Clone with token
            auth_url = clone_url.replace('https://', 
                                       f'https://oauth2:{access_token}@')
            repo = Repo.clone_from(auth_url, clone_path)
            setup_git_credentials(clone_path, access_token, clone_url)

        elif service == 'gitlab':
            clone_url = repository_metadata.get('cloneUrlHttp')
            if not clone_url:
                raise Exception("No HTTPS clone URL found")
                
            # For GitLab, we can use token in URL directly
            auth_url = clone_url.replace('https://', 
                                       f'https://oauth2:{access_token}@')
            repo = Repo.clone_from(auth_url, clone_path)
            setup_git_credentials(clone_path, access_token, clone_url)
            
        else:
            raise Exception(f"Unsupported repository service: {service}")
        
        # If task_id is provided, create and checkout task-specific branch
        if task_id and repo:
            try:
                task_branch_name = f"cga-{task_id}"
                
                # Ensure we're on kavia-main branch first
                try:
                    repo.git.checkout('kavia-main')
                    print(f"Switched to kavia-main branch")
                except Exception as e:
                    print(f"Warning: Could not switch to kavia-main branch: {str(e)}")
                    # Try to checkout main if kavia-main doesn't exist
                    try:
                        repo.git.checkout('main')
                        print(f"Switched to main branch as fallback")
                    except Exception as e2:
                        print(f"Warning: Could not switch to main branch either: {str(e2)}")
                
                # Create and checkout the task-specific branch
                repo.git.checkout('-b', task_branch_name)
                print(f"Created and switched to branch: {task_branch_name}")
                
            except Exception as e:
                print(f"Warning: Could not create task branch: {str(e)}")
                # Continue without the task branch
            
    except git.GitCommandError as e:
        if "Permission denied" in str(e):
            raise Exception(f"Authentication failed when cloning repository") from e
        else:
            raise Exception(f"Error cloning repository: {e}") from e
            
    if not os.path.exists(clone_path):
        raise Exception("Repository cloning failed: Clone path does not exist")
        
    return clone_path