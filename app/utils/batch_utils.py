import json
from app.connection.establish_db_connection import get_mongo_db
from app.core.constants import TASKS_COLLECTION_NAME as tasks_collection_name, TaskStatus

db = get_mongo_db().db

def get_detailed_report(task_id):
    db_record = db[tasks_collection_name].find_one({ '_id': task_id},{  'terminal_output': 0 })
    if not db_record:
        return {}
    try:
        context = json.loads(db_record.get('request_context', "{}"))
    except:
        context = {}
    insigths = process_context(context, task_id)
    

    db_record['context'] = context
    db_record['insights'] = insigths
    return db_record
    

def process_context(context, task_id):
    try:
        task_count_map = {
            "total": 0,
            "completed": 0,
            "failed": 0,
            "in-progress": 0,
            "to-do": 0
            
        }
        task_list = context.get('task_list')   
        if not task_list:
            return task_count_map
            
        total_tasks = len(task_list)
        task_count_map["total"] = total_tasks
        if total_tasks == 0:
            return task_count_map
        
        for task in task_list:
            if task_count_map.get(task.get('status')) is None:
                task_count_map[task.get('status')] = 1
            task_count_map[task.get('status')] += 1 
        if task_count_map["completed"] == 0:
            percentage = 0
        else:     
            percentage = (task_count_map["completed"] / total_tasks) * 100

        if task_count_map["completed"] == total_tasks:
            db[tasks_collection_name].update_one({ '_id': task_id }, { '$set': { 'task_status' : TaskStatus.COMPLETE } })
    
       
        return task_count_map
    except Exception as e:
        print(e)
        return None
