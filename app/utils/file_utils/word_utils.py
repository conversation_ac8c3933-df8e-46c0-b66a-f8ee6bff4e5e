import io
from docx import Document
import subprocess
import tempfile
import os

try:
    import docx2txt
    DOCX2TXT_AVAILABLE = True
except ImportError:
    DOCX2TXT_AVAILABLE = False

def extract_text_from_docx(content):
    """
    Extract text from a .docx file using python-docx library with docx2txt fallback

    Args:
        content (bytes): The binary content of the .docx file

    Returns:
        str: Extracted text from the document
    """
    try:
        # First try with python-docx (more comprehensive)
        try:
            # Create a BytesIO object from the content
            doc_stream = io.BytesIO(content)

            # Load the document
            document = Document(doc_stream)

            # Extract text from all paragraphs
            text_content = []

            # Extract text from paragraphs
            for paragraph in document.paragraphs:
                if paragraph.text.strip():  # Only add non-empty paragraphs
                    text_content.append(paragraph.text)

            # Extract text from tables
            for table in document.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        text_content.append(" | ".join(row_text))

            # Join all text with newlines
            extracted_text = "\n".join(text_content)

            return extracted_text.strip()

        except Exception as docx_error:
            print(f"python-docx failed: {str(docx_error)}, trying docx2txt fallback")

            # Fallback to docx2txt if available
            if DOCX2TXT_AVAILABLE:
                try:
                    # Create a temporary file for docx2txt
                    with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
                        temp_file.write(content)
                        temp_file_path = temp_file.name

                    try:
                        # Extract text using docx2txt
                        text = docx2txt.process(temp_file_path)
                        return text.strip() if text else None
                    finally:
                        # Clean up temporary file
                        try:
                            os.unlink(temp_file_path)
                        except OSError:
                            pass

                except Exception as docx2txt_error:
                    print(f"docx2txt fallback failed: {str(docx2txt_error)}")

            # If both methods fail, re-raise the original error
            raise docx_error

    except Exception as e:
        print(f"Error extracting text from DOCX: {str(e)}")
        return None


def extract_text_from_doc(content):
    """
    Extract text from a .doc file using antiword or catdoc
    
    Args:
        content (bytes): The binary content of the .doc file
        
    Returns:
        str: Extracted text from the document
    """
    try:
        # Create a temporary file to store the .doc content
        with tempfile.NamedTemporaryFile(suffix='.doc', delete=False) as temp_file:
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            # Try using antiword first (more reliable for .doc files)
            try:
                result = subprocess.run(
                    ['antiword', temp_file_path],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                
                if result.returncode == 0:
                    return result.stdout.strip()
                else:
                    print(f"Antiword failed with return code {result.returncode}: {result.stderr}")
                    
            except (subprocess.TimeoutExpired, FileNotFoundError) as e:
                print(f"Antiword not available or timed out: {str(e)}")
            
            # Fallback to catdoc
            try:
                result = subprocess.run(
                    ['catdoc', temp_file_path],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                
                if result.returncode == 0:
                    return result.stdout.strip()
                else:
                    print(f"Catdoc failed with return code {result.returncode}: {result.stderr}")
                    
            except (subprocess.TimeoutExpired, FileNotFoundError) as e:
                print(f"Catdoc not available or timed out: {str(e)}")
            
            # Try docx2txt as a fallback (sometimes works for .doc files)
            if DOCX2TXT_AVAILABLE:
                try:
                    text = docx2txt.process(temp_file_path)
                    if text and text.strip():
                        return text.strip()
                except Exception as docx2txt_error:
                    print(f"docx2txt fallback failed: {str(docx2txt_error)}")

            # If docx2txt fails, try python-docx as a last resort
            # (it might work for some .doc files that are actually .docx)
            try:
                return extract_text_from_docx(content)
            except Exception as docx_error:
                print(f"Python-docx fallback failed: {str(docx_error)}")

            # If all methods fail, return an error message
            return "Error: Unable to extract text from .doc file. Please ensure antiword or catdoc is installed, or convert the file to .docx format."
            
        finally:
            # Clean up the temporary file
            try:
                os.unlink(temp_file_path)
            except OSError:
                pass
                
    except Exception as e:
        print(f"Error extracting text from DOC: {str(e)}")
        return None


def extract_text_from_word_document(content, file_extension):
    """
    Extract text from Word documents (.doc or .docx)
    
    Args:
        content (bytes): The binary content of the Word document
        file_extension (str): The file extension ('doc' or 'docx')
        
    Returns:
        str: Extracted text from the document
    """
    if file_extension.lower() == 'docx':
        return extract_text_from_docx(content)
    elif file_extension.lower() == 'doc':
        return extract_text_from_doc(content)
    else:
        raise ValueError(f"Unsupported Word document format: {file_extension}")
