from cachetools import cached, TTLCache
from functools import wraps

def async_ttl_cache(ttl=0.1, maxsize=128):
    cache = TTLCache(maxsize=maxsize, ttl=ttl)
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            key = str(args) + str(kwargs)
            if key in cache:
                return cache[key]
            result = await func(*args, **kwargs)
            cache[key] = result
            return result
        return wrapper
    return decorator