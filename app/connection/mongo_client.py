# app/connection/mongo_client.py
from typing import Any
import logging
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError, InvalidURI
import certifi
from app.core.Settings import settings
from app.connection.tenant_middleware import get_tenant_id, get_user_id, KAVIA_ROOT_DB_NAME
from app.utils.b2c_utils import get_collection_name

Database = None
db_client: MongoClient = None
ca = certifi.where()
logger = logging.getLogger(__name__)

class TenantBasedDatabase:
    def __init__(self, db):
        self._db = db
        
    def __getitem__(self, collection_name):
        # Check if the tenant is a B2C client
        if get_tenant_id() == settings.KAVIA_B2C_CLIENT_ID and self._db.name != KAVIA_ROOT_DB_NAME:
            user_id = get_user_id()
            # Only prefix the collection name if it doesn't already start with the user ID
            if not collection_name.startswith(f"{user_id}_"):
                modified_collection_name = f"{user_id}_{collection_name}"
                return self._db[modified_collection_name]
        # For regular tenants or collections that already have the prefix, use the collection name as is
        return self._db[collection_name]
    
    # Forward all other attributes/methods to the underlying database object
    def __getattr__(self, name):
        return getattr(self._db, name)

class TenantAwareMongoClient(MongoClient):
    def __getitem__(self, db_name):
        # Get the database from the original client
        db = super().__getitem__(db_name)
        # Wrap it with our custom TenantBasedDatabase class
        return TenantBasedDatabase(db)

def connect_db():
    """Create database connection with tenant awareness."""
    print(" ++ initialising mongo_client new connection ++")
    global db_client
    
    # Try multiple connection methods in sequence until one works
    connection_attempts = [
        # First attempt: Basic connection with minimal options
        {
            "connectTimeoutMS": 30000,
            "socketTimeoutMS": 30000,
            "serverSelectionTimeoutMS": 30000
        },
        
        # Second attempt: With TLS but no certificate validation
        {
            "tls": True,
            "tlsAllowInvalidCertificates": True,
            "connectTimeoutMS": 30000,
            "socketTimeoutMS": 30000,
            "serverSelectionTimeoutMS": 30000
        },
        
        # Third attempt: TLS with certificate validation
        {
            "tls": True,
            "tlsCAFile": ca,
            "connectTimeoutMS": 30000,
            "socketTimeoutMS": 30000,
            "serverSelectionTimeoutMS": 30000
        },
        
        # Fourth attempt: TLS with specific options
        {
            "tls": True,
            "tlsInsecure": True,
            "connectTimeoutMS": 30000,
            "socketTimeoutMS": 30000,
            "serverSelectionTimeoutMS": 30000
        }
    ]
    
    last_error = None
    
    # Try each connection method until one works
    for i, options in enumerate(connection_attempts):
        try:
            logger.info(f"Trying MongoDB connection attempt #{i+1}")
            db_client = TenantAwareMongoClient(settings.MONGO_CONNECTION_URI, **options)
            
            # Test the connection
            db_client.admin.command('ping')
            logger.info(f"MongoDB connection successful on attempt #{i+1}")
            
            return db_client
        except (ConnectionFailure, ServerSelectionTimeoutError, InvalidURI) as e:
            last_error = e
            logger.warning(f"MongoDB connection attempt #{i+1} failed: {str(e)}")
            continue
    
    # If all attempts fail, log the last error and raise it
    if last_error:
        logger.error(f"All MongoDB connection attempts failed. Last error: {str(last_error)}")
        raise last_error
    
    raise ConnectionFailure("Failed to connect to MongoDB after all attempts")

def get_db_client():
    """Return database client instance."""
    global db_client
    if db_client is None: 
        return connect_db()
    
    # Test if the connection is still valid
    try:
        db_client.admin.command('ping')
        return db_client
    except Exception as e:
        logger.warning(f"MongoDB connection test failed, reconnecting: {str(e)}")
        return connect_db()

def close_db():
    """Close database connection."""
    global db_client
    if db_client:
        db_client.close()
        db_client = None
        logger.info("MongoDB connection closed")

def get_db(db_name: str = settings.MONGO_DB_NAME):
    """Return database instance with tenant awareness."""
    client = get_db_client()
    # This will return a TenantBasedDatabase instance
    return client[db_name]