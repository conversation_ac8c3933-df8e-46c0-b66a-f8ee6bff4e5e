from app.discussions.discussion import Discussion
from app.celery_app import user_context

class DiscussionFactory:
    registry = {}

    @classmethod
    def register(cls, discussion_type, constructor, node_type=None, multi_level_discussion=False):
        key = (discussion_type, node_type) if node_type else (discussion_type,)
        cls.registry[key] = {'class': constructor, 'multi_level_discussion': multi_level_discussion}
    
    @classmethod
    async def create_discussion(cls, discussion_type, current_user=user_context.get(), node_type=None, node_id=None, discussion_node_id=None, config=None , title=None, description=None, logging_context=None, semaphore=None, levels=0, invocation_type='interactive_config', supervisor=None):
        # Look up constructor
        print("user id in discussion", current_user)
        if (discussion_type, node_type) in cls.registry:
            constructor = cls.registry[(discussion_type, node_type)]['class']
            multi_level_discussion = cls.registry[(discussion_type, node_type)]['multi_level_discussion']
        elif (discussion_type,) in cls.registry:
            constructor = cls.registry[(discussion_type,)]['class']
            multi_level_discussion = cls.registry[(discussion_type,)]['multi_level_discussion']
        else:
            constructor = Discussion
            multi_level_discussion = False

        if multi_level_discussion:
            discussion =  constructor(discussion_type, node_id, discussion_node_id, title, description, logging_context, semaphore, levels)
        else:
            discussion = constructor(discussion_type, node_id, discussion_node_id, title, description)
        discussion.set_invocation_type(invocation_type)
        discussion.set_current_user(current_user)
        if supervisor:  # Set supervisor if provided
            discussion.set_supervisor(supervisor)
        await discussion.async_initialize()
        return discussion