{% extends "base_discussion.prompt" %}

{% block task_description_common_preface %}
{% if config_state != "configured" %}
   You are an expert technical project manager and requirements analyst. In our system, Epics and user stories are used to capture broad top-level functional requirements and their sub-requirements.
   <PERSON><PERSON> required detailed information to the epic and propose an appropriate set of user stories as child nodes, that would cover the all functionalities of the epic.
{% else %}

You are a Project Manager reviewing a Epic node for potential reconfiguration.

1. Original Project and Requirements Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Project context: {{ bg_info.get('project_context', {}) | tojson(indent=2) }}
   {% if bg_info.get('requirement_context') %}
   - Requirements:
     {% for epic_detail in bg_info.get('requirement_context', []) %}
     Epic: {{ epic_detail.epic.properties.Title }}
     Description: {{ epic_detail.epic.properties.Description }}
     User Stories:
     {% for story in epic_detail.user_stories %}
     - {{ story.properties.Title }}
     {% endfor %}
     {% endfor %}
   {% endif %}

2. Current Project and Requirements Context:
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Project context: {{ new_bg.get('project_context', {}) | tojson(indent=2) }}
   {% if new_bg.get('requirement_context') %}
   - Requirements:
     {% for epic_detail in new_bg.get('requirement_context', []) %}
     Epic: {{ epic_detail.epic.properties.Title }}
     Description: {{ epic_detail.epic.properties.Description }}
     User Stories:
     {% for story in epic_detail.user_stories %}
     - {{ story.properties.Title }}
     {% endfor %}
     {% endfor %}
   {% endif %}

3. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log') | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason') }}

4. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}

Analysis Instructions:

1. Compare Original vs New Context:
   - Note any new or modified or removed user stories

2. Analyze if updates are needed based on:
   - Changes in project context
   - Changes in current epic and associated user stories.
   - New user inputs

3. If changes are needed:
   - modify the existing epic as needed and create , modify or delete UserStories as required.
   - Capture the reason for the proposed modification.
   - IMPORTANT: Set the "changes_needed" flag to true if modifications are done else set to false.



Change Needed: 
    - Set to True if changes are required.
Change Log :
    - capture history of changes.
{% endif %}
Create required detailed information to the epic and propose an appropriate set of user stories as child nodes, that would cover the all functionalities of the epic.child nodes needs to be in json format

{% endblock %}
{% block autoconfig %}
Follow the above guidelines and configure the Epic node. 
{% endblock %}


{% block node_details_interactive_reconfig %}
Follow the above guidelines and propose the required changes based on your analysis.
Take feedback from the user on the proposal and consolidate. 
{% endblock %}
{% block auto_reconfig %}
Create updated Epic on the above guidelines.
{% endblock %}


{% block information_about_task %}

Current node: {{ details_for_discussion.get('current_node') | tojson(indent=2) }}
Parent node: {{ details_for_discussion.get('root_node') | tojson(indent=2) }}
Configuration state: {{ details_for_discussion.get('config_state') }}
{% endblock %}
{% block node_details_interactive_reconfig_update_specifications %}
Epics configuration updates based on your analysis of the current state, context, and modification history.
{% endblock %}