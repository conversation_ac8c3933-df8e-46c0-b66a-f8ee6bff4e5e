{# 
Mermaid Diagram Requirements - Include only when needed
Usage: {% include 'includes/mermaid_guidance.jinja2' %}
#}

** MERMAID DIAGRAM REQUIREMENTS:**
* Follow official Mermaid grammar for the chosen diagram type only
* *Never combine different diagram syntaxes in one block*
* **Do not** wrap Mermaid code in Markdown fences (```mermaid or ```)
* **No HTML** (<br>, <div>, etc.). For line breaks inside labels use the literal string \n
* **No Markdown** formatting (**bold**, *italic*, etc.) inside the diagram text
* Keep all brackets [], braces {}, and parentheses (), plus quotes " " / ' ', perfectly balanced
* Use plain ASCII; avoid characters that are not part of Mermaid syntax
* **Class assignments**: Use identifiers without spaces: `class MyContainer container` not `class My Container container`
* **Standard quotes only**: Use `Node["text"]` not `Node[\"text\"]` - avoid unnecessary escaping

** DIAGRAM-SPECIFIC SYNTAX:**
* **Flowcharts/Graphs**: Use `-->` for arrows (double dash), not `->` or `--->`
* **Sequence Diagrams**: Use `->>` for messages, not `-->`
* **Class Diagrams**: Use proper relationship notation like `--|>`, `*--`, etc.
* **State Diagrams**: Use `-->` for transitions between states, NEVER `<--` (reverse arrows)
* **ER Diagrams**: Use proper relationship symbols like `||--o{`, `}o--||`

