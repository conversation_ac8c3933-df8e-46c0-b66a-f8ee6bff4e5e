{% extends "base_discussion.prompt" %}

You are an expert software architect.

{% block task_description_common_preface %}

{% if config_state != "configured" %}
As an expert software architect, configure the System Context based on the C4 model focusing on the overall system, its users, and external systems:

{% else %}
You are an expert system architect reviewing the System Context for potential reconfiguration.

1. Original Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Project context: {{ bg_info.get('project_context', {}) | tojson(indent=2) }}
   - Requirements context:
     {% set req_context = bg_info.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

2. Current Context (Changes to Review):
   {% set new_bg = details_for_discussion.get('new_background', {}) %}
   - Updated project context: {{ new_bg.get('project_context', {}) | tojson(indent=2) }}
   - Updated requirements context:
     {% set new_req_context = new_bg.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in new_req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in new_req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

3. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log', []) | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason', '') }}

{% endif %}

GUIDELINES:

1. Review Project Details and Requirements:
   - Project Context: {{ details_for_discussion.get('project_details') | tojson(indent=2) }}
   - Requirements Context:
     - Functional Requirements:
       {% for req in details_for_discussion.get('requirements_context', {}).get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in details_for_discussion.get('requirements_context', {}).get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

2. System Overview:
   - Provide detailed description of the system's purpose and main functionalities
   - Define system boundaries and scope

3. Users and External Systems:
   - Identify and describe all users/actors of the system
   - Identify external systems that interact with this system 
   

4. For each external system:
     * Create a container with ContainerType "external" as child node of system context
     * Provide name and description
     * Define its role and purpose

5. Generate C4 System Context Diagram:
   - Use Mermaid syntax
   - Show system boundaries
   - Include all users and external systems
   - Show high-level relationships

{% block mermaid_diagram_requirements %}
    {% include 'includes/mermaid_guidance.jinja2' %}
{% endblock %}

   IMPORTANT: Use this diagram as a structural reference only. Generate your diagram based on:
   - The identified users from the requirements
   - The external systems including databases from the system context
   - The actual relationships and interfaces discovered
   - The specific system name and purpose from the project context

   Generic System Context Diagram Reference:
```
graph TB
   %% Users/Actors
   subgraph Users[System Users]
       PrimaryUser["Primary User<br/>[Person]<br/><i>Main user of<br/>the system</i>"]
       AdminUser["Administrator<br/>[Person]<br/><i>System admin and<br/>configuration</i>"]
       SecondaryUser["Secondary User<br/>[Person]<br/><i>Supporting user<br/>role</i>"]
   end

   %% Main System
   subgraph MainSystem[Core System]
       System["System Name<br/>[System]<br/><i>Core system purpose<br/>and main capabilities</i>"]
   end

   %% External Systems including Databases
   subgraph ExternalSystems[External Systems]
       Database["Primary Database<br/>[System_Ext]<br/><i>Main data storage</i>"]
       Auth["Authentication Service<br/>[System_Ext]<br/><i>User authentication<br/>and authorization</i>"]
       Integration["Integration Service<br/>[System_Ext]<br/><i>Third-party<br/>integrations</i>"]
       Cache["Cache Service<br/>[System_Ext]<br/><i>Data caching</i>"]
   end

   %% Relationships
   PrimaryUser -->|"Primary interactions<br/>Protocol"| System
   AdminUser -->|"Admin operations<br/>Protocol"| System
   SecondaryUser -->|"Support operations<br/>Protocol"| System
   
   System -->|"Data operations<br/>Protocol"| Database
   System -->|"Authentication<br/>Protocol"| Auth
   System -->|"Integration<br/>Protocol"| Integration
   System -->|"Caching<br/>Protocol"| Cache

   %% Styling
   classDef system fill:#1168bd,stroke:#0b4884,color:#ffffff
   classDef person fill:#08427b,stroke:#052e56,color:#ffffff
   classDef external fill:#666666,stroke:#0b4884,color:#ffffff
   classDef boundary fill:none,stroke:#cccccc,stroke-dasharray:4

   class System system
   class PrimaryUser,AdminUser,SecondaryUser person
   class Database,Auth,Integration,Cache external
   class Users,ExternalSystems,MainSystem boundary
```

Note: When generating the diagram:
1. Replace generic names with actual system/user/external system names
2. Update descriptions to match actual roles and purposes
3. Use appropriate protocols in relationships
4. Include all identified external systems and databases 
5. Show actual system boundaries based on the context

Change Needed: 
   - Set to False if changes are not required

Change Log:
   - Capture history of changes
{% endblock %}

{% block autoconfig %}
Create a comprehensive C4 Model System Context focusing on the system's purpose, users, and external systems.
{% endblock %}

{% block information_about_task %}
{{ super() }}
    Existing interactions: {{ details_for_discussion.get('existing_interactions') | tojson(indent=2) }}
    Users and External Systems: 
    {{ details_for_discussion.get('external_systems') | tojson(indent=2) }}
    {{ details_for_discussion.get('users') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
{{ super() }}
    Project Details: {{ details_for_discussion.get('project_details') }} 
    Architectural Requirements: {{ details_for_discussion.get('architectural_requirements', '') }}
    Functional Requirements: {{ details_for_discussion.get('functional_requirements', '') }}
    Users and External Systems: 
    {{ details_for_discussion.get('external_systems') | tojson(indent=2) }}
    {{ details_for_discussion.get('users') | tojson(indent=2) }}
{% endblock %} 