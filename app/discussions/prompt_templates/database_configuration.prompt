{% extends "base_discussion.prompt" %}

You are an expert database architect.

{% block task_description_common_preface %}
{% if config_state != "configured" %}
As an expert database architect, configure database integration for the {{ details_for_discussion.container_details.title }} container.

## Container Analysis

**Container Details:**
- Title: {{ details_for_discussion.container_details.title }}
- Description: {{ details_for_discussion.container_details.description }}
- Type: {{ details_for_discussion.container_details.container_type }}
- Has Database: {{ details_for_discussion.container_details.has_database }}

**Available External Systems:**
{% for container in details_for_discussion.external_containers %}
- {{ container.properties.Title }} ({{ container.properties.ContainerType }})
{% endfor %}

**Existing Databases:**
{% for db in details_for_discussion.existing_databases %}
- {{ db.properties.Title }} ({{ db.properties.DatabaseType }})
{% endfor %}

**Functional Requirements:**
{% for req in details_for_discussion.functional_requirements %}
- {{ req.properties.Title }}: {{ req.properties.Description }}
{% endfor %}

## Database Configuration Requirements

### 1. Database Design

Based on the container's requirements, create Databases as child nodes of this container and configure them following the below guidelines:

- **Database Selection**: Choose appropriate database types
- **Schema Design**: Define entities, relationships, and constraints
- **Connection Configuration**: Set up secure database connections
- **Performance Optimization**: Configure indexing and query optimization

### 2. Entity-Relationship Design

Follow standard database design principles:

- **Entity Identification**: From functional requirements and container purpose
- **Attribute Definition**: Data types, constraints, and validation rules
- **Relationship Modeling**: Primary keys, foreign keys, and cardinality
- **Normalization**: Appropriate normalization level for performance vs. maintainability

### 3. Schema Standards

Use industry-standard schema definition formats:

- **JSON Schema**: For entity definitions and validation
- **SQL DDL**: For relational database schema
- **OpenAPI**: For database API specifications
- **Mermaid ER Diagrams**: For visual relationship representation

### 4. Interface Generation

Create database access interface as child node:

- **REST API**: Standard CRUD operations
- **Query Interface**: Complex queries and search
- **Security**: Authentication and authorization
- **Documentation**: API specifications and examples

{% else %}

You are an expert database architect reviewing the database configuration for potential reconfiguration.

Current Configuration Context:

1. Existing State:
   - Current configuration: {{ details_for_discussion.get('original_node') | tojson(indent=2) }}
   - Configuration state: {{ details_for_discussion.get('config_state') }}

2. Original Context:
   {% set bg_info = details_for_discussion.get('background_info', {}) %}
   - Project context: {{ bg_info.get('project_context', {}) | tojson(indent=2) }}
   - Container context: 
     - Parent Container: {{ bg_info.get('container_context', {}).get('container') | tojson(indent=2) }}
     - Database Details: {{ bg_info.get('container_context', {}).get('database_details') | tojson(indent=2) }}
   - Requirements context:
     {% set req_context = bg_info.get('requirements_context', {}) %}
     - Functional Requirements:
       {% for req in req_context.get('functional_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}
     - Non-Functional Requirements:
       {% for req in req_context.get('architectural_requirements', []) %}
       * {{ req.properties.Title }}: {{ req.properties.Description }}
       {% endfor %}

3. Change History:
   - Previous changes: {{ details_for_discussion.get('change_log') | tojson(indent=2) }}
   - Last change reason: {{ details_for_discussion.get('change_reason') }}

4. User Interaction History:
   - Previous user inputs: {{ details_for_discussion.get('user_interaction').get('input') | tojson(indent=2) }}

Based on the current configuration and context:

1. Compare original and current contexts
2. Analyze if updates are needed based on:
   - Changes in container requirements
   - New functional requirements
   - Performance optimization needs
   - Security requirements
   - Integration requirements
3. If changes are needed:
   - Modify the database configuration as needed
   - Update schema and interface specifications
   - Capture the reason for the proposed modification
   - IMPORTANT: Set the "changes_needed" flag to true if modifications are done
4. If no changes needed:
   - Explain why current configuration remains valid
   - Reference specific requirements that support this

{% endif %}

Change Needed: 
    - Set to True if changes are required.

Change Log:
    - Capture history of changes.

{% endblock %}

{% block autoconfig %}
Based on the container analysis, automatically generate appropriate database configuration including:
- Database type selection
- Schema design
- Connection configuration
- Performance optimization settings
- Security configurations
- Interface specifications
{% endblock %}

{% block auto_reconfig %}
Analyze the current database configuration and update based on:
- Changes in container requirements
- New functional requirements
- Performance optimization needs
- Security requirements
- Integration requirements
{% endblock %}

{% block node_details_interactive_reconfig %}
Follow the above guidelines and propose the required changes based on your analysis.
Take feedback from the user on the proposal and consolidate. Make sure to capture all the changes in new child nodes or modified child nodes if the existing nodes do not include them.
{% endblock %}

{% block node_details_interactive_reconfig_update_specifications %}
Database configuration updates based on your analysis of the current state, requirements, and modification history. Focus on:
- Database type and schema design
- Connection and security settings
- Performance optimization
- Interface specifications
{% endblock %}

{% block information_about_task %}
{{ super() }}
Container Details: {{ details_for_discussion.get('container_details') | tojson(indent=2) }}
External Systems: {{ details_for_discussion.get('external_containers') | tojson(indent=2) }}
Existing Databases: {{ details_for_discussion.get('existing_databases') | tojson(indent=2) }}
Functional Requirements: {{ details_for_discussion.get('functional_requirements') | tojson(indent=2) }}
{% endblock %}

{% block background_information %}
{{ super() }}
Project Details: {{ details_for_discussion.get('project_details') | tojson(indent=2) }}
System Context: {{ details_for_discussion.get('system_context') | tojson(indent=2) }}
{% endblock %}

