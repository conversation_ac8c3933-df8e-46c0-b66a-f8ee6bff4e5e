from app.discussions.discussion import Discussion
from app.discussions.discussion_factory import DiscussionFactory
from app.utils.node_version_manager import NodeVersionManager
from app.telemetry.logger_config import get_logger
from app.utils.datetime_utils import generate_timestamp

class DatabaseConfiguration(Discussion):
    def __init__(self, discussion_type, node_id=None, discussion_id=None, title=None, description=None):
        super().__init__(discussion_type, node_id, discussion_id, title, description, node_type="Container")
        self.template_name = "database_configuration.prompt"
        self.function_schema_type = "DatabaseConfiguration"
        self.version_manager = NodeVersionManager()
        self.update_logger = get_logger(__name__)

    def get_config_state(self, discussion_type, node_type, node):
        """Get configuration state for database configuration."""
        if not node or 'properties' not in node:
            return 'not_configured'
        return node['properties'].get('database_config_state', 'not_configured')

    def set_config_state(self, node_properties):
        """Set configuration state for database configuration."""
        node_properties.setdefault('database_config_state', 'configured')
        return node_properties

    async def retrieve_info(self):
        """Retrieve container and database information"""
        self.update_logger.info(f"Retrieving database configuration info for node {self.node_id}")
        await super().retrieve_info()
        
        try:
            # Get container node and structure container details
            container_node = self.retrieved_info['current_node']
            container_details = {
                'title': container_node['properties'].get('Title'),
                'description': container_node['properties'].get('Description'),
                'container_type': container_node['properties'].get('ContainerType'),
                'has_database': container_node['properties'].get('HasDatabase', False)
            }
            self.retrieved_info['container_details'] = container_details
            
            # Get existing database child nodes
            existing_databases = await self.db.get_child_nodes(self.node_id, "Database")
            self.retrieved_info['existing_databases'] = existing_databases
            self.update_logger.info(f"Found {len(existing_databases)} existing database nodes")
            
            # Get existing interface child nodes
            existing_interfaces = await self.db.get_child_nodes(self.node_id, "Interface")
            self.retrieved_info['existing_interfaces'] = existing_interfaces
            
            # Get system context for external containers
            system_context = await self.db.get_parent_node(self.node_id)
            if system_context:
                # Find existing external containers
                all_containers = await self.db.get_child_nodes(system_context['id'], "Container")
                external_containers = [
                    container for container in all_containers
                    if container['properties'].get('ContainerType') == 'external'
                ]
                self.retrieved_info['external_containers'] = external_containers
                self.update_logger.info(f"Found {len(external_containers)} external containers")
            
            # Get functional requirements
            project_node = self.retrieved_info['root_node']
            architecture_root = await self.db.get_child_nodes(project_node['id'], "ArchitectureRoot")
            if architecture_root:
                arch_req_nodes = await self.db.get_child_nodes(architecture_root[0]['id'], "ArchitecturalRequirement")
                if arch_req_nodes:
                    functional_reqs = await self.db.get_child_nodes(arch_req_nodes[0]['id'], "FunctionalRequirement")
                    self.retrieved_info['functional_requirements'] = functional_reqs

            # Structure current background info
            current_background_info = {
                'container_context': {
                    'container': container_details,
                    'database_details': {
                        'existing_databases': existing_databases,
                        'existing_interfaces': existing_interfaces,
                        'external_containers': external_containers
                    }
                },
                'requirements_context': {
                    'functional_requirements': self.retrieved_info.get('functional_requirements', [])
                }
            }

            # Handle reconfiguration
            if self.is_reconfig():
                stored_background_info = await self.version_manager._get_context(self.node_id)
                
                if stored_background_info:
                    self.retrieved_info.update({
                        'original_node': self.node,
                        'background_info': stored_background_info.get('background_info', current_background_info),
                        'user_interaction': {
                            'input': self.node['properties'].get('user_inputs', '[]')
                        },
                        'change_log': self.node['properties'].get('change_log', []),
                        'change_reason': self.node['properties'].get('change_reason', '')
                    })
                    if not self.retrieved_info['background_info']:
                        self.retrieved_info['background_info'] = current_background_info
                else:
                    self.retrieved_info.update({
                        'original_node': self.node,
                        'background_info': current_background_info,
                        'user_interaction': {
                            'input': self.node['properties'].get('user_inputs', '[]')
                        },
                        'change_log': self.node['properties'].get('change_log', []),
                        'change_reason': self.node['properties'].get('change_reason', '')
                    })
                
                self.retrieved_info['new_background'] = current_background_info
            else:
                # For initial configuration
                self.retrieved_info['background_info'] = current_background_info
                self.background_info = current_background_info  # Set class variable
                self.retrieved_info['new_background'] = current_background_info
                # Initialize empty user interaction for initial configuration
                self.retrieved_info['user_interaction'] = {
                    'input': '[]'
                }
            
            return self.retrieved_info
            
        except Exception as e:
            self.update_logger.error(f"Error retrieving database configuration info: {str(e)}")
            raise

    def get_modifications_from_llm_output(self):
        """Process LLM output for database configuration"""
        self.update_logger.info(f"Processing LLM output for node {self.node_id}")
        
        # Set child node types
        self.modifications['child_node_types'] = ['Database', 'Interface']
        
        # Mark container as having database
        self.modifications['modified_node']['HasDatabase'] = True
        
        return self.modifications

    async def merge_captured_items(self):
        """Merge captured items into the database"""
        if not self.modifications.get('modified_node'):
            self.update_logger.warning("No modifications to merge")
            return
            
        await self.retrieve_info()
        user_inputs = self.modifications['modified_node'].get('user_inputs', '')
        change_reason = self.modifications['modified_node'].get('change_reason', '')
        
        try:
            if self.is_reconfig():
                self.update_logger.info(f"Processing reconfiguration for node {self.node_id}")
                if self.modifications['modified_node'].get('changes_needed', False):
                    await super().merge_captured_items()
                    
                    # Save version info
                    save_data = {
                        'node_id': self.node_id,
                        'properties': self.modifications['modified_node'],
                        'background_info': self.retrieved_info['new_background'],
                        'user_interaction': {
                            'timestamp': generate_timestamp(),
                            'action': 'reconfig',
                            'input': user_inputs,
                            'reason': change_reason
                        }
                    }
                    await self.version_manager.save_node_info(save_data)
                    self.update_logger.info(f"Saved reconfiguration version info for node {self.node_id}")
            else:
                self.update_logger.info(f"Processing initial configuration for node {self.node_id}")
                await super().merge_captured_items()
                
                # Save version info for initial configuration
                save_data = {
                    'node_id': self.node_id,
                    'properties': self.modifications['modified_node'],
                    'background_info': self.retrieved_info['new_background'],
                    'user_interaction': {
                        'timestamp': generate_timestamp(),
                        'action': 'initial_config',
                        'input': user_inputs,
                        'reason': change_reason
                    }
                }
                await self.version_manager.save_node_info(save_data)
                self.update_logger.info(f"Saved initial configuration version info for node {self.node_id}")
            
            self.update_logger.info(f"Successfully saved changes to node {self.node_id}")
            
        except Exception as e:
            self.update_logger.error(f"Error in database configuration merge: {str(e)}")
            raise

    def is_reconfig(self):
        """Check if this is a reconfiguration scenario."""
        config_state = self.get_config_state(self.discussion_type, self.node_type, self.node)
        return config_state == 'configured'

# Register the discussion type
DiscussionFactory.register('database_configuration', DatabaseConfiguration, "Container") 