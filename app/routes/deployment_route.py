from fastapi import API<PERSON><PERSON><PERSON>, HTT<PERSON><PERSON><PERSON><PERSON>, Depends, Body
from fastapi.responses import JSONResponse
from typing import Dict, Any, Optional,List
import os
import logging
import subprocess 
import json
import asyncio
from urllib.parse import urlparse
from datetime import datetime
from github import Github, GithubException
import shutil
import boto3
from app.connection.establish_db_connection import get_node_db, NodeDB
from app.classes.MongoDB import MongoDBHandler
from app.routes.deployment_helper.sample_tf import get_workflows,get_outputs_tf,get_main_tf,get_providers_tf,get_variables_tf
from app.routes.repository_route import get_repository,list_branches
from app.routes.deployment_helper.node_helper import get_deployment_node
from app.routes.deployment_helper.aws_handler import trigger_amplify_deployment,read_init_tool_content,create_codecommit_repository,modify_codecommit_repo_name,push_to_codecommit,clone_codecommit_repo
from app.routes.deployment_helper.get_tf_files import get_backend_terraform_files, get_frontend_tf_files
from app.routes.deployment_helper.s3_handler import save_terraform_files_to_s3
from app.routes.deployment_helper.status_handler import get_amplify_details,get_ecs_service_status,get_elb_details,get_backend_terraform_files
from app.routes.deployment_helper.terraform_handler import deploy_infrastructure_handler
from app.routes.deployment_helper.directory_handler import cleanup_terraform_files,setup_deployment_directory
import time
import random
import string
from fastapi.responses import StreamingResponse
from typing import AsyncGenerator, Union, Callable
from enum import Enum
from app.utils.datetime_utils import generate_timestamp
from app.core.Settings import settings
from app.core.constants import DEPLOYMENT_COLLECTION_NAME
from app.connection.establish_db_connection import get_mongo_db

# Configure logging
logger = logging.getLogger(__name__)
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(handler)
logger.setLevel(logging.INFO)

_SHOW_NAME = "deployment"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)

def get_first_two_words(text):
    words = text.split('-')
    return '-'.join(words[:2])

async def determine_container_type(component):
        """
        Analyze component to determine its type based on properties and description.
        Returns: str - Component type ('frontend', 'backend', 'database', or 'unknown')
        """
        # Extract properties and convert to lowercase for case-insensitive matching
        title = component['properties'].get('Title', '').lower()
        description = component['properties'].get('Description', '').lower()
        technology = component['properties'].get('Technology', '').lower()
        
        # Combine all fields for comprehensive searching
        combined_text = f"{title} {description} {technology}"
        
        # Enhanced keyword sets with more comprehensive terms
        database_keywords = {
            'database', 'db', 'storage', 'mongo', 'postgresql', 'mysql', 'redis',
            'oracle', 'sqlserver', 'cassandra', 'elasticsearch', 'dynamodb',
            'mariadb', 'sql', 'nosql', 'data store'
        }
        
        frontend_keywords = {
            'ui', 'frontend', 'react', 'vue', 'angular', 'web', 'client',
            'browser', 'spa', 'tsx', 'jsx'
        }
        
        backend_keywords = {
            'api', 'backend', 'server', 'service', 'nodejs', 'java', 'python',
            'microservice', 'rest', 'graphql', 'grpc', 'flask', 'django', 'express','Node.js','Chart.js',
        }

        # Count matches for each type to handle components that might match multiple types
        matches = {
            'database': sum(1 for keyword in database_keywords if keyword in combined_text),
            'frontend': sum(1 for keyword in frontend_keywords if keyword in combined_text),
            'backend': sum(1 for keyword in backend_keywords if keyword in combined_text)
        }
        
        # Debug logging to help track matches
        print(f"Component: {title}")
        print(f"Matches found: {matches}")
        print("--------------------}")
        
        # If we have matches, return the type with the most matches
        if any(matches.values()):
            print(max(matches.items(), key=lambda x: x[1])[0])
            return max(matches.items(), key=lambda x: x[1])[0]
        
        return "unknown"

def generate_unique_domain(base_name: str ) -> str:
    """
    Generate a valid unique domain name for AWS Amplify.
    
    Args:
        base_name (str): Base name for the domain
        
    Returns:
        str: Valid unique domain name
    """
    # Clean the base name - remove special characters and convert to lowercase
    clean_base = ''.join(c if c.isalnum() or c == '-' else '-' for c in base_name.lower())
    clean_base = clean_base.strip('-')
    
    # Generate timestamp
    timestamp = int(time.time())
    
    # Generate random string
    random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=4))
    
    # Combine with default AWS Amplify domain
    # Format: base-name-timestamp-random.amplifyapp.com
    domain = f"{clean_base}-{timestamp}-{random_suffix}.amplifyapp.com"
    
    return domain


# Status Enums for Consistency
class StepStatus(Enum):
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    ERROR = "error"

def get_step_response(status: str, step: str, message: str) -> Dict[str, Any]:
    return {
        "status": status,
        "step": step,
        "message": message
    }

@router.post("/{project_id}/{container_id}/{branch}/deploy_infrastructure")
async def deploy_infrastructure(
    project_id: int,
    container_id: int,
    branch: str,
    db = Depends(get_node_db)
) -> StreamingResponse:
    async def generate_progress(
        project_id=project_id,  # Capture variables by passing them as default arguments
        container_id=container_id,
        branch=branch
    ):
        temp_dir = "./deployments"
        
        try:
            logger.info(f"Starting deployment process for project {project_id}, container {container_id}, branch {branch}")
            
            # Initialization
            init_step = get_step_response(
                StepStatus.IN_PROGRESS.value,
                "initialization",
                "Starting deployment process"
            )
            yield f"data: {json.dumps(init_step)}\n\n"
            
            infrastructure_path = os.path.join(temp_dir, 'infrastructure')
            # workflows_path = os.path.join(temp_dir, '.github/workflows')
            os.makedirs(infrastructure_path, exist_ok=True)

            # Set up directory structure
            deployment_dir = setup_deployment_directory(temp_dir, cleanup_existing=True)
            if not deployment_dir:
                raise Exception("Failed to set up deployment directory")

            logger.info(f"Deployment directory ready at: {deployment_dir}")

            # Configuration and Validation
            logger.info("Starting input validation")
            access_token = os.environ.get("GITHUB_ACCESS_TOKEN")
            if not access_token:
                raise ValueError("GitHub access token not found in environment variables")

            
            repo_info = await get_repository(project_id,container_id,db)
            repo_url = repo_info["repository"]["cloneUrlHttp"]

            logger.info("Input validation completed successfully")

            '''
            # Keeping for future reference
            validation_complete = {
                "status": "completed",
                "step": "validation",
                "message": "All inputs validated successfully"
            }
            yield f"data: {json.dumps(validation_complete)}\n\n"
            '''


            # Context Retrieval
            logger.info("Retrieving system context")
            system_context = await db.get_child_nodes(project_id, "SystemContext")
            if not system_context:
                logger.error("System context not found")
                raise ValueError("System context not found")
            
            # Get container type
            container = await db.get_node_by_id(container_id)
            container_type = await determine_container_type(container)

            deployment_node = await get_deployment_node(project_id, container_id, db)
            deployment_type = deployment_node['properties'].get('deployment_type', 'frontend')
            
            # Extract repository name
            repo_name = repo_url.split('/')[-1].replace('.git', '').replace('-experimental', '')
            main_repo_name = repo_url.split('/')[-1].replace('.git', '')
            reuse_workspace = True
            app_name = get_first_two_words(repo_name)

            if not deployment_node:
                raise ValueError("Deployment node not found")
             # Handle frontend (Amplify) deployment
            if container_type == "frontend":
                # Read .init-run-tool directly from CodeCommit
                try:
                    init_tool_content = await read_init_tool_content(
                        repo_name=main_repo_name,
                        branch=branch
                    )
                    
                    
                    if not init_tool_content:
                        raise Exception("No working_directory specified in .init-run-tool")
                    
                    msg = {
                        "status": "in_progress",
                        "step": "configuration",
                        "message": f"Found working directory: {init_tool_content}"
                    }
                    yield f"data: {json.dumps(msg)}\n\n"
                    
                except Exception as e:
                    raise Exception(f"Error reading .init-run-tool: {str(e)}")

                
                #  # Prepare terraform files
                terraform_files = {
                    'main.tf': deployment_node['properties'].get('main_tf'),
                    'terraform.tfvars': deployment_node['properties'].get('terraform_tfvars'),
                    'dockerfile': deployment_node['properties'].get('dockerfile'),
                    'docker_compose': deployment_node['properties'].get('docker_compose'),
                 }
                # Deploy infrastructure with proper initialization
                terraform_outputs = await deploy_infrastructure_handler(project_id,container_id,infrastructure_path, terraform_files,reuse_workspace)
                app_id = terraform_outputs['app_id']['value'] 
                status = await trigger_amplify_deployment(app_id,branch)
                print(status)
            else:
                init_complete = get_step_response(
                    StepStatus.COMPLETED.value,
                    "initialization",
                    "Deployment process initialized"
                )
                yield f"data: {json.dumps(init_complete)}\n\n"

                new_repo_name = f"{repo_name}_deploy"
                region = "us-east-2"

                
                # Repository Creation
                logger.info(f"Creating Codecommit repository: {new_repo_name}")
                repo_step = {
                    "status": "in_progress",
                    "step": "repo_creation",
                    "message": f"Creating Codecommit repository: {new_repo_name}"
                }
                yield f"data: {json.dumps(repo_step)}\n\n"

                created_repo_url = await create_codecommit_repository(repo_name,region,branch)
                # if not await create_codecommit_repository(repo_name):
                #     raise Exception(f"Failed to ensure repository {new_repo_name} exists")

                # Terraform Preparation
                logger.info("Preparing Terraform files")
                terraform_prep = get_step_response(
                    StepStatus.IN_PROGRESS.value,
                    "terraform_prep",
                    "Preparing Terraform files"
                )
                yield f"data: {json.dumps(terraform_prep)}\n\n"
                
                technology = "node"
                # os.makedirs(workflows_path, exist_ok=True)
                # Get deployment node to access configuration
                terraform_files = get_backend_terraform_files(technology, repo_name, branch,app_name)
                
                #  # Prepare terraform files
                terraform_files = {
                    'main.tf': deployment_node['properties'].get('main_tf'),
                    'terraform.tfvars': deployment_node['properties'].get('terraform_tfvars'),
                    'dockerfile': deployment_node['properties'].get('dockerfile'),
                    'docker_compose': deployment_node['properties'].get('docker_compose'),
                 }


                reuse_workspace = True
                # Deploy infrastructure with proper initialization
                terraform_outputs = await deploy_infrastructure_handler(project_id,container_id,infrastructure_path, terraform_files,reuse_workspace)

            if db:
                if deployment_node:
                    
                    updated_properties = {
                        **deployment_node['properties'],
                        'terraform_outputs': json.dumps(terraform_outputs),
                        'last_deployment': generate_timestamp()
                    }
                    await db.update_node_by_id(
                        deployment_node['id'],
                        updated_properties
                    )

            logger.info("Codecommit repository created successfully")
            repo_complete = {
                "status": "completed",
                "step": "repo_creation",
                "message": "Codecommit repository created successfully"
            }
            yield f"data: {json.dumps(repo_complete)}\n\n"


            terraform_prep_complete = get_step_response(
                StepStatus.COMPLETED.value,
                "terraform_prep",
                "Terraform files prepared successfully"
            )
            yield f"data: {json.dumps(terraform_prep_complete)}\n\n"

            # Directory Setup
            logger.info("Setting up deployment directories")
            directory_step = get_step_response(
                StepStatus.IN_PROGRESS.value,
                "directory_setup",
                "Setting up deployment directories"
            )
            yield f"data: {json.dumps(directory_step)}\n\n"
            
            # Validate directory creation
            if not os.path.exists(infrastructure_path):
                raise Exception("Failed to create required directories")
            
            directory_complete = get_step_response(
                StepStatus.COMPLETED.value,
                "directory_setup",
                "Deployment directories created successfully"
            )
            yield f"data: {json.dumps(directory_complete)}\n\n"

            # File Creation
            logger.info("Creating infrastructure files")
            saved_files = {"terraform": []}
            for filename, content in terraform_files.items():
                if content and filename != 'workflow_file':
                    file_path = os.path.join(infrastructure_path, filename)
                    print(content)
                    with open(file_path, 'w') as f:
                        f.write(content)
                    saved_files["terraform"].append(filename)
                    logger.debug(f"Created file: {filename}")
            
           
            # Final completion
            logger.info("Deployment completed successfully")
            completion_data = {
                "status": "completed",
                "step": "deployment",
                "message": "Infrastructure deployed successfully",
                "data": {
                    "project_id": project_id,
                    "container_id": container_id,
                    "saved_files": saved_files,
                    "terraform_outputs": terraform_outputs,
                    "next_steps": [
                        "Monitor the deployment in AWS Console",
                        "Check GitHub Actions for CI/CD pipeline status",
                        "Update application configurations with new infrastructure details"
                    ]
                }
            }
            yield f"data: {json.dumps(completion_data)}\n\n"

        except FileNotFoundError as e:
            logger.error(f"File operation failed: {str(e)}")
            error_data = get_step_response(
                StepStatus.ERROR.value,
                "error",
                f"File operation failed: {str(e)}"
            )
            yield f"data: {json.dumps(error_data)}\n\n"
        
        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing failed: {str(e)}")
            error_data = get_step_response(
                StepStatus.ERROR.value,
                "error",
                f"JSON parsing failed: {str(e)}"
            )
            yield f"data: {json.dumps(error_data)}\n\n"

        except Exception as e:
            logger.error(f"Deployment failed: {str(e)}")
            error_data = get_step_response(
                StepStatus.ERROR.value,
                "error",
                f"Error deploying infrastructure: {str(e)}"
            )
            yield f"data: {json.dumps(error_data)}\n\n"

        finally:
            if os.path.exists(temp_dir):
                try:
                    # shutil.rmtree(temp_dir)
                    logger.info("Cleaned up temporary directory")
                except Exception as e:
                    logger.error(f"Failed to clean up directory: {str(e)}")

    return StreamingResponse(
        generate_progress(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )



@router.get("/{project_id}/{container_id}/get_deployment_form")
async def get_deployment_form(
    project_id: int,
    container_id: int,
    db = Depends(get_node_db)
) -> JSONResponse:
    """
    Get deployment form configuration and any existing values
    
    Args:
        project_id: Project ID
        container_id: Container ID
    Returns:
        Form configuration and existing values
    """
    try:
        await create_or_update_deployment_node(project_id,container_id,db)
        # Get deployment node
        deployment_nodes = await db.get_child_nodes(container_id, "Deployment")
        if not deployment_nodes:
            raise HTTPException(status_code=404, detail="Deployment configuration not found")
        
        deployment_node = deployment_nodes[0]
        
        
        project_repositories = await get_repository(project_id, container_id, db)
        if "error" in project_repositories:
            raise HTTPException(status_code=404, detail="Repository not found")
            
        repo_info = project_repositories["repository"]
        repo_url = repo_info.get("cloneUrlHttp", "")
        repo_name = repo_info.get("repositoryName", "")

        
        aws_repo_url = modify_codecommit_repo_name(repo_url)
        
        branches_info = await list_branches(project_id, container_id, db)
        branch_options = []
        for branch in branches_info.get("branches", []):
            # Convert datetime objects to strings if they exist
            branch_data = {
                "label": f"{branch['name']}", 
                "value": branch['name']
            }
            branch_options.append(branch_data)

        default_branch = branches_info.get("branches", [{}])[0].get("name", "kavia-main")

        

        # Get existing configuration or create default
        existing_config = deployment_node['properties'].get('deployment_config', {})
        # Create default configuration
        repo_name = get_first_two_words(repo_name)

        # Generate unique domain name
        domain_name = generate_unique_domain(repo_name)

        default_config = {
            "repo_url": aws_repo_url,
            "app_name": f"{repo_name}-app",
            "domain_name": domain_name
        }
                
        # Update deployment node with default config
        # updated_properties = {
        #     **deployment_node['properties'],
        #     'deployment_config': json.dumps(default_config),
        #     'updated_at': generate_timestamp()  # Convert datetime to string
        # }
        
        # await db.update_node_by_id(
        #     deployment_node['id'],
        #     updated_properties
        # )
        
        # existing_config = default_config

        if isinstance(existing_config, str):
            try:
                existing_config = json.loads(existing_config)
            except json.JSONDecodeError:
                existing_config = {}

        config_to_use = {**default_config, **existing_config}

        form_config = {
            "title": "Deployment Configuration",
            "description": "Configure your infrastructure deployment settings",
            "fields": [
                {
                    "name": "repo_url",
                    "label": "Repository URL",
                    "type": "text",
                    "placeholder": "https://github.com/username/repo.git",
                    "required": True,
                    "value": aws_repo_url,
                    "validation": "url"
                },
                {
                    "name": "branch",
                    "label": "Branch",
                    "type": "select",
                    "required": True,
                    "options": branch_options  ,
                    "value":config_to_use.get("branch", "")
                },
                {
                    "name": "app_name",
                    "label": "Application Name",
                    "type": "text",
                    "placeholder": "my-application",
                    "required": True,
                    "value": f"{repo_name}-app"
                },
                {
                    "name": "domain_name",
                    "label": "Domain Name",
                    "type": "text",
                    "placeholder": "example.com",
                    "required": False,
                    "value": domain_name
                },
                {
                    "name": "aws_region",
                    "label": "AWS Region",
                    "type": "select",
                    "required": True,
                    "value": "us-east-2",
                    "options": [
                        {"label": "US West (Oregon)", "value": "us-west-2"},
                        {"label": "US East (N. Virginia)", "value": "us-east-1"},
                        {"label": "EU (Ireland)", "value": "eu-west-1"},
                        {"label": "Asia Pacific (Singapore)", "value": "ap-southeast-1"}
                    ]
                }
            ],
        "submit": {
        "label": "Deploy Infrastructure",
        "endpoint": f"/api/deployment/{project_id}/{container_id}/update_deployment_config",
        "method": "POST"
    }
        }
        
        return JSONResponse(
            status_code=200,
            content={
                "form_config": form_config,
                "deployment_node_id": deployment_node['id']
            }
        )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting deployment form configuration: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    

@router.post("/{project_id}/{container_id}/update_deployment_config")
async def update_deployment_config(
    project_id: int,
    container_id: int,
    form_data: dict = Body(...),
    db: NodeDB = Depends(get_node_db)
) -> JSONResponse:
    """
    Update deployment configuration and store for template generation
    """
    try:
        print(form_data)
        # Get deployment node
        deployment_nodes = await db.get_child_nodes(container_id, "Deployment")
        if not deployment_nodes:
            raise HTTPException(status_code=404, detail="Deployment configuration not found")
        
        deployment_node = deployment_nodes[0]
        
        # Store form data for template generation
        updated_properties = {
            **deployment_node['properties'],
            'deployment_config': json.dumps(form_data),
            'updated_at': generate_timestamp()
        }
        
        # Update node in database
        result = await db.update_node_by_id(
            deployment_node['id'],
            updated_properties
        )
        
        if not result:
            raise HTTPException(
                status_code=500,
                detail="Failed to update deployment configuration"
            )
            
        return JSONResponse(
            status_code=200,
            content={
                "message": "Deployment configuration updated successfully",
                "deployment_config": form_data
            }
        )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating deployment configuration: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))



# Modified deployment route to use CodeCommit and CodePipeline
@router.post("/{project_id}/{container_id}/{branch}/push_to_new_repo")
async def deploy_to_aws(
    project_id: int,
    container_id: int,
    branch: str,
    repo_url: str,
    db = Depends(get_node_db)
) -> StreamingResponse:
    async def generate_progress():
        working_dir = "./deployments_latest"
        # Create temp directory
        temp_clone_dir = f"{working_dir}_full"
        if os.path.exists(temp_clone_dir):
            shutil.rmtree(temp_clone_dir)
        os.makedirs(temp_clone_dir)
        try:
            # Initialize
            init_step = {
                "status": "in_progress",
                "step": "initialization",
                "message": "Starting AWS deployment process"
            }
            yield f"data: {json.dumps(init_step)}\n\n"
            
            # Get repository info
            repo_info = await get_repository(project_id, container_id, db)
            repo_url = repo_info["repository"]["cloneUrlHttp"]
            
            # Extract repository name
            repo_name = repo_url.split('/')[-1].replace('.git', '').replace('-experimental', '')
            repo_actual_name = repo_url.split('/')[-1].replace('.git', '')
            app_name = get_first_two_words(repo_name)
            new_repo_name = f"{repo_name}_deploy"
            # Clone repository
            clone_step = {
                "status": "in_progress",
                "step": "clone",
                "message": "Cloning CodeCommit repository"
            }
            yield f"data: {json.dumps(clone_step)}\n\n"
            
            clone_success = await clone_codecommit_repo(
                repo_url=repo_url,
                local_path=temp_clone_dir,
                branch=branch
            )
            
            if not clone_success:
                raise Exception("Failed to clone repository")
            
            # Get working directory from .init-run-tool
            frontend_path = await read_init_tool_content(repo_actual_name,branch=branch)
            frontend_path = frontend_path['working_directory']
            frontend_path =temp_clone_dir
            technology = "node"
            app_name = "basic-quickvote-v2"
            # if not os.path.exists(frontend_path):
            #     raise Exception("Frontend directory not found in repository")

            if os.path.exists(working_dir):
                shutil.rmtree(working_dir)
            os.makedirs(working_dir)
             # Generate terraform and supporting files
            terraform_files = get_backend_terraform_files(
                technology=technology,
                repo_name=new_repo_name,
                branch=branch,
                app_name=app_name
            )

            # Create buildspec.yml
            buildspec_path = os.path.join(working_dir, 'buildspec.yml')
            with open(buildspec_path, 'w') as f:
                f.write(terraform_files['buildspec_yml'])
            logger.info("Created buildspec.yml")

            # Create Dockerfile
            dockerfile_path = os.path.join(working_dir, 'Dockerfile')
            with open(dockerfile_path, 'w') as f:
                f.write(terraform_files['dockerfile'])
            logger.info("Created Dockerfile")

            shutil.copytree(frontend_path, working_dir, dirs_exist_ok=True)
            shutil.rmtree(temp_clone_dir)

            logger.info("Frontend directory setup completed")
            frontend_complete = {
                "status": "completed",
                "step": "frontend_setup",
                "message": "Frontend directory setup completed"
            }
            yield f"data: {json.dumps(frontend_complete)}\n\n"

            # Workflow Setup
            logger.info("Setting up Codecommit workflows")
            workflow_step = {
                "status": "in_progress",
                "step": "workflow_setup",
                "message": "Setting up Codecommit workflows"
            }
            yield f"data: {json.dumps(workflow_step)}\n\n"
        

            logger.info("GitHub workflows setup completed")
            workflow_complete = {
                "status": "completed",
                "step": "workflow_setup",
                "message": "GitHub workflows setup completed"
            }
            yield f"data: {json.dumps(workflow_complete)}\n\n"

            # Push changes
            push_step = {
                "status": "in_progress",
                "step": "push",
                "message": "Pushing to CodeCommit"
            }
            yield f"data: {json.dumps(push_step)}\n\n"
            
            await push_to_codecommit(
                repo_path=repo_url,
                branch=branch,
                source_directory=working_dir,
                force=True
            )

            push_step = {
                "status": "completed",
                "step": "push",
                "message": "Pushing to CodeCommit"
            }
            yield f"data: {json.dumps(push_step)}\n\n"
            
            
            # Final completion
            completion_data = {
                "status": "completed",
                "step": "completion",
                "message": "AWS deployment completed"}
            #     "data": {
            #         "pipeline_execution": pipeline_execution,
            #         "final_status": status
            #     # }
            # }
            yield f"data: {json.dumps(completion_data)}\n\n"
            deployment_node = await get_deployment_node(project_id, container_id, db)
            deployment_type = deployment_node['properties'].get('terraform_outputs')
            print(deployment_type)
        except Exception as e:
            logger.error(f"AWS deployment failed: {str(e)}")
            error_data = {
                "status": "error",
                "step": "error",
                "message": f"Error in AWS deployment: {str(e)}"
            }
            yield f"data: {json.dumps(error_data)}\n\n"
            
        finally:
            # Cleanup
            if os.path.exists(working_dir):
                print(working_dir)
                shutil.rmtree(working_dir)

    return StreamingResponse(
        generate_progress(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )

@router.post("/{project_id}/{container_id}/create_or_update_deployment_node")
async def create_or_update_deployment_node(
    project_id: int,
    container_id: int,
    db: NodeDB = Depends(get_node_db)
) -> JSONResponse:
    """Create or update a deployment node with Terraform configurations."""
    try:
        container = await db.get_node_by_id(container_id)
        container_type = await determine_container_type(container)
        # Get repository and container information
        repo_info = await get_repository(project_id, container_id, db)
        if "error" in repo_info:
            raise HTTPException(status_code=404, detail="Repository not found")

        # Get container information
        container = await db.get_node_by_id(container_id)
        if not container:
            raise HTTPException(status_code=404, detail="Container not found")

        
        existing_deployment_nodes = await db.get_child_nodes(container_id, "Deployment")
        deployment_properties = {
                "Title": f"Deployment Configuration for {repo_name}",
                "Description": "AWS Amplify deployment configuration",
                "deployment_type": "",
                "main_tf": "",
                "terraform_tfvars": "",
                "configuration_state": "",
                "Type": "Deployment",
                "updated_at": generate_timestamp(),
                "deployment_config":""
            }
        if existing_deployment_nodes:
            # Update existing deployment node
            existing_node = existing_deployment_nodes[0]
            updated_node = await db.update_node_by_id(
                existing_node["id"],
                deployment_properties
            )
            message = "Deployment node updated successfully"
        else:
            # Create new deployment node
            updated_node = await db.create_node(
                ["Deployment"],
                deployment_properties,
                container_id
            )
            message = "Deployment node created successfully"

            return JSONResponse(
                status_code=200,
                content={
                    "message": message,
                    "deployment_node": updated_node,
                    "deployment_type": deployment_type
                }
            )

        # Get container type and properties
        # container_type = container.get("properties", {}).get("Type", "").lower()
        repo_data = repo_info["repository"]
        repo_url = repo_data.get("cloneUrlHttp", "")

        # Extract repository name
        repo_name = repo_url.split('/')[-1].replace('.git', '').replace('-experimental', '')
        new_repo_name = f"{repo_name}_deploy"
        main_repo_name = repo_url.split('/')[-1].replace('.git', '')
        # Get branch information
        branches_info = await list_branches(project_id, container_id, db)
        
        # Generate configurations based on container type
        if container_type == "frontend":
            technology = "react"
            default_branch = "kavia-main"
            app_name = "cosmetics-recommender-v2" 

            init_tool_content = await read_init_tool_content(
            repo_name=main_repo_name,
            branch=default_branch
            )
                    
                     
            if not init_tool_content:
                raise Exception("No working_directory specified in .init-run-tool")
            # Check for existing deployment node
            terraform_files = get_frontend_tf_files(
                repo_name=main_repo_name,
                app_name = app_name,
                branch=default_branch,
                working_directory=init_tool_content['working_directory'],
                technology=technology
            )
            deployment_type = "frontend"

            deployment_properties = {
                "Title": f"Deployment Configuration for {repo_name}",
                "Description": "AWS Amplify deployment configuration",
                "deployment_type": deployment_type,
                "main_tf": terraform_files["main_tf"],
                "terraform_tfvars": terraform_files["terraform_tfvars"],
                "configuration_state": "configured",
                "Type": "Deployment",
                "updated_at": generate_timestamp(),
                "deployment_config":""
            }
        else:
            technology = "node"
            app_name = "basic-quickvote-v2"
            deployment_type = "backend"
            

            terraform_files = get_backend_terraform_files(
                technology=technology,
                repo_name=new_repo_name,
                branch=default_branch,
                app_name = app_name
            )

            deployment_properties = {
                "Title": f"Deployment Configuration for {repo_name}",
                "Description": "AWS ECS deployment configuration",
                "deployment_type": deployment_type,
                "main_tf": terraform_files["main_tf"],
                "terraform_tfvars": terraform_files["terraform_tfvars"],
                "dockerfile": terraform_files["dockerfile"],
                "docker_compose": terraform_files["docker_compose"],
                "configuration_state": "configured",
                "Type": "Deployment",
                "updated_at": generate_timestamp()
            }

        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/{project_id}/{container_id}/deployment-status")
async def monitor_deployment_status(
    project_id: int,
    container_id: int,
    db: NodeDB = Depends(get_node_db)
) -> StreamingResponse:
    """Monitor deployment status and update deployment node."""
    
    async def generate_status():
        max_retries = 30
        retry_count = 0

        try:
            # Get deployment node
            deployment_nodes = await db.get_child_nodes(container_id, "Deployment")
            if not deployment_nodes:
                raise Exception("Deployment node not found")
                
            deployment_node = deployment_nodes[0]
            deployment_type = deployment_node['properties'].get('deployment_type', 'frontend')
            
            # Get terraform outputs from node
            terraform_outputs = json.loads(deployment_node['properties'].get('terraform_outputs', '{}'))
            
            # Get deployment config
            deployment_config = json.loads(deployment_node['properties'].get('deployment_config', '{}'))
            app_name = deployment_config.get('app_name')
            branch = deployment_config.get('branch')

            # Handle frontend (Amplify) deployment
            if deployment_type == 'frontend':
                app_id = terraform_outputs.get('amplify_app_id', {}).get('value')
                if not app_id:
                    raise Exception("No Amplify app ID found in Terraform outputs")
                
                msg = {
                    'status': 'in_progress',
                    'step': 'infrastructure',
                    'message': 'Infrastructure deployed successfully',
                    'app_id': app_id
                }
                yield f"data: {json.dumps(msg)}\n\n"
                
                while retry_count < max_retries:
                    try:
                        app_details = await get_amplify_details(app_id)
                        msg = {
                            'status': 'in_progress',
                            'step': 'deployment',
                            'message': 'Waiting for deployment...',
                            'details': app_details
                        }
                        yield f"data: {json.dumps(msg)}\n\n"
                        
                        amplify_client = boto3.client('amplify')
                        branches = amplify_client.list_branches(appId=app_id)
                        branch_status = next(
                            (b for b in branches['branches'] if b['branchName'] == branch),
                            None
                        )
                        
                        if branch_status and branch_status.get('status') == 'ACTIVE':
                            final_details = await get_amplify_details(app_id)
                            deployment_url = final_details['branch_domains'].get(branch)
                            msg = {
                                'status': 'completed',
                                'step': 'finished',
                                'message': 'Deployment completed successfully',
                                'details': final_details,
                                'url': deployment_url
                            }
                            yield f"data: {json.dumps(msg)}\n\n"
                            
                            # Update deployment node with final status
                            updated_properties = {
                                **deployment_node['properties'],
                                'deployment_url': deployment_url,
                                'deployment_status': 'deployed',
                                'last_deployed': generate_timestamp(),
                                'deployment_details': json.dumps(final_details)
                            }
                            await db.update_node_by_id(deployment_node['id'], updated_properties)
                            break
                            
                    except Exception as e:
                        msg = {
                            'status': 'in_progress',
                            'step': 'deployment',
                            'message': f'Checking status: {str(e)}'
                        }
                        yield f"data: {json.dumps(msg)}\n\n"
                    
                    await asyncio.sleep(10)
                    retry_count += 1
                    
            # Handle backend (ECS) deployment
            else:
                service_url = terraform_outputs.get('service_url', {}).get('value')
                if not service_url:
                    raise Exception("No service URL found in Terraform outputs")
                
                msg = {
                    'status': 'in_progress',
                    'step': 'infrastructure',
                    'message': 'Infrastructure deployed successfully',
                    'service_url': service_url
                }
                yield f"data: {json.dumps(msg)}\n\n"
                
                while retry_count < max_retries:
                    try:
                        elb_details = await get_elb_details(app_name)
                        service_status = await get_ecs_service_status(f"{app_name}-cluster", f"{app_name}-service")
                        
                        msg = {
                            'status': 'in_progress',
                            'step': 'deployment',
                            'message': 'Waiting for deployment...',
                            'details': {
                                'elb': elb_details,
                                'service': service_status
                            }
                        }
                        yield f"data: {json.dumps(msg)}\n\n"
                        
                        if (service_status['running_count'] > 0 and 
                            service_status['running_count'] == service_status['desired_count']):
                            msg = {
                                'status': 'completed',
                                'step': 'finished',
                                'message': 'Deployment completed successfully',
                                'details': {
                                    'elb': elb_details,
                                    'service': service_status
                                },
                                'url': elb_details['url']
                            }
                            yield f"data: {json.dumps(msg)}\n\n"
                            
                            # Update deployment node with final status
                            updated_properties = {
                                **deployment_node['properties'],
                                'deployment_url': elb_details['url'],
                                'deployment_status': 'deployed',
                                'last_deployed': generate_timestamp(),
                                'deployment_details': json.dumps({
                                    'elb': elb_details,
                                    'service': service_status
                                })
                            }
                            await db.update_node_by_id(deployment_node['id'], updated_properties)
                            break
                            
                    except Exception as e:
                        msg = {
                            'status': 'in_progress',
                            'step': 'deployment',
                            'message': f'Checking status: {str(e)}'
                        }
                        yield f"data: {json.dumps(msg)}\n\n"
                    
                    await asyncio.sleep(10)
                    retry_count += 1
            
            if retry_count >= max_retries:
                raise Exception("Deployment timed out")

        except Exception as e:
            msg = {
                'status': 'error',
                'step': 'error',
                'message': f"Deployment monitoring failed: {str(e)}"
            }
            yield f"data: {json.dumps(msg)}\n\n"

    return StreamingResponse(
        generate_status(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )
    
# Artifact Deployments
@router.get("/{project_id}/list_deployments")
async def list_deployments(
    project_id: str,
    db: NodeDB = Depends(get_node_db),
    mongo_db: MongoDBHandler = Depends(get_mongo_db)
) -> JSONResponse:
    cursor = mongo_db.db[DEPLOYMENT_COLLECTION_NAME].find(
        {"project_id": project_id}
    )
    
    # Convert cursor results and handle datetime and ObjectId serialization
    result = []
    for doc in cursor:
        # Create a new dict for the processed document
        processed_doc = {}
        
        # Convert ObjectId to string
        if '_id' in doc:
            processed_doc['_id'] = str(doc['_id'])
            
        # Process all other fields
        for key, value in doc.items():
            if key == '_id':
                continue
            # Convert datetime objects to ISO format strings
            if isinstance(value, datetime):
                processed_doc[key] = value.isoformat()
            else:
                processed_doc[key] = value
                
        result.append(processed_doc)
    
    return JSONResponse(status_code=200, content=result)


@router.delete("/{project_id}/delete_deployment/{app_id}")
async def delete_deployment(
    project_id: str,
    app_id: str,
    db: NodeDB = Depends(get_node_db),
    mongo_db: MongoDBHandler = Depends(get_mongo_db)
) -> JSONResponse:
    try:
        # Delete the deployment from AWS Amplify
        # Implementation of AWS Amplify deletion would go here

        amplify_client = boto3.client('amplify', region_name=settings.AWS_REGION,
                                    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                                    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        amplify_client.delete_app(appId=app_id)

        # Remove the deployment record from MongoDB
        result = mongo_db.db[DEPLOYMENT_COLLECTION_NAME].delete_one({
            "project_id": project_id,
            "app_id": app_id
        })
        
        if result.deleted_count == 0:
            return JSONResponse(
                status_code=404,
                content={"message": f"Deployment with app_id {app_id} not found"}
            )
            
        return JSONResponse(
            status_code=200,
            content={"message": f"Deployment with app_id {app_id} successfully deleted"}
        )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"message": f"Failed to delete deployment: {str(e)}"}
        )