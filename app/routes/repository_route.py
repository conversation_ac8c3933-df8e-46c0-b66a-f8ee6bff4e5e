from app.connection.establish_db_connection import get_node_db, NodeDB
from app.connection.llm_init import get_llm_interface
from app.utils.project_utils import name_to_slug
from app.utils.node_utils import get_node_type
from fastapi import APIRouter, Depends, HTTPException
from app.models.repository_model import RepositoryUpdateRequest
from fastapi.responses import JSONResponse, StreamingResponse
from app.core.Settings import Settings
from app.core.constants import GIT_IGNORE_CONTENT
from app.connection.tenant_middleware import get_tenant_id
import boto3
from github import Github
from typing import Optional
import json
import os
from app.routes.deployment_helper.node_helper import get_deployment_node
from app.routes.scm_route import scm_manager
from app.models.scm import SCMType, SCMConfiguration
from app.utils.auth_utils import get_current_user
from app.utils.hash import decrypt_string
import gitlab
import github
from math import ceil
from app.routes.kg_route import import_codebase, CodebaseImportRequest, RepoBranchRequest
from fastapi import BackgroundTasks
import logging
import requests
import re
from pydantic import BaseModel, SecretStr, Field
from fastapi import Query
from enum import Enum
from github.Repository import Repository
from app.utils.respository_utils import get_github_client
from app.connection.establish_db_connection import get_mongo_db, MongoDBHandler
from app.core.constants import TASKS_COLLECTION_NAME
from app.models.scm import ACCESS_TOKEN_PATH


# Logger
logger = logging.getLogger(__name__)

_SHOW_NAME = "repository"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)

db = get_node_db()
llm_interface = get_llm_interface()
settings = Settings()


    
def get_codecommit_client():
    return boto3.client('codecommit', 
                        region_name=os.getenv("AWS_DEFAULT_REGION"), 
                        aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID_MAIN"), 
                        aws_secret_access_key=os.getenv("AWS_ACCESS_KEY_ID_MAIN"))



async def create_codecommit_repository(repository_name: str, project_details: dict) -> dict:
    codecommit_client = get_codecommit_client()
    
    
    response = codecommit_client.create_repository(
        repositoryName=repository_name,
        repositoryDescription="",
        tags={}
    )
    
    repository_metadata = {
        'service': 'codecommit',
        'repositoryName': response["repositoryMetadata"]["repositoryName"],
        'repositoryId': response["repositoryMetadata"]["repositoryId"],
        'cloneUrlHttp': response["repositoryMetadata"]["cloneUrlHttp"],
        'cloneUrlSsh': response["repositoryMetadata"]["cloneUrlSsh"],
        'repositoryStatus': 'initialized'
    }
    
    initial_commit = codecommit_client.create_commit(
        repositoryName=repository_name,
        branchName='kavia-main',
        authorName='Automated System',
        email='<EMAIL>',
        commitMessage='Initial commit',
        putFiles=[
            {
                'filePath': 'README.md',
                'fileMode': 'NORMAL',
                'fileContent': b'# Project Repository\n\nThis is the initial README file for the project.'
            }
        ]
    )
    
    codecommit_client.put_file(
        repositoryName=repository_name,
        branchName='kavia-main',
        fileContent=GIT_IGNORE_CONTENT.encode('utf-8'),
        filePath=".gitignore",
        commitMessage="Add .gitignore file with Python and Node.js ignorables",
        parentCommitId=initial_commit['commitId']
    )
    
    return repository_metadata


async def setup_default_branch_safe(repo: Repository, target_branch="kavia-main", max_retries=3):
   from github import GithubException
   
   for attempt in range(max_retries):
       try:
           # Get main branch and create target branch
           main_branch = repo.get_branch("main")
           repo.create_git_ref(f"refs/heads/{target_branch}", main_branch.commit.sha)
           
           # Set as default and delete main
           repo.edit(default_branch=target_branch)
           repo.get_git_ref("refs/heads/main").delete()
           
           return True
           
       except GithubException as e:
           if e.status == 422 and "already exists" in str(e):
               return True  # Branch already exists
           if attempt == max_retries - 1:
               print(f"Branch setup failed after {max_retries} attempts: {e.data.get('message', str(e))}")
               return False
       except Exception as e:
           if attempt == max_retries - 1:
               print(f"Branch setup failed after {max_retries} attempts: {str(e)}")
               return False
   
   return False

async def create_github_repository(repository_name: str,
                                 config: SCMConfiguration,
                                 is_private: bool = True,
                                ):
    access_token = config.credentials.access_token
    github_client = Github(access_token)
    org = config.credentials.organization
    
    try:
        if org:
            try:
                # Try to get organization and create repo
                organization = github_client.get_organization(org)
                repo = organization.create_repo(
                    name=repository_name,
                    private=is_private,
                    auto_init=True,
                    gitignore_template="Python"
                )
            except github.GithubException as e:
                if e.status == 404:
                    # Fall back to creating personal repo if org not found
                    print(f"Organization {org} not found, creating personal repository instead")
                    repo = github_client.get_user().create_repo(
                        name=repository_name,
                        private=is_private,
                        auto_init=True,
                        gitignore_template="Python"
                    )
                else:
                    raise e
        else:
            # Create personal repository
            repo: Repository = github_client.get_user().create_repo(
                name=repository_name,
                private=is_private,
                auto_init=True,
                gitignore_template="Python"
            )
        
        # Create kavia-main branch from main branch
        try:
            setup_default_branch_safe(repo)
            
        except Exception as e:
            print(f"Warning: Could not set kavia-main as default branch: {str(e)}")
            # Continue with the repository creation even if branch operations fail
        
        repository_metadata = {
            'service': 'github',
            'repositoryName': repo.name,
            'repositoryId': str(repo.id),
            'cloneUrlHttp': repo.clone_url,
            'cloneUrlSsh': repo.ssh_url,
            'organization': org if org else None,
            'encrypted_scm_id': config.encrypted_scm_id,
            'repositoryStatus': 'initialized'
        }
        
        # Update README content
        readme = repo.get_contents("README.md")
        repo.update_file(
            path="README.md",
            message="Update README",
            content="# Project Repository\n\nThis is the initial README file for the project.",
            sha=readme.sha,
            branch="kavia-main"
        )
        
        # Update .gitignore content if needed
        gitignore = repo.get_contents(".gitignore")
        repo.update_file(
            path=".gitignore",
            message="Update .gitignore",
            content=GIT_IGNORE_CONTENT,
            sha=gitignore.sha,
            branch="kavia-main"
        )
        
        return repository_metadata
        
    except Exception as e:
        print(f"Error creating GitHub repository: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create GitHub repository: {str(e)}"
        )

# curl --location 'https://api.github.com/user/repos?per_page=30&page=2' \
# --header 'Authorization: Bearer <YOUR-TOKEN>' \
# --header 'X-GitHub-Api-Version: 2022-11-28'
async def get_github_repositories(
    access_token: str, 
    page: int = 1, 
    per_page: int = 30, 
    search: Optional[str] = None,
    organization: Optional[str] = None,
    visibility: Optional[str] = None
) -> dict:
    headers = {
        "Accept": "application/vnd.github+json",
        "Authorization": f"Bearer {access_token}",
        "X-GitHub-Api-Version": "2022-11-28"
    }
    
    # Build search query with filters
    search_query = []
    if search and search.strip():
        search_query.append(search.strip())
    if organization:
        search_query.append(f"org:{organization}")
    if visibility:
        search_query.append(f"is:{visibility}")
    
    # Always add user:@me if no organization specified
    if not organization:
        search_query.append("user:@me")
    
    # If we have any search criteria, use the search API
    if search_query:
        query = "+".join(search_query)
        api_url = f"https://api.github.com/search/repositories?q={query}&per_page={per_page}&page={page}"
        response = requests.get(api_url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            repositories = data.get("items", [])
            total_count = data.get("total_count", 0)
            total_pages = ceil(total_count / per_page) if total_count > 0 else 1
            
            return {
                "repositories": repositories,
                "total_count": total_count,
                "pagination": {
                    "current_page": page,
                    "per_page": per_page,
                    "total_pages": total_pages
                }
            }
    else:
        # Default repository listing API with visibility filter if specified
        api_url = f"https://api.github.com/user/repos?per_page={per_page}&page={page}"
        if visibility:
            api_url += f"&visibility={visibility}"
            
        response = requests.get(api_url, headers=headers)
        
        if response.status_code == 200:
            repositories = response.json()
            
            total_count = len(repositories)
            total_pages = 1
            
            if "Link" in response.headers:
                link_header = response.headers["Link"]
                last_page_match = re.search(r'page=(\d+)>; rel="last"', link_header)
                if last_page_match:
                    total_pages = int(last_page_match.group(1))
                    if page < total_pages:
                        total_count = (total_pages - 1) * per_page + len(repositories)
            
            return {
                "repositories": repositories,
                "total_count": total_count,
                "pagination": {
                    "current_page": page,
                    "per_page": per_page,
                    "total_pages": total_pages
                }
            }
    
    raise HTTPException(
        status_code=response.status_code, 
        detail=response.json().get("message", "Unknown error fetching GitHub repositories")
    )

async def get_gitlab_repositories(
    access_token: str, 
    page: int = 1, 
    per_page: int = 30, 
    search: Optional[str] = None,
    organization: Optional[str] = None,
    visibility: Optional[str] = None
) -> dict:
    headers = {
        "Authorization": f"Bearer {access_token}"
    }
    
    # Build base GitLab API URL with filters
    api_url = f"https://gitlab.com/api/v4/projects?page={page}&per_page={per_page}&order_by=id&sort=desc"
    
    # Add filters
    if organization:
        api_url += f"&owned=false&search_namespaces=true&group={organization}"
    else:
        api_url += "&owned=true"
    if visibility:
        api_url += f"&visibility={visibility}"
    if search and search.strip():
        api_url += f"&search={search.strip()}"
    
    response = requests.get(api_url, headers=headers)

    if response.status_code == 200:
        repositories = response.json()
        total_count = int(response.headers.get("x-total", len(repositories)))
        total_pages = int(response.headers.get("x-total-pages", 1))
        
        return {
            "repositories": repositories,
            "total_count": total_count, 
            "pagination": {
                "current_page": page,
                "per_page": per_page,
                "total_pages": total_pages
            }
        }
    else:
        raise HTTPException(
            status_code=response.status_code, 
            detail=response.json().get("message", "Unknown error fetching GitLab repositories")
        )

async def get_github_repository(repository_name: str, access_token: str, organization: str ) -> dict:

    headers = {
        "Accept": "application/vnd.github+json",
        "Authorization": f"Bearer {access_token}",
        "X-GitHub-Api-Version": "2022-11-28"
    }

    api_url= f"https://api.github.com/repos/{organization}/{repository_name}"
    response = requests.get(api_url, headers=headers)
    if response.status_code == 200:
        return response.json()
    else:
        raise HTTPException(status_code=response.status_code, detail=response.json().get("message", "Unknown error"))



async def get_github_branches(repository_name: str, access_token: str, organization: str, page: int = 1, per_page: int = 30) -> dict:
    headers = {
        "Accept": "application/vnd.github+json",
        "Authorization": f"Bearer {access_token}",
        "X-GitHub-Api-Version": "2022-11-28"
    }
    
    api_url = f"https://api.github.com/repos/{organization}/{repository_name}/branches"
    params = {
        "per_page": per_page,
        "page": page
    }
    
    response = requests.get(api_url, headers=headers, params=params)
    
    if response.status_code == 200:
        branches_data = response.json()
        
        # Calculate total count from Link header if available
        total_count = len(branches_data)
        total_pages = 1
        
        if "Link" in response.headers:
            link_header = response.headers["Link"]
            last_page_match = re.search(r'page=(\d+)>; rel="last"', link_header)
            if last_page_match:
                total_pages = int(last_page_match.group(1))
                # Estimate total count if we're not on the last page
                if page < total_pages:
                    total_count = (total_pages - 1) * per_page + len(branches_data)
        
        # Format the response
        return {
            "branches": branches_data,
            "total_count": total_count,
            "pagination": {
                "current_page": page,
                "per_page": per_page,
                "total_pages": total_pages
            }
        }
    else:
        raise HTTPException(
            status_code=response.status_code, 
            detail=response.json().get("message", "Unknown error")
        )

# #curl -L \
#   -H "Accept: application/vnd.github+json" \
#   -H "Authorization: Bearer <YOUR-TOKEN>" \
#   -H "X-GitHub-Api-Version: 2022-11-28" \
#   https://api.github.com/repos/OWNER/REPO/git/ref/REF

async def get_a_ref(repository_name: str, access_token: str, organization: str, source_branch: str) -> dict:
    headers = {
        "Accept": "application/vnd.github+json",
        "Authorization": f"Bearer {access_token}",
        "X-GitHub-Api-Version": "2022-11-28"
    }
    ref = f"refs/heads/{source_branch}"
    api_url = f"https://api.github.com/repos/{organization}/{repository_name}/git/{ref}"
    response = requests.get(api_url, headers=headers)
    if response.status_code == 200:
        return response.json()
    else:
        raise HTTPException(status_code=response.status_code, detail=response.json().get("message", "Unknown error"))

# curl -L \
#   -X POST \
#   -H "Accept: application/vnd.github+json" \
#   -H "Authorization: Bearer <YOUR-TOKEN>" \
#   -H "X-GitHub-Api-Version: 2022-11-28" \
#   https://api.github.com/repos/OWNER/REPO/git/refs \
#   -d '{"ref":"refs/heads/featureA","sha":"aa218f56b14c9653891f9e74264a383fa43fefbd"}'

async def create_a_ref(repository_name: str, access_token: str, organization: str, ref: str, sha: str) -> dict:
    headers = {
        "Accept": "application/vnd.github+json",
        "Authorization": f"Bearer {access_token}",
        "X-GitHub-Api-Version": "2022-11-28"
    }
    api_url = f"https://api.github.com/repos/{organization}/{repository_name}/git/refs"
    data = {
        "ref": ref,
        "sha": sha
    }
    response = requests.post(api_url, headers=headers, json=data)
    if response.status_code == 201:
        return response.json()
    else:
        raise HTTPException(status_code=response.status_code, detail=response.json().get("message", "Unknown error"))
    

async def create_gitlab_repository(
    repository_name: str,
    config: SCMConfiguration,
    is_private: bool = True
) -> dict:
    gl = gitlab.Gitlab(
        config.api_url or 'https://gitlab.com',
        oauth_token=config.credentials.access_token
    )
    gl.auth()
    
    visibility = 'private' if is_private else 'public'
    
    if config.credentials.organization:
        try:
            group = gl.groups.get(config.credentials.organization)
            project = group.projects.create({
                'name': repository_name,
                'visibility': visibility,
                'initialize_with_readme': True
            })
        except Exception as e:
            project = gl.projects.create({
                'name': repository_name,
                'visibility': visibility,
                'initialize_with_readme': True
            })
    else:
        project = gl.projects.create({
            'name': repository_name,
            'visibility': visibility,
            'initialize_with_readme': True
        })

    # Create kavia-main branch from main branch and set as default
    try:
        # Get the main branch
        main_branch = project.branches.get('main')
        
        # Create kavia-main branch from main
        project.branches.create({
            'branch': 'kavia-main',
            'ref': 'main'
        })
        
        # Set kavia-main as default branch
        project.default_branch = 'kavia-main'
        project.save()
        
        # Delete the main branch
        main_branch.delete()
        
    except Exception as e:
        print(f"Warning: Could not set kavia-main as default branch for GitLab: {str(e)}")
        # Continue with the repository creation even if branch operations fail

    # Update .gitignore
    try:
        project.files.create({
            'file_path': '.gitignore',
            'branch': 'kavia-main',
            'content': GIT_IGNORE_CONTENT,
            'commit_message': 'Add .gitignore file'
        })
    except:
        # .gitignore might already exist from template
        try:
            f = project.files.get('.gitignore', ref='kavia-main')
            f.content = GIT_IGNORE_CONTENT
            f.save(branch='kavia-main', commit_message='Update .gitignore file')
        except:
            # If kavia-main branch doesn't exist, try main branch
            try:
                f = project.files.get('.gitignore', ref='main')
                f.content = GIT_IGNORE_CONTENT
                f.save(branch='main', commit_message='Update .gitignore file')
            except:
                print("Warning: Could not update .gitignore file")

    repository_metadata = {
        'service': 'gitlab',
        'repositoryName': project.name,
        'repositoryId': str(project.id),
        'cloneUrlHttp': project.http_url_to_repo,
        'cloneUrlSsh': project.ssh_url_to_repo,
        'organization': config.credentials.organization,
        'encrypted_scm_id': config.encrypted_scm_id,
        'repositoryStatus': 'initialized'
    }
    
    return repository_metadata

# Pydantic model for repository download request
class DownloadRepoRequest(BaseModel):
    scm_type: str
    owner: str
    repo: str
    access_token: SecretStr  # Using SecretStr to protect the token in logs
    file_extension: str = "zip"  

def download_repo(scm: SCMType, owner: str, repo: str, access_token: str, file_extension: str):
    """Downloads a repository from GitHub or GitLab as a .zip or .tar file and returns a streaming response."""
    if scm == "github":
        url = f"https://api.github.com/repos/{owner}/{repo}/{file_extension}ball"
        headers = {"Authorization": f"token {access_token}"}
    elif scm == "gitlab":
        url = f"https://gitlab.com/api/v4/projects/{owner}%2F{repo}/repository/archive.{file_extension}"
        headers = {"PRIVATE-TOKEN": access_token}
    else:
        raise ValueError("scm must be either 'github' or 'gitlab'")

    try:
        response = requests.get(url, headers=headers, stream=True)
        response.raise_for_status()  # Raise exception for 4XX/5XX responses
        
        # Return a streaming response to the client
        return response
    except requests.exceptions.RequestException as e:
        # Handle network errors, timeouts, etc.
        error_message = str(e)
        if hasattr(e, 'response') and e.response is not None:
            status_code = e.response.status_code
            error_message = f"{error_message}: {e.response.text}"
        else:
            status_code = 500
        raise HTTPException(status_code=status_code, detail=error_message)

@router.post("/download_repo/")
async def download_repository(request: DownloadRepoRequest):
    try:
        # Extract the plain text value from SecretStr
        access_token = request.access_token.get_secret_value()
        
        # Get the streamed response from the SCM provider
        response = download_repo(
            request.scm_type, 
            request.owner, 
            request.repo, 
            access_token, 
            request.file_extension
        )
        
        # Create a streaming response to send to the client
        filename = f"{request.repo}.{request.file_extension}"
        
        # Set the appropriate content type based on file extension
        content_type = "application/zip" if request.file_extension == "zip" else "application/x-tar"
        
        # Define a generator function to stream the content in chunks
        def stream_content():
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:  # filter out keep-alive new chunks
                    yield chunk
        
        # Return streaming response with the appropriate headers
        return StreamingResponse(
            stream_content(),
            media_type=content_type,
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )
    except ValueError as e:
        # Handle validation errors raised within download_repo
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/create_repository/{project_id}/")
async def create_repository(
    project_id: int, 
    container_id: int,
    scm_id: str,
    repository_name: Optional[str] = None,
    is_private: bool = True,
    force_new: bool = False,
    db: NodeDB = Depends(get_node_db),
    current_user: dict = Depends(get_current_user),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    project = await db.get_node_by_id(project_id)
    project_details = project.get("properties")
    
    if not project_details or get_node_type(project_details.get("Type")) != "Project":
        raise HTTPException(status_code=404, detail="Project not configured")
    
    container = await db.get_node_by_id(container_id)
    container_details = container.get("properties")
    
    if not container:
        raise HTTPException(status_code=404, detail="Container not configured")
    
    # Check if container repository already exists
    project_repositories = json.loads(project_details.get("repositories", "{}"))
    if str(container_id) in project_repositories and not force_new:
        return {"repository": project_repositories[str(container_id)]}
    
    # Generate default repository name if not provided
    if not repository_name:
        repository_name = name_to_slug(
            f"{project_details.get('Title', project_details.get('Name'))}"
        )
        repository_name = f'{repository_name}-{project.get("id")}-{container.get("id")}'
    
    tenant_id = current_user.get("custom:tenant_id")
    config = scm_manager.get_configuration(tenant_id, scm_id=scm_id)
    if not config:
        raise HTTPException(status_code=404, detail="No SCM configuration found")

    if config.scm_type == SCMType.GITLAB:
        repository_metadata = await create_gitlab_repository(
            repository_name=repository_name,
            config=config,
            is_private=is_private
        )
    elif config.scm_type == SCMType.GITHUB:
        repository_metadata = await create_github_repository(
            repository_name=repository_name,
            config=config,
            is_private=is_private
        )
    else:
        raise HTTPException(status_code=400, detail="Unsupported SCM service")

    # Update project node with container repository details
    project_repositories[str(container_id)] = repository_metadata
    await db.update_node_by_id(project_id, {"repositories": json.dumps(project_repositories)})
    logger.info(f"Repository created for project {project_id} with container {container_id}")

    repo_branch_request: RepoBranchRequest = RepoBranchRequest(
        repo_name=f"{repository_metadata.get('organization')}/{repository_metadata.get('repositoryName')}",
        branch_name="kavia-main",
        repo_type="private",
        repo_id=repository_metadata.get("repositoryId"),
        associated=True
    )

    codebase_import_request: CodebaseImportRequest = CodebaseImportRequest(
        project_id=project_id,
        repositories=[repo_branch_request],
        encrypted_scm_id=repository_metadata.get("encrypted_scm_id")
    )
    
    background_tasks.add_task(import_codebase, codebase_import_request, False, current_user)
    return {"repository": repository_metadata}

@router.post("/link_repository/{project_id}/")
async def link_repository(
    project_id: int,
    container_id: int,
    repository_metadata: dict,
    db: NodeDB = Depends(get_node_db),
    current_user: dict = Depends(get_current_user),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    project = await db.get_node_by_id(project_id)
    project_details = project.get("properties")
    
    if not project_details or get_node_type(project_details.get("Type")) != "Project":
        raise HTTPException(status_code=404, detail="Project not configured")
    
    container = await db.get_node_by_id(container_id)
    if not container:
        raise HTTPException(status_code=404, detail="Container not configured")
    
    # Load existing repositories or initialize empty dict
    project_repositories = json.loads(project_details.get("repositories", "{}"))
    branch_name = container.get("properties", {}).get("branch_name", "kavia-main")
    repo_branch_request: RepoBranchRequest = RepoBranchRequest(
        repo_name=f"{repository_metadata.get('organization')}/{repository_metadata.get('repositoryName')}",
        branch_name=branch_name,
        repo_type="private",
        repo_id=str(repository_metadata.get("repositoryId")),
        associated=True
    )
    codebase_import_request: CodebaseImportRequest = CodebaseImportRequest(
        project_id=project_id,
        repositories=[repo_branch_request],
        encrypted_scm_id=repository_metadata.get("encrypted_scm_id")
    )
    # Update project node with new repository details, overwriting if exists
    project_repositories[str(container_id)] = repository_metadata
    await db.update_node_by_id(project_id, {"repositories": json.dumps(project_repositories)})
    logger.info(f"Importing codebase for project {project_id} with container {container_id}")
    logger.info(f"Codebase import request: {codebase_import_request} and branchRequest: {repo_branch_request}")
    background_tasks.add_task(import_codebase, codebase_import_request, False, current_user)
    
    return {"repository": repository_metadata}

@router.get("/get_repository/{project_id}/")
async def get_repository(project_id: int, container_id: int, db: NodeDB = Depends(get_node_db)):
    try:
        project = await db.get_node_by_id(project_id)
        project_details = project.get("properties")
        
        if not project_details or get_node_type(project_details.get("Type")) != "Project":
            raise HTTPException(status_code=404, detail="Project not configured")
        
        # Load the repositories JSON string into a dictionary
        project_repositories = json.loads(project_details.get("repositories", "{}"))
        
        # Get repository metadata for the specific container
        repository_metadata = project_repositories.get(str(container_id))
        if not repository_metadata:
            return {"error": "No repository exists for the given container."}
        
        return {"repository": repository_metadata}
    except json.JSONDecodeError:
        return {"error": "Invalid repository data format"}
    except Exception as e:
        return {"error": f"No repository exists for the given project: {str(e)}"}

@router.get("/list_repositories/{scm_type}")
async def list_repositories(
    scm_type: SCMType,
    scm_id: Optional[str] = None,
    search: Optional[str] = None,  # Add search parameter
    organization: Optional[str] = None,
    visibility: Optional[str] = None,
    page: int = 1,
    per_page: int = 30,
    current_user: dict = Depends(get_current_user),
    db: NodeDB = Depends(get_node_db)
):
    try:
        tenant_id = current_user.get("custom:tenant_id")
        if not tenant_id:
            tenant_id = get_tenant_id()
        
        # Get SCM configurations
        if scm_id:
            configs = [scm_manager.get_configuration(tenant_id, scm_id)]
            if not configs[0]:
                raise HTTPException(status_code=404, detail=f"SCM configuration with ID {scm_id} not found")
        else:
            configs = scm_manager.get_configuration(tenant_id)
            configs = [config for config in configs if config.scm_type == scm_type]
            if not configs:
                raise HTTPException(status_code=404, detail=f"No {scm_type} configurations found")

        if not isinstance(configs, list):
            configs = [configs]

        organizations_repos = {}
        total_pages = 1  # Default value
        
        for config in configs:
            try:
                if config.scm_type == SCMType.GITHUB:
                    access_token = config.credentials.access_token
                    repositories_response = await get_github_repositories(access_token, page, per_page, search, organization, visibility)
                    repositories = repositories_response.get("repositories", [])
                    total_count = repositories_response.get("total_count", 0)
                    pagination = repositories_response.get("pagination", {})
                    formatted_repositories = []
                    for repo in repositories:
                        repo_info = {
                            "repositoryName": repo.get("name"),
                            "repositoryId": repo.get("id"),
                            "path": repo.get("full_name"),
                            "web_url": repo.get("html_url"),
                            "organization": repo.get("owner", {}).get("login") or repo.get("full_name").split("/")[0],
                            "description": repo.get("description"),
                            "default_branch": repo.get("default_branch"),
                            "visibility": repo.get("visibility"),
                            "ssh_url": repo.get("ssh_url"),
                            "http_url": repo.get("clone_url"),
                            "created_at": repo.get("created_at"),
                            "last_activity_at": repo.get("updated_at"),
                            "scm_type": "github",
                        }
                        formatted_repositories.append(repo_info)

                    return {
                        "status": "success",
                        "message": "Successfully retrieved repositories for SCMType.GITHUB",
                        "data": {
                            "repositories": formatted_repositories,
                            "total_repositories": total_count,
                            "pagination": pagination
                        }
                    }
                elif config.scm_type == SCMType.GITLAB:
                    try:
                        access_token = config.credentials.access_token
                        repositories_response = await get_gitlab_repositories(access_token, page, per_page, search, organization, visibility)
                        repositories = repositories_response.get("repositories", [])
                        total_count = repositories_response.get("total_count", 0)
                        pagination = repositories_response.get("pagination", {})
                        formatted_repositories = []
                        for repo in repositories:
                            repo_info = {
                                "repositoryName": repo.get("name"),
                                "repositoryId": repo.get("id"),
                                "path_with_namespace": repo.get("path_with_namespace"),
                                "path": repo.get("path"),
                                "web_url": repo.get("web_url"),
                                "organization": repo.get("namespace", {}).get("name") or repo.get("path_with_namespace", "").split("/")[0],
                                "description": repo.get("description"),
                                "default_branch": repo.get("default_branch"),
                                "visibility": repo.get("visibility"),
                                "ssh_url": repo.get("ssh_url_to_repo"),
                                "http_url": repo.get("http_url_to_repo"),
                                "created_at": repo.get("created_at"),
                                "last_activity_at": repo.get("last_activity_at"),
                                "scm_type": "gitlab",
                            }
                            formatted_repositories.append(repo_info)

                        return {
                            "status": "success",
                            "message": "Successfully retrieved repositories for SCMType.GITLAB",
                            "data": {
                                "repositories": formatted_repositories,
                                "total_repositories": total_count,
                                "pagination": pagination
                            }
                        }
                    except Exception as e:
                        print(f"Error processing SCM configuration: {str(e)}")
                        raise HTTPException(status_code=500, detail=str(e))
            except Exception as e:
                print(f"Error processing SCM configuration: {str(e)}")
                continue
        
    except Exception as e:
        print(f"Error in list_repositories: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/list_project_repositories/{project_id}/")
async def list_project_repositories(project_id: int, db: NodeDB = Depends(get_node_db)):
    try:
        project = await db.get_node_by_id(project_id)
        project_details = project.get("properties")
        
        if not project_details or get_node_type(project_details.get("Type")) != "Project":
            raise HTTPException(status_code=404, detail="Project not configured")
        
        # Load the repositories JSON string into a dictionary
        project_containers = await db.get_system_context_with_containers(project_id)
        project_repositories = json.loads(project_details.get("repositories", "{}"))
        
        return {"repositories": project_repositories, "containers": project_containers}
    except json.JSONDecodeError:
        return {"error": "Invalid repository data format"}
    except Exception as e:
        return {"error": f"Error fetching project repositories: {str(e)}"}

@router.put("/update_repository/{project_id}/")
async def update_repository(
    project_id: int,
    request: RepositoryUpdateRequest,
    db: NodeDB = Depends(get_node_db)
):
    try:
        project = await db.get_node_by_id(project_id)
        project_details = project.get("properties")
        
        if not project_details or get_node_type(project_details.get("Type")) != "Project":
            raise HTTPException(status_code=404, detail="Project not configured")
        
        project_repositories = json.loads(project_details.get("repositories", "{}"))
        repository_metadata = project_repositories.get(str(request.container_id))
        
        if not repository_metadata:
            return JSONResponse(
                status_code=404,
                content={"error": "No repository exists for the given container."}
            )
        
        service = repository_metadata.get('service', '').lower()
        old_name = repository_metadata.get('repositoryName')
        
        if service == 'codecommit':
            codecommit_client = get_codecommit_client()
            response = codecommit_client.update_repository_name(
                oldName=old_name,
                newName=request.new_name
            )
            print(response)
                 # Get the updated repository details
            repo_details = codecommit_client.get_repository(
                repositoryName=request.new_name
            )
            
            # Extract repository metadata from the response
            repository_metadata.update({
                'repositoryName': repo_details['repositoryMetadata']['repositoryName'],
                'cloneUrlHttp': repo_details['repositoryMetadata']['cloneUrlHttp'],
                'cloneUrlSsh': repo_details['repositoryMetadata']['cloneUrlSsh'],
                'repositoryId': repo_details['repositoryMetadata']['repositoryId'],
                'repositoryStatus': 'initialized'
            })
        
        project_repositories[str(request.container_id)] = repository_metadata
        await db.update_node_by_id(
            project_id,
            {"repositories": json.dumps(project_repositories)}
        )
        
        return {"repository": repository_metadata}
        
    except Exception as e:
        print(e)
        return JSONResponse(
            status_code=500,
            content={"error": f"Error updating repository: {str(e)}"}
        )
        
@router.delete("/delete_repository/{project_id}/")
async def delete_repository(
    project_id: int, 
    container_id: int, 
    db: NodeDB = Depends(get_node_db)
):
    try:
        project = await db.get_node_by_id(project_id)
        project_details = project.get("properties")
        
        if not project_details or get_node_type(project_details.get("Type")) != "Project":
            raise HTTPException(status_code=404, detail="Project not configured")
        
        # Load the repositories JSON string into a dictionary
        project_repositories = json.loads(project_details.get("repositories", "{}"))
        
        # Check if repository exists
        repository_metadata = project_repositories.get(str(container_id))
        if not repository_metadata:
            return JSONResponse(
                status_code=404, 
                content={"error": "No repository exists for the given container."}
            )
        
        # Delete from the actual service (CodeCommit or GitHub)
        service = repository_metadata.get('service', '').lower()
        repository_name = repository_metadata.get('repositoryName')
        
        if service == 'codecommit':
            codecommit_client = get_codecommit_client()
            try:
                codecommit_client.delete_repository(
                    repositoryName=repository_name
                )
            except codecommit_client.exceptions.RepositoryDoesNotExistException:
                pass  # Repository already deleted from CodeCommit
                
        # elif service == 'github':
        #     github_client = get_github_client()
        #     org = github_client.get_organization("Kavia-ai")
        #     try:
        #         repo = org.get_repo(repository_name)
        #         repo.delete()
        #     except:
        #         pass  # Repository already deleted from GitHub
        
        # Remove from project's repositories
        del project_repositories[str(container_id)]
        
        # Update project node with updated repositories list
        await db.update_node_by_id(
            project_id, 
            {"repositories": json.dumps(project_repositories)}
        )
        
        return {"message": f"Repository {repository_name} successfully deleted"}
        
    except json.JSONDecodeError:
        return JSONResponse(
            status_code=400, 
            content={"error": "Invalid repository data format"}
        )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": f"Error deleting repository: {str(e)}"}
        )

@router.get("/list_branches/{project_id}/")
async def list_branches(
    project_id: int, 
    container_id: int,
    db: NodeDB = Depends(get_node_db)
):
    try:
        project = await db.get_node_by_id(project_id)
        project_details = project.get("properties")
        
        if not project_details:
            raise HTTPException(status_code=404, detail="Project not found")
            
        project_repositories = json.loads(project_details.get("repositories", "{}"))
        repository_metadata = project_repositories.get(str(container_id))
        
        if not repository_metadata:
            raise HTTPException(status_code=404, detail="Repository not found")
            
        

        # Create boto3 Session with credentials
        session = boto3.Session(
        aws_access_key_id=os.environ.get("AWS_ACCESS_KEY_ID_MAIN"),
        aws_secret_access_key=os.environ.get("AWS_SECRET_ACCESS_KEY_MAIN"),
        region_name='us-east-1'
        )
        
        codecommit_client = session.client('codecommit')
        
        
        try:
            # Get repository details
            repo_response = codecommit_client.get_repository(
                repositoryName=repository_metadata['repositoryName']
            
            )
            repo_details = {
                'repositoryName': repo_response['repositoryMetadata']['repositoryName'],
                'repositoryId': repo_response['repositoryMetadata']['repositoryId'],
                'creationDate': repo_response['repositoryMetadata']['creationDate'],
                'lastModifiedDate': repo_response['repositoryMetadata']['lastModifiedDate'],
                'cloneUrlHttp': repo_response['repositoryMetadata']['cloneUrlHttp']
            }

            # Get branches
            branches_response = codecommit_client.list_branches(
                repositoryName=repository_metadata['repositoryName']
            )
            
            # Get deployment configuration if exists
            configured_branch = None
            try:
                deployment_node = await get_deployment_node(project_id, container_id, db)
                if deployment_node and 'deployment_config' in deployment_node['properties']:
                    deployment_config = json.loads(deployment_node['properties']['deployment_config'])
                    configured_branch = deployment_config.get('branch')
            except Exception as e:
                print(f"Could not get deployment configuration: {str(e)}")

            branch_details = []
            for branch_name in branches_response['branches']:
                # Skip branches that don't match configured branch if one is set
                # if configured_branch and branch_name != configured_branch:
                #     continue
                    
                branch_info = codecommit_client.get_branch(
                    repositoryName=repository_metadata['repositoryName'],
                    branchName=branch_name
                )
                
                commit_id = branch_info['branch']['commitId']
                
                commit_details = codecommit_client.get_commit(
                    repositoryName=repository_metadata['repositoryName'],
                    commitId=commit_id
                )
                
                branch_details.append({
                    'name': branch_name,
                    'lastCommitId': commit_id,
                    'lastCommitMessage': commit_details['commit']['message'],
                    'isConfigured': branch_name == configured_branch if configured_branch else False
                })

            response = {
                "repository": repo_details,
                "branches": branch_details,
                "configuredBranch": configured_branch
            }

            # If a specific branch was configured but not found, add a warning
            if configured_branch and not any(b['name'] == configured_branch for b in branch_details):
                response["warning"] = f"Configured branch '{configured_branch}' not found in repository"
                
            return response
            
        except Exception as e:
            raise HTTPException(
                status_code=500, 
                detail=f"Error retrieving repository details: {str(e)}"
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/all_branches/{project_id}/")
async def list_all_branches(
    project_id: int, 
    container_id: int,
    db: NodeDB = Depends(get_node_db),
    current_user: dict = Depends(get_current_user),
    page: int = 1,
    per_page: int = 30
):
    try:
        tenant_id = current_user.get("custom:tenant_id")
        project = await db.get_node_by_id(project_id)
        project_details = project.get("properties")
        
        if not project_details:
            raise HTTPException(status_code=404, detail="Project not found")
            
        project_repositories = json.loads(project_details.get("repositories", "{}"))
        repository_metadata = project_repositories.get(str(container_id))
        
        if not repository_metadata:
            raise HTTPException(status_code=404, detail="Repository not found")
        access_token = None
        service = repository_metadata.get('service', '').lower()
        repository_name = repository_metadata.get('repositoryName')
        access_token_path = repository_metadata.get('access_token_path')
        if access_token_path == ACCESS_TOKEN_PATH.KAVIA_MANANGED.value:
            access_token = settings.GITHUB_ACCESS_TOKEN
        else:
            decrypted_scm_id = decrypt_string(repository_metadata.get('encrypted_scm_id'))
            config = scm_manager.get_configuration(tenant_id, scm_id=decrypted_scm_id)
            access_token = config.credentials.access_token
        branch_details = []
        
      
        if service == 'github':
            
            organization = repository_metadata.get('organization')
            try:
                response = await get_github_branches(repository_name, access_token, organization, page, per_page)
                return response
            except Exception as e:
                raise HTTPException(
                    status_code=500,
                    detail=f"Error fetching GitHub branches: {str(e)}"
                )
                    

   
        elif service == 'gitlab':
            try:
                gl = gitlab.Gitlab(
                    config.api_url or 'https://gitlab.com',
                    oauth_token=config.credentials.access_token
                )
                gl.auth()
                
                project = gl.projects.get(repository_metadata['repositoryId'])
                
                repo_details = {
                    'repositoryName': project.name,
                    'repositoryId': str(project.id),
                    'creationDate': project.created_at,
                    'lastModifiedDate': project.last_activity_at,
                    'cloneUrlHttp': project.http_url_to_repo
                }

                branches = project.branches.list(all=True)
                branch_details = []
                
                for branch in branches:
                    commit = project.commits.get(branch.commit['id'])
                    branch_details.append({
                        'name': branch.name,
                        'lastCommitId': branch.commit['id'],
                        'lastCommitMessage': commit.message
                    })
                    
            except Exception as e:
                raise HTTPException(
                    status_code=500,
                    detail=f"Error retrieving GitLab branches: {str(e)}"
                )

        else:
            raise HTTPException(
                status_code=400,
                detail="Unsupported repository service"
            )

        return {
            "service": service,
            "repositoryName": repository_name,
            "branches": branch_details
        }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/create_branch/{project_id}/")
async def create_branch(
    project_id: int,
    container_id: int,
    new_branch_name: str,
    source_branch: str = "kavia-main",
    db: NodeDB = Depends(get_node_db),
    current_user: dict = Depends(get_current_user)
):
    try:
        tenant_id = current_user.get("custom:tenant_id")
        project = await db.get_node_by_id(project_id)
        project_details = project.get("properties")
        
        if not project_details:
            raise HTTPException(status_code=404, detail="Project not found")
            
        project_repositories = json.loads(project_details.get("repositories", "{}"))
        repository_metadata = project_repositories.get(str(container_id))
        
        if not repository_metadata:
            raise HTTPException(status_code=404, detail="Repository not found")
            
        service = repository_metadata.get('service', '').lower()
        repository_name = repository_metadata.get('repositoryName')
        decrypted_scm_id = decrypt_string(repository_metadata.get('encrypted_scm_id'))
        config = scm_manager.get_configuration(tenant_id, scm_id=decrypted_scm_id)
   
        if service == 'github':
            try:
                # Get the source branch reference
                print("calling get_a_ref with repository_name: ", repository_name, "organization: ", repository_metadata.get('organization'), "source_branch: ", source_branch)
                source_branch_ref = await get_a_ref(repository_name, config.credentials.access_token, repository_metadata.get('organization'), source_branch)
                print(source_branch_ref)
                response = await create_a_ref(repository_name, config.credentials.access_token, repository_metadata.get('organization'), f"refs/heads/{new_branch_name}", source_branch_ref['object']['sha'])
                if response:
                    return {
                        "message": f"Branch '{new_branch_name}' created successfully from '{source_branch}'",
                        "branch_name": new_branch_name,
                        "source_branch": source_branch,
                        "commit_id": source_branch_ref['object']['sha'],
                        "service": service,
                        "repositoryName": repository_name,
                        "status": "success"
                    }
                
            except Exception as e:
                if e.status == 404:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Source branch '{source_branch}' does not exist in the repository"
                    )
                else:
                    raise HTTPException(
                        status_code=e.status,
                        detail=f"Error accessing source branch: {e.data.get('message', str(e))}"
                    )

        elif service == 'gitlab':
            gl = gitlab.Gitlab(
                config.api_url or 'https://gitlab.com',
                oauth_token=config.credentials.access_token
            )
            gl.auth()

            project = gl.projects.get(repository_metadata['repositoryId'])
            
            # Verify source branch exists
            try:
                source_branch_obj = project.branches.get(source_branch)
            except:
                raise HTTPException(
                    status_code=400,
                    detail=f"Source branch '{source_branch}' does not exist in the repository"
                )
            
            # Create new branch
            branch = project.branches.create({
                'branch': new_branch_name,
                'ref': source_branch
            })
            
            return {
                "message": f"Branch '{new_branch_name}' created successfully from '{source_branch}'",
                "branch_name": new_branch_name,
                "source_branch": source_branch,
                "commit_id": branch.commit['id'],
                "service": service,
                "repositoryName": repository_name,
                "status": "success"
            }
        else:
            raise HTTPException(
                status_code=400,
                detail="Unsupported repository service"
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/change_default_branch/{project_id}/")
async def change_default_branch(
    project_id: int,
    container_id: int,
    new_default_branch: str,
    db: NodeDB = Depends(get_node_db)
):
    try:
        project = await db.get_node_by_id(project_id)
        project_details = project.get("properties")
        
        if not project_details:
            raise HTTPException(status_code=404, detail="Project not found")
            
        project_repositories = json.loads(project_details.get("repositories", "{}"))
        repository_metadata = project_repositories.get(str(container_id))
        
        if not repository_metadata:
            raise HTTPException(status_code=404, detail="Repository not found")
            
        service = repository_metadata.get('service', '').lower()
        repository_name = repository_metadata.get('repositoryName')
        
        if service == 'codecommit':
            codecommit_client = get_codecommit_client()
            try:
                # First verify the branch exists
                branches_response = codecommit_client.list_branches(
                    repositoryName=repository_name
                )
                
                if new_default_branch not in branches_response['branches']:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Branch '{new_default_branch}' does not exist in the repository"
                    )
                
                # Change default branch
                response = codecommit_client.update_default_branch(
                    repositoryName=repository_name,
                    defaultBranchName=new_default_branch
                )
                
            except Exception as e:
                raise HTTPException(
                    status_code=500,
                    detail=f"Error changing CodeCommit default branch: {str(e)}"
                )
                
        elif service == 'github':
            github_client = get_github_client()
            try:
                org = github_client.get_organization("Kavia-ai")
                repo = org.get_repo(repository_name)
                
                # Verify branch exists
                try:
                    repo.get_branch(new_default_branch)
                except:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Branch '{new_default_branch}' does not exist in the repository"
                    )
                
                # Change default branch
                repo.edit(default_branch=new_default_branch)
                
            except Exception as e:
                raise HTTPException(
                    status_code=500,
                    detail=f"Error changing GitHub default branch: {str(e)}"
                )
        else:
            raise HTTPException(
                status_code=400,
                detail="Unsupported repository service"
            )
            
        return {
            "message": f"Default branch successfully changed to {new_default_branch}",
            "service": service,
            "repositoryName": repository_name,
            "status": "success"
        }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 

@router.get("/repositories/{task_id}")
async def get_repositories(
    task_id: str,
    mongo_handler: MongoDBHandler = Depends(get_mongo_db)
) -> list[dict]:
    try:
        db = mongo_handler.db
        task = db[TASKS_COLLECTION_NAME].find_one({"_id": task_id})
        
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        # Handle code-gen tasks
        if task_id.startswith("cg"):
            project_details = task.get("project_details")
            if not project_details:
                raise HTTPException(status_code=404, detail="Project details not found")
            
            project_details = json.loads(project_details)
            repository = project_details.get("current_repository")
            if not repository:
                raise HTTPException(status_code=404, detail="Repository not found")
                
            
            return [repository]
        
        # Handle code-maintenance tasks
        elif task_id.startswith("cm"):
            project_id = task.get("project_id")
            if not project_id:
                raise HTTPException(status_code=404, detail="Project ID not found")
                
            # Get repositories for the project
            project_repositories = db["project_repositories"].find_one({"project_id": project_id})
            if not project_repositories:
                raise HTTPException(status_code=404, detail="Project repositories not found")
                
            repositories = project_repositories.get("repositories", [])
            
            # Format each repository to match required structure
            formatted_repositories = [format_repository(repo) for repo in repositories]
            return formatted_repositories
        
        # Handle other types of tasks
        else:
            task_details = task.get("properties")
            if not task_details:
                raise HTTPException(status_code=404, detail="Task details not found")
            
            repositories = task_details.get("repositories")
            if not repositories:
                raise HTTPException(status_code=404, detail="Repositories not found")
            
            # Format each repository to match required structure
            formatted_repositories = [format_repository(repo) for repo in repositories]
            return formatted_repositories
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

def format_repository(repo):
    """
    Formats repository data to match the required structure:
    {
        'service': 'github',
        'repositoryName': repo.name,
        'repositoryId': str(repo.id),
        'cloneUrlHttp': repo.clone_url,
        'cloneUrlSsh': repo.ssh_url,
        'organization': org if org else None,
        'encrypted_scm_id': config.encrypted_scm_id,
        'repositoryStatus': 'initialized'
    }
    """
    # Extract organization from repository name if available
    repo_name = repo.get("repository_name", "")
    org = None
    if "/" in repo_name:
        parts = repo_name.split("/")
        if len(parts) >= 2:
            org = parts[0]
            name = parts[1]
        else:
            name = repo_name
    else:
        name = repo_name
    
    # Map fields from the repository data to the required format
    return {
        "service": repo.get("service"),
        "repositoryName": name,
        "repositoryId": str(repo.get("repo_id")),
        "cloneUrlHttp": repo.get("git_url"),
        "cloneUrlSsh": repo.get("clone_url_ssh"),
        "organization": org,
        "encrypted_scm_id": repo.get("scm_id"),
        "repositoryStatus": repo.get("repositoryStatus", "initialized")
    }

class DownloadRepoRequestV2(BaseModel):
    scm_type: SCMType = Field(..., description="Type of SCM (github or gitlab)")
    scm_id: str = Field(..., description="SCM ID for authentication")
    owner: str = Field(..., description="Repository owner")
    repo: str = Field(..., description="Repository name")
    file_extension: str = Field("zip", description="File extension for download (zip or tar)")

@router.post("/download_repo/v2")
async def download_repository_v2(
    request: DownloadRepoRequestV2,
    branch: str = Query(..., description="Branch to download")
):
    try:
        # Get SCM configuration and access token using scm_id
        config = scm_manager.get_valid_configuration(request.scm_id)
        if not config:
            raise HTTPException(status_code=401, detail="Invalid SCM configuration")
        
        access_token = config.credentials.access_token
        if not access_token:
            raise HTTPException(status_code=401, detail="Invalid or expired SCM credentials")
        
        # Get the streamed response from the SCM provider
        response = download_repo(
            request.scm_type, 
            request.owner, 
            request.repo, 
            access_token, 
            request.file_extension,
            # branch=branch
        )
        
        # Create a streaming response to send to the client
        filename = f"{request.repo}-{branch}.{request.file_extension}"
        
        # Set the appropriate content type based on file extension
        content_type = "application/zip" if request.file_extension == "zip" else "application/x-tar"
        
        # Define a generator function to stream the content in chunks
        def stream_content():
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:  # filter out keep-alive new chunks
                    yield chunk
        
        # Return streaming response with the appropriate headers
        return StreamingResponse(
            stream_content(),
            media_type=content_type,
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error downloading repository: {str(e)}")