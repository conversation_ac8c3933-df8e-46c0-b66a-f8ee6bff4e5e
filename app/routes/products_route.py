import logging
from fastapi import APIRouter, HTTPException
from app.core.Settings import settings
import stripe
from app.utils.product_utils import format_price
from app.models.product_model import ProductListResponse
from typing import List, Optional

stripe.api_key = settings.STRIPE_SECRET_KEY
logger = logging.getLogger(__name__)

SHOW_NAME = "products"
router = APIRouter(
    prefix=f"/{SHOW_NAME}",
    tags=[SHOW_NAME],
    responses={404: {"description": "Not found"}}
)


@router.get("/list")
async def get_products() -> List[ProductListResponse]:
    """
    Retrieve all active Stripe products with pricing information and credits metadata.
    Returns products sorted by price.
    """
    try:
        products = stripe.Product.list(
            active=True,
            expand=["data.default_price"],
            limit=100
        )
        
        product_list_response = []
        
        for product in products.data:
            try:
                if not product.default_price:
                    logger.warning(f"Product {product.id} has no default price, skipping")
                    continue
                
                # Enhanced credits extraction with comma handling
                credits = None
                if product.metadata:
                    # Try multiple possible key names (case insensitive)
                    possible_keys = ['Credits', 'credits', 'CREDITS', 'Credit', 'credit']
                    
                    for key in possible_keys:
                        if key in product.metadata:
                            try:
                                # Remove commas and convert to int
                                credits_str = str(product.metadata[key]).replace(',', '').strip()
                                credits = int(credits_str)
                                logger.info(f"Found credits for {product.id} using key '{key}': {credits}")
                                break
                            except (ValueError, TypeError) as e:
                                logger.warning(f"Invalid credits value for {product.id} with key '{key}': {product.metadata[key]} - {e}")
                    
                    # If still not found, check if any key contains the word "credit"
                    if credits is None:
                        for key, value in product.metadata.items():
                            if 'credit' in key.lower():
                                try:
                                    # Remove commas and convert to int
                                    credits_str = str(value).replace(',', '').strip()
                                    credits = int(credits_str)
                                    logger.info(f"Found credits for {product.id} using partial match key '{key}': {credits}")
                                    break
                                except (ValueError, TypeError):
                                    continue
                
                # Handle pricing information
                default_price = product.default_price
                is_recurring = bool(default_price.recurring)
                recurring_interval = default_price.recurring.interval if is_recurring else None
                recurring_interval_count = default_price.recurring.interval_count if is_recurring else None
                
                product_response = ProductListResponse(
                    product_id=product.id,
                    product_name=product.name,
                    product_description=product.description or "",
                    price_id=default_price.id,
                    currency=default_price.currency,
                    # Removed: unit_amount and unit_amount_decimal
                    price=format_price(default_price.unit_amount_decimal),
                    is_recurring=is_recurring,
                    recurring_interval=recurring_interval,
                    recurring_interval_count=recurring_interval_count,
                    credits=credits
                )
                
                product_list_response.append(product_response)
                
            except Exception as e:
                logger.error(f"Error processing product {product.id}: {str(e)}")
                continue
        
        # Sort by price (but we still need it for sorting, so keep the logic)
        product_list_response.sort(key=lambda x: int(float(x.price)) * 100)  # Convert price back to cents for sorting
        
        return product_list_response
        
    except stripe.error.StripeError as e:
        logger.error(f"Stripe API error: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch products from Stripe")
    except Exception as e:
        logger.error(f"Unexpected error in get_products: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")