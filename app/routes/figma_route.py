# app/routes/figma_route.py
from llm_wrapper.core.llm_interface import LLMInterface
from fastapi import APIRouter, Depends, HTTPException, Query,UploadFile, File
from fastapi.responses import JSONResponse, StreamingResponse
from typing import Optional, List, Dict, Any
from pydantic import BaseModel
from app.utils.auth_utils import get_current_user as get_current_user_v1
from app.models.figma_model import FrameLink
from app.connection.establish_db_connection import get_node_db, NodeDB
from app.utils.figma_utils import (
    extract_file_key,
    extract_frame_data,
    fetch_frame_images,
    fetch_frame_image,
    get_frame_details,
    get_figma_access_token,
    get_figma_file_data_limited,
    figma_access_token,
)
from app.core.constants import FIGMA_BASE_PATH
from app.utils.kg_inspect.knowledge_reporter import Reporter
from app.celery_app import celery_task_id, get_websocket_session, create_websocket_session,cleanup_websocket_session
from app.core.websocket import websocket_manager
import requests
import io
import zipfile
import json
import re
import base64
import time
from app.models.uiux.figma_model import FigmaModel, UserModel, FigmaRequestModel
from app.models.project.project_model import ProjectModel
from datetime import datetime
from app.connection.tenant_middleware import get_tenant_id
from app.models.tenant.settings_model import TenantSettings
from app.classes.S3Handler import S3Handler
from app.core.Settings import settings
from app.models.uiux.figma_model import (
    FigmaSizesModel,
    ProcessingStatus,
    FigmaFrameModel,
)
import asyncio
from fastapi import BackgroundTasks
import httpx
from app.utils.figma_utils import (
    get_figma_file_data_limited_async,
    fetch_frame_images_async,
    process_frame
)
from app.core.websocket.client import WebSocketClient
from app.connection.establish_db_connection import get_mongo_db
from app.utils.datetime_utils import generate_timestamp

from app.discussions.figma.tools.extraction import Extraction
import uuid
from fastapi import Body
import os

from pydantic import BaseModel
from app.models.figma_model import FigmaExtractionRequest, ImageTemplate, ExtractionTypes

from sse_starlette.sse import EventSourceResponse

from copy import copy, deepcopy

from app.core.constants import FIGMA_BASE_PATH as BASE_PATH
from app.tasks import Task, process_figma_in_celery
import traceback
from app.discussions.figma.tools.work_input_discovery_tool import WorkInputDiscovery
from llm_wrapper.core.llm_interface import LLMInterface
from pydantic import Field
from app.telemetry.logger_config import get_logger, setup_logging
from sse_starlette.sse import ServerSentEvent
from app.utils.file_utils.image_compressor import compress_image

setup_logging()

_SHOW_NAME = "figma"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}},
)

def convert_png_to_bas64url(png_path: str):
    with open(png_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')
    
# figma_access_token.set("*********************************************")


async def get_current_user(current_user=Depends(get_current_user_v1)):
    tenant_id = get_tenant_id()
    settings = await TenantSettings.get_settings(tenant_id)
    figma_api_key = next(
        (
            setting.value
            for setting in settings.integrations.figma
            if setting.name == "figma_api_key"
        ),
        "",
    )
    figma_access_token.set(figma_api_key)
    return current_user


def get_figma_file_fize(figma_data):
    json_str = json.dumps(figma_data)
    size_bytes = len(json_str.encode("utf-8"))
    size_kb = size_bytes / 1024
    size_mb = size_kb / 1024
    return {
        "size_kb": round(size_kb, 2),
        "size_mb": round(size_mb, 2),
        "byte_limit": None,
        "mb_limit": None,
    }


async def process_figma_file(
    project_id: str, figma_link: FigmaRequestModel, is_new_file: bool = True
):
    """
    Common function to process Figma file for both add and update operations
    """
    tenant_id = get_tenant_id()
    file_key = extract_file_key(figma_link.url)
    file_name = f"{tenant_id}-{project_id}-{file_key}.json"

    if not file_key:
        raise HTTPException(status_code=400, detail="Invalid Figma link")

    s3_handler = S3Handler(tenant_id)

    # Check if file exists (only for new files)
    # if is_new_file and s3_handler.is_file(file_name):
    #     raise HTTPException(status_code=400, detail="Design already linked to this project")

    # Get and process Figma file data
    data, sizes = get_figma_file_data_limited(
        figma_link.url, get_figma_access_token(), mb_limit=2
    )
    frames = extract_frame_data(data)
    frame_ids = [frame["id"] for frame in frames]
    image_urls = fetch_frame_images(file_key, frame_ids)

    frames_with_images = [
        {**frame, "imageUrl": image_urls.get(frame["id"])} for frame in frames
    ]

    file_data = {
        "frames": frames_with_images,
        "fileKey": file_key,
        "document": data["document"],
        "sizes": sizes,
    }

    # Handle S3 storage
    if is_new_file:
        s3_handler.add_file(file_name, json.dumps(file_data))
    else:
        s3_handler.update_file(file_name, json.dumps(file_data))

    return file_data, sizes



# Helper function to retry image fetch
async def retry_fetch_image(
    file_key: str, frame_id: str, max_retries: int = 3
) -> Optional[str]:
    """
    Retry fetching frame image with exponential backoff.

    Args:
        file_key (str): Figma file key
        frame_id (str): Frame ID
        max_retries (int): Maximum number of retry attempts

    Returns:
        Optional[str]: Image URL if successful, None otherwise
    """
    for attempt in range(max_retries):
        try:
            url = f"https://api.figma.com/v1/images/{file_key}"
            params = {"ids": frame_id, "scale": 2, "format": "png"}
            headers = {"X-Figma-Token": get_figma_access_token()}

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    url, params=params, headers=headers, timeout=10.0
                )
                if response.status_code == 200:
                    data = response.json()
                    return data["images"].get(frame_id)

                # Rate limit handling
                if response.status_code == 429:
                    retry_after = int(response.headers.get("Retry-After", 5))
                    await asyncio.sleep(retry_after)
                    continue

        except Exception:
            pass

        # Exponential backoff
        await asyncio.sleep(2**attempt)

    return None


async def background_process_figma_file(
    project_id: str,
    figma_link: FigmaRequestModel,
    tenant_id: str,
    figma_api_key: str,
    is_new_file: bool = True,
):
    """Background task to process Figma file with frame status tracking"""
    print("Background task started")
    file_logger = get_logger(__name__)
    processed_frames = []
    completed_count = 0
    failed_count = 0
    total_frames = 0
    data = None

    try:
        figma_access_token.set(figma_api_key)
        file_key = extract_file_key(figma_link.url)
        figma_id = f"{tenant_id}-{project_id}-{file_key}"

        llm = LLMInterface(
            f"{BASE_PATH}/{project_id}/{tenant_id}/{figma_id}",
            instance_name=f"figma_discussion_{project_id}",
            user_id=tenant_id,
            project_id=project_id,
            agent_name="FigmaExtractionAgent"
        )

        work_input_discovery : WorkInputDiscovery = WorkInputDiscovery(
            callback_functions=None,
            base_path=f"{BASE_PATH}/{project_id}/{tenant_id}/{figma_id}",
            logger=file_logger,
            llm=llm

        )
        ws_client = WebSocketClient(f"figma-{project_id}", uri=settings.WEBSOCKET_URI)
        ws_client.connect()
   

        # Use async client for HTTP requests
        async with httpx.AsyncClient() as client:
            # Process Figma file data first to get total frames count
            data, sizes = await get_figma_file_data_limited_async(
                client, figma_link.url, figma_api_key, mb_limit=5
            )
            frames = extract_frame_data(data)
            total_frames = len(frames)

            # Update FigmaModel with initial frame count and status
            update_data = {
                "total_frames": total_frames,
                "completed_frames": 0,
                "failed_frames": 0,
                "sizes": FigmaSizesModel(**sizes).dict(),
                "time_updated": generate_timestamp(),
            }
            await FigmaModel.update(figma_id, update_data)

            # Process frames and track counts
            frame_ids = [frame["id"] for frame in frames]
            image_urls = await fetch_frame_images_async(client, file_key, frame_ids)
            has_errors = False

            for frame in frames:
                try:
                    # Process frame with status tracking
                    frame_model = await process_frame(frame, file_key, image_urls)

                    # Convert to format matching process_figma_file
                    # Ensure absoluteBoundingBox is properly structured
                    bounding_box = frame.get("absoluteBoundingBox", {})
                    if not bounding_box:
                        # Provide default dimensions if missing
                        bounding_box = {
                            "x": 0,
                            "y": 0,
                            "width": 800,  # default width
                            "height": 600,  # default height
                        }

                    processed_frame = {
                        "id": frame["id"],
                        "name": frame["name"],
                        "type": frame["type"],
                        "absoluteBoundingBox": bounding_box,
                        "imageUrl": (
                            frame_model.imageUrl
                            if frame_model.status == ProcessingStatus.COMPLETED
                            else None
                        ),
                        "status": frame_model.status,
                        "error_message": (
                            frame_model.error_message
                            if hasattr(frame_model, "error_message")
                            else None
                        ),
                        "time_updated": frame_model.time_updated,
                        # Add fields needed for frontend
                        "dimensions": {
                            "width": round(bounding_box.get("width", 800)),
                            "height": round(bounding_box.get("height", 600)),
                        },
                    }

                    processed_frames.append(processed_frame)

                    if frame_model.status == ProcessingStatus.COMPLETED:
                        completed_count += 1
                    elif frame_model.status == ProcessingStatus.FAILED:
                        failed_count += 1
                        has_errors = True

                    if(FigmaSizesModel(**sizes).model_dump()['size_kb'] >= 6000):
                        status = ProcessingStatus.FAILED
                    else:
                        status = ProcessingStatus.PROCESSING

                    # Update MongoDB with current counts
                    update_data = {
                        "total_frames": total_frames,
                        "completed_frames": completed_count,
                        "failed_frames": failed_count,
                        "time_updated": generate_timestamp(),
                        "sizes": FigmaSizesModel(**sizes).dict(),
                        "status": status,
                    }
                    if has_errors:
                        update_data["status"] = ProcessingStatus.PARTIALLY_COMPLETED
                    ws_client.send_message(
                        "figma_update",
                        {"figma_id": figma_id, "update_data": update_data},
                    )
                    await FigmaModel.update(figma_id, update_data)

                except Exception as e:
                    print(f"Error processing frame {frame['id']}: {str(e)}")
                    failed_count += 1
                    has_errors = True

            # Store frames data in S3 in the same format as process_figma_file
            file_data = {
                "frames": processed_frames,
                "fileKey": file_key,
                "document": data["document"],
                "sizes": sizes,
                "progress": {
                    "total": total_frames,
                    "completed": completed_count,
                    "failed": failed_count,
                },
            }
            hashes, work_item = work_input_discovery.process_figma_json(file_data)
            update_data["work_input_sh256"] = hashes
            update_data["work_input"] = work_item
            s3_handler = S3Handler(tenant_id)
            await s3_handler.update_file_async(
                f"{figma_id}.json", json.dumps(file_data)
            )

        # Final update
        final_status = (
            ProcessingStatus.COMPLETED
            if failed_count == 0
            else ProcessingStatus.PARTIALLY_COMPLETED
        )
        final_update = {
            "status": final_status,
            "error_message": (
                f"{failed_count} frames failed to process" if failed_count > 0 else None
            ),
            "time_updated": generate_timestamp(),
        }
        ws_client.send_message(
            "figma_update", {"figma_id": figma_id, "update_data": final_update}
        )
        await FigmaModel.update(figma_id, final_update)

    except Exception as e:
        print(f"Error in background task: {str(e)}")
        # Store any successfully processed frames
        if processed_frames and data:
            file_data = {
                "frames": processed_frames,
                "fileKey": file_key,
                "document": data["document"],
                "sizes": sizes,
                "progress": {
                    "total": total_frames,
                    "completed": completed_count,
                    "failed": failed_count,
                },
            }
            s3_handler = S3Handler(tenant_id)
            await s3_handler.update_file_async(
                f"{figma_id}.json", json.dumps(file_data)
            )

            # Update status to partially completed if any frames were processed
            if completed_count > 0:
                error_update = {
                    "status": ProcessingStatus.PARTIALLY_COMPLETED,
                    "error_message": str(e),
                    "time_updated": generate_timestamp(),
                }
            else:
                error_update = {
                    "status": ProcessingStatus.FAILED,
                    "error_message": str(e),
                    "time_updated": generate_timestamp(),
                }
            ws_client.send_message(
                "figma_update", {"figma_id": figma_id, "update_data": error_update}
            )
            await FigmaModel.update(figma_id, error_update)
    finally:
        ws_client.disconnect()

def classify_exception_status_code(exception: Exception) -> int:
    """
    Classify exceptions and return appropriate HTTP status codes.
    
    Args:
        exception: The caught exception
        
    Returns:
        int: Appropriate HTTP status code
    """
    exception_type = type(exception).__name__
    exception_str = str(exception).lower()
    
    # Network/HTTP related errors (502 Bad Gateway)
    if isinstance(exception, (httpx.ConnectError, httpx.TimeoutException, httpx.NetworkError)):
        return 502
    
    # HTTP client errors from external APIs
    if isinstance(exception, httpx.HTTPStatusError):
        # For Figma API authentication errors, return 403
        if exception.response.status_code == 403 and 'figma.com' in str(exception.request.url):
            return 403
        # For other 4xx errors from external APIs that indicate client errors
        elif 400 <= exception.response.status_code < 500:
            return exception.response.status_code
        # For 5xx errors from external APIs, return 502 (bad gateway)
        else:
            return 502
    
    # Authentication/Authorization errors (401/403)
    if any(keyword in exception_str for keyword in ['unauthorized', 'forbidden', 'authentication', 'permission']):
        return 401 if 'unauthorized' in exception_str or 'authentication' in exception_str else 403
    
    # Validation/Bad Request errors (400)
    if (isinstance(exception, (ValueError, TypeError)) or 
        any(keyword in exception_str for keyword in ['invalid', 'bad request', 'validation', 'missing required'])):
        return 400
    
    # Resource not found (404)
    if any(keyword in exception_str for keyword in ['not found', 'does not exist', 'no such']):
        return 404
    
    # Conflict errors (409)
    if any(keyword in exception_str for keyword in ['already exists', 'conflict', 'duplicate']):
        return 409
    
    # Rate limiting (429)
    if any(keyword in exception_str for keyword in ['rate limit', 'too many requests', 'quota exceeded']):
        return 429
    
    # Database/Connection errors (503 Service Unavailable)
    if any(keyword in exception_str for keyword in ['database', 'connection', 'timeout', 'unavailable']):
        return 503
    
    # Default to 500 for unclassified exceptions
    return 500

############################################################################################################
@router.post("/add_figma_file_v2")
async def add_figma_file_v2(
    background_tasks: BackgroundTasks,
    project_id: str,
    figma_link: FigmaRequestModel,
    current_user=Depends(get_current_user),
):
    try:
        tenant_id = get_tenant_id()
        file_key = extract_file_key(figma_link.url)

        if not file_key:
            return JSONResponse(
                status_code=400, content={"message": "Invalid Figma link"}
            )

        # Create FigmaModel with all required fields
        figma_id = f"{tenant_id}-{project_id}-{file_key}"

        # Check if design already exists
        existing_design = await FigmaModel.get_one(figma_id)
        if existing_design:
            return JSONResponse(
                status_code=400, content={"message": "Design already exists"}
            )

        # Create FigmaModel data
        user_model = UserModel(
            username=current_user["sub"],
            name=current_user["custom:Name"],
            email=current_user["email"],
        )

        settings = await TenantSettings.get_settings(tenant_id)

        figma_api_key = next(
            (
                setting.value
                for setting in settings.integrations.figma
                if setting.name == "figma_api_key"
            ),
            None,
        )

        if not figma_api_key:
            return JSONResponse(
                status_code=400, content={"message": "Figma API key not configured"}
            )

        async with httpx.AsyncClient() as client:
            # Process Figma file data first to get total frames count
            data, sizes = await get_figma_file_data_limited_async(
                client, figma_link.url, figma_api_key, mb_limit=5
            )

        if(sizes["size_kb"] < 6000):
            status = ProcessingStatus.PENDING
        else:
            status = ProcessingStatus.FAILED

        figma_data = {
            "tenant_id": tenant_id,
            "project_id": project_id,
            "file_key": file_key,
            "id": figma_id,
            "name": figma_link.name,
            "url": figma_link.url,
            "added_by": user_model.dict(),
            "status": status,
            "total_frames": 0,
            "completed_frames": 0,
            "failed_frames": 0,
            "time_created": generate_timestamp(),
            "time_updated": generate_timestamp(),
            "sizes": FigmaSizesModel(**sizes).dict(),
        }

        # First try to add to project
        success = await ProjectModel.add_design_id(project_id, figma_id)
        if not success:
            return JSONResponse(
                status_code=500, content={"message": "Failed to add design to project"}
            )

        # Then create design document
        created_design = await FigmaModel.create(figma_data)
        if not created_design:
            # Cleanup project if design creation fails
            await ProjectModel.remove_design_id(project_id, figma_id)
            return JSONResponse(
                status_code=500, content={"message": "Failed to create design"}
            )

        # Get Figma API key for background task
        try:
            settings = await TenantSettings.get_settings(tenant_id)
            figma_api_key = next(
                (
                    setting.value
                    for setting in settings.integrations.figma
                    if setting.name == "figma_api_key"
                ),
            )
            if figma_api_key and status != ProcessingStatus.FAILED:

                figma_link_dict = {
                    "name": figma_link.name,
                    "url": figma_link.url
                }
                

                # celery_task = Task.schedule_task(
                #     process_figma_in_celery,
                #     project_id=project_id,
                #     figma_link=figma_link_dict,
                #     figma_api_key=figma_api_key,
                #     is_new_file=True,
                #     tenant_id=get_tenant_id(),
                #     current_user=current_user.get("cognito:username"),
                # )
                background_tasks.add_task(
                    background_process_figma_file,
                    project_id,
                    figma_link,
                    tenant_id,
                    figma_api_key,
                    True,
                )
        except Exception as e:
            print(f"Error setting up background task: {str(e)}")  # Just log the error

        return JSONResponse(
            status_code=202,
            content={
                "message": "Figma file processing started",
                "status": ProcessingStatus.PENDING,
                "id": figma_id,
            },
        )

    except Exception as e:
        status_code = classify_exception_status_code(e)
        print(f"Error in add_figma_file_v2: {str(e)}")  # Debug print
        return JSONResponse(
            status_code=status_code, 
            content={"message": f"An error occurred: {str(e)}"}
        )


@router.put("/update_figma_file_v2")
async def update_figma_file_v2(
    background_tasks: BackgroundTasks,
    project_id: str,
    figma_link: FigmaRequestModel,
    current_user=Depends(get_current_user),
):
    try:
        tenant_id = get_tenant_id()
        file_key = extract_file_key(figma_link.url)
        figma_id = f"{tenant_id}-{project_id}-{file_key}"

        # Check if design exists
        existing_design = await FigmaModel.get_one(figma_id)
        if not existing_design:
            return JSONResponse(
                status_code=404, content={"message": "Design not found"}
            )

        # Reset the design status and counters
        update_data = {
            "status": ProcessingStatus.PENDING,
            "total_frames": 0,
            "completed_frames": 0,
            "failed_frames": 0,
            "error_message": None,
            "time_updated": generate_timestamp(),
        }

        updated = await FigmaModel.update(figma_id, update_data)
        if not updated:
            return JSONResponse(
                status_code=500, content={"message": "Failed to update design status"}
            )

        # Get Figma API key and start background process
        try:
            settings = await TenantSettings.get_settings(tenant_id)
            figma_api_key = next(
                (
                    setting.value
                    for setting in settings.integrations.figma
                    if setting.name == "figma_api_key"
                ),
                "",
            )
            if figma_api_key:
                background_tasks.add_task(
                    background_process_figma_file,
                    project_id,
                    figma_link,
                    tenant_id,
                    figma_api_key,
                    False,
                )
        except:
            pass  # Continue even if background task setup fails

        return JSONResponse(
            status_code=202,
            content={
                "message": "Figma file update started",
                "status": ProcessingStatus.PENDING,
                "id": figma_id,
            },
        )

    except Exception as e:
        return JSONResponse(
            status_code=500, content={"message": f"An error occurred: {str(e)}"}
        )


@router.delete("/delete_design/{project_id}/{figma_id}")
async def delete_design(
    project_id: str, figma_id: str, current_user=Depends(get_current_user)
):
    """
    Delete a design including its S3 data and MongoDB records.

    Args:
        project_id (str): The project ID
        figma_id (str): The Figma design ID

    Returns:
        JSONResponse: Result of the deletion operation
    """
    deletion_results = {
        "s3_deleted": False,
        "mongodb_deleted": False,
        "project_ref_removed": False,
    }

    try:
        # First verify the design exists
        existing_design = await FigmaModel.get_one(figma_id)
        if not existing_design:
            return JSONResponse(
                status_code=404,
                content={
                    "message": "Design not found",
                    "id": figma_id,
                    "results": deletion_results,
                },
            )

        # Check if design belongs to project
        if existing_design.get("project_id") != project_id:
            return JSONResponse(
                status_code=400,
                content={
                    "message": "Design does not belong to this project",
                    "id": figma_id,
                    "results": deletion_results,
                },
            )

        # 1. Delete S3 data
        try:
            tenant_id = get_tenant_id()
            file_name = f"{figma_id}.json"
            s3_handler = S3Handler(tenant_id)
            if s3_handler.is_file(file_name):
                s3_handler.delete_file(file_name)
                deletion_results["s3_deleted"] = True
        except Exception as e:
            print(f"Error deleting S3 data: {str(e)}")

        # 2. Delete from figma_designs collection
        try:
            deleted = await FigmaModel.delete(figma_id)
            deletion_results["mongodb_deleted"] = bool(deleted)
        except Exception as e:
            print(f"Error deleting MongoDB data: {str(e)}")

        # 3. Remove reference from project
        try:
            removed = await ProjectModel.remove_design_id(project_id, figma_id)
            deletion_results["project_ref_removed"] = bool(removed)
        except Exception as e:
            print(f"Error removing project reference: {str(e)}")

        # Determine success based on critical operations
        if (
            deletion_results["mongodb_deleted"]
            and deletion_results["project_ref_removed"]
        ):
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Design deleted successfully",
                    "id": figma_id,
                    "results": deletion_results,
                },
            )
        else:
            # If some operations failed but others succeeded
            return JSONResponse(
                status_code=207,  # Multi-Status
                content={
                    "message": "Design deletion partially completed",
                    "id": figma_id,
                    "results": deletion_results,
                },
            )

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "message": f"An error occurred during deletion: {str(e)}",
                "id": figma_id,
                "results": deletion_results,
            },
        )


@router.post("/reload_design/{project_id}/{figma_id}")
async def reload_design(
    background_tasks: BackgroundTasks,
    project_id: str,
    figma_id: str,
    current_user=Depends(get_current_user),
):
    """Reload a Figma design by re-processing all frames"""
    try:
        # Verify design exists
        design = await FigmaModel.get_one(figma_id)
        if not design:
            return JSONResponse(
                status_code=404, content={"message": "Design not found"}
            )

        if design.get("project_id") != project_id:
            return JSONResponse(
                status_code=400,
                content={"message": "Design does not belong to this project"},
            )

        # Reset design status and counters
        update_data = {
            "status": ProcessingStatus.PENDING,
            "total_frames": 0,
            "completed_frames": 0,
            "failed_frames": 0,
            "error_message": None,
            "time_updated": generate_timestamp(),
        }

        updated = await FigmaModel.update(figma_id, update_data)
        if not updated:
            return JSONResponse(
                status_code=500, content={"message": "Failed to update design status"}
            )

        # Get Figma API key
        tenant_id = design.get("tenant_id")
        settings = await TenantSettings.get_settings(tenant_id)
        figma_api_key = next(
            (
                setting.value
                for setting in settings.integrations.figma
                if setting.name == "figma_api_key"
            ),
            None,
        )

        if not figma_api_key:
            return JSONResponse(
                status_code=400, content={"message": "Figma API key not configured"}
            )

        # Prepare figma link model
        figma_link = FigmaRequestModel(
            name=design.get("name", ""), url=design.get("url", "")
        )

        # Add the background task
        # This will be executed after the response is sent
        background_tasks.add_task(
            background_process_figma_file,
            project_id,
            figma_link,
            tenant_id,
            figma_api_key,
            False,
        )

        # Return success response - background task will continue running
        return JSONResponse(
            status_code=202,
            content={
                "message": "Design reload started",
                "status": ProcessingStatus.PENDING,
                "id": figma_id,
            },
        )

    except Exception as e:
        # Update the design status to failed if we encounter an error
        try:
            await FigmaModel.update(
                figma_id,
                {
                    "status": ProcessingStatus.FAILED,
                    "error_message": str(e),
                    "time_updated": generate_timestamp(),
                },
            )
        except:
            pass  # Ignore errors in error handling

        return JSONResponse(
            status_code=500, content={"message": f"An error occurred: {str(e)}"}
        )


@router.get("/stream-status/{project_id}/{figma_id}")
async def stream_status(
    project_id: str,
    figma_id: str,
    stream: bool = Query(
        False,
        description="Stream updates until completion if True, else return current status",
    ),
    current_user=Depends(get_current_user),
):
    async def get_current_status():
        try:
            design = await FigmaModel.get_one(figma_id)
            if not design:
                return {"error": "Design not found", "status": ProcessingStatus.FAILED}

            return {
                "id": figma_id,
                "status": design.get("status", ProcessingStatus.PENDING),
                "total_frames": design.get("total_frames", 0),
                "completed_frames": design.get("completed_frames", 0),
                "failed_frames": design.get("failed_frames", 0),
                "error_message": design.get("error_message"),
                "time_updated": design.get("time_updated", generate_timestamp()),
            }
        except Exception as e:
            return {"error": str(e), "status": ProcessingStatus.FAILED}

    async def generate_status_events():
        prev_status = None
        retry_count = 0
        max_retries = 60  # 30 seconds with 0.5s sleep

        while retry_count < max_retries:
            try:
                status_data = await get_current_status()

                current_status = (
                    status_data.get("status"),
                    status_data.get("completed_frames"),
                    status_data.get("failed_frames"),
                )

                if current_status != prev_status:
                    yield f"data: {json.dumps(status_data)}\n\n"
                    prev_status = current_status

                if status_data.get("error") or status_data.get("status") in [
                    ProcessingStatus.COMPLETED,
                    ProcessingStatus.FAILED,
                ]:
                    break

                await asyncio.sleep(0.5)
                retry_count += 1

            except Exception as e:
                yield f"data: {json.dumps({'error': str(e), 'status': ProcessingStatus.FAILED})}\n\n"
                break

    if stream:
        return StreamingResponse(
            generate_status_events(), media_type="text/event-stream"
        )
    else:
        status_data = await get_current_status()
        return JSONResponse(content=status_data)


@router.post("/add_figma_file")
async def add_figma_file(
    project_id: str,
    figma_link: FigmaRequestModel,
    current_user=Depends(get_current_user),
    db: NodeDB = Depends(get_node_db),
):
    try:
        file_data, sizes = await process_figma_file(
            project_id, figma_link, is_new_file=True
        )

        # Add the design to the project
        user_model = UserModel(
            username=current_user["sub"],
            name=current_user["custom:Name"],
            email=current_user["email"],
        )
        figma_model = FigmaModel(
            id=project_id,
            name=figma_link.name,
            url=figma_link.url,
            added_by=user_model,
            sizes=FigmaSizesModel(**sizes),
        )

        # Get existing project if it exists
        existing_project = await ProjectModel.get_one(project_id)

        if existing_project:
            designs = existing_project.get("fields", {}).get("designs", [])
            designs.append(figma_model.model_dump())
            project_data = {"_id": project_id, "fields": {"designs": designs}}
        else:
            project_data = {
                "_id": project_id,
                "fields": {"designs": [figma_model.model_dump()]},
            }

        project_model = ProjectModel(**project_data)
        await project_model.upsert(project_data)

        return JSONResponse(content={"message": "Figma file added successfully"})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/delete_figma_file")
async def delete_figma_file(
    project_id: str,
    figma_design: FigmaRequestModel,
    current_user=Depends(get_current_user),
    db: NodeDB = Depends(get_node_db),
):
    try:
        tenant_id = get_tenant_id()
        figma_link = figma_design.url
        # Extract file key and generate filename
        file_key = extract_file_key(figma_link)
        file_name = f"{tenant_id}-{project_id}-{file_key}.json"

        # Delete file from S3
        s3_handler = S3Handler(tenant_id)
        if s3_handler.is_file(file_name):
            s3_handler.delete_file(file_name)

        # Get existing project
        existing_project = await ProjectModel.get_one(project_id)
        if not existing_project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Remove design from project's designs list
        designs = existing_project.get("fields", {}).get("designs", [])
        designs = [d for d in designs if d.get("url") != figma_link]

        # Update project with modified designs list
        project_data = {"_id": project_id, "fields": {"designs": designs}}
        project_model = ProjectModel(**project_data)
        await project_model.upsert(project_data)

        return JSONResponse(content={"message": "Figma file deleted successfully"})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/update_figma_file")
async def update_figma_file(
    project_id: str,
    figma_link: FigmaRequestModel,
    current_user=Depends(get_current_user),
    db: NodeDB = Depends(get_node_db),
):
    try:
        # Verify project exists
        existing_project = await ProjectModel.get_one(project_id)
        if not existing_project:
            raise HTTPException(status_code=404, detail="Project not found")

        file_data, sizes = await process_figma_file(
            project_id, figma_link, is_new_file=False
        )
        # Get existing designs
        designs = existing_project.get("fields", {}).get("designs", [])

        # Update the sizes for the matching design
        for design in designs:
            if design.get("url") == figma_link.url:
                figma_model = FigmaModel(
                    id=design.get("id"),
                    name=design.get("name"),
                    url=design.get("url"),
                    added_by=design.get("added_by"),
                    sizes=FigmaSizesModel(**sizes),
                    time_created=design.get("time_created"),
                    time_updated=generate_timestamp(),
                )
                design.update(figma_model.dict())
                break

        # Update project with modified designs
        project_data = {"_id": project_id, "fields": {"designs": designs}}
        project_model = ProjectModel(**project_data)
        await project_model.upsert(project_data)
        return JSONResponse(
            content={
                "message": "Figma file updated successfully",
                "file_data": file_data,
                "sizes": sizes,
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/get_figma_files")
async def get_figma_files(
    project_id: str,
    current_user=Depends(get_current_user),
    db: NodeDB = Depends(get_node_db),
):
    try:
        print(f"Project ID: {project_id}")
        designs, images  = await FigmaModel.get_by_project(project_id, include_images=True)
        return JSONResponse(content={"designs": designs, "images": images})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/get_figma_file_data")
async def get_figma_file_data(figma_id: str, current_user=Depends(get_current_user)):
    try:
        tenant_id = get_tenant_id()
        s3_handler = S3Handler(tenant_id)
        file_name = f"{figma_id}.json"
        data = s3_handler.get_file(file_name)
        data = json.loads(data.decode("utf-8"))
        return JSONResponse(content=data)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/file")
async def get_figma_file(
    project_id: str,
    figma_link: str,
    current_user=Depends(get_current_user),
    db: NodeDB = Depends(get_node_db),
):
    try:
        tenant_id = get_tenant_id()
        file_key = extract_file_key(figma_link)
        file_name = f"{tenant_id}-{project_id}-{file_key}.json"
        s3_handler = S3Handler(tenant_id)
        if not file_key:
            raise HTTPException(status_code=400, detail="Invalid Figma link")

        if not s3_handler.is_file(file_name):
            raise HTTPException(status_code=404, detail="File not found")

        # data = get_figma_file_data_limited(figma_link, get_figma_access_token(), kb_limit=500)
        data = s3_handler.get_file(file_name)
        data = json.loads(data.decode("utf-8"))

        return JSONResponse(
            content={
                "frames": data["frames"],
                "fileKey": file_key,
                "document": data["document"],
            }
        )
        # return JSONResponse(content={"frames": frames_with_images, "fileKey": file_key, "document": data["document"]})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/frame-image")
async def get_frame_image(
    figma_link: str, frame_id: str, current_user=Depends(get_current_user)
):
    file_key = extract_file_key(figma_link)
    image_url = fetch_frame_image(file_key, frame_id)
    if not image_url:
        raise HTTPException(status_code=404, detail="Frame image not found")
    return JSONResponse(content={"imageUrl": image_url})


@router.get("/download")
async def download_all_frames(figma_link: str, current_user=Depends(get_current_user)):
    file_key = extract_file_key(figma_link)
    if not file_key:
        raise HTTPException(status_code=400, detail="Invalid Figma link")

    print(file_key)
    zip_buffer = io.BytesIO()

    url = f"https://api.figma.com/v1/files/{file_key}"
    headers = {"X-Figma-Token": get_figma_access_token()}

    response = requests.get(url, headers=headers, timeout=300)
    response.raise_for_status()

    data = response.json()
    frames = extract_frame_data(data)

    with zipfile.ZipFile(zip_buffer, "w", zipfile.ZIP_DEFLATED) as zip_file:
        for frame in frames:
            json_content = json.dumps(frame, indent=2)
            zip_file.writestr(f"{frame['name']}/{frame['name']}.json", json_content)

            image_url = fetch_frame_image(file_key, frame["id"])
            if image_url:
                image_response = requests.get(image_url, timeout=300)
                image_response.raise_for_status()
                zip_file.writestr(
                    f"{frame['name']}/{frame['name']}.png", image_response.content
                )

    zip_buffer.seek(0)
    return StreamingResponse(
        iter([zip_buffer.getvalue()]),
        media_type="application/zip",
        headers={
            "Content-Disposition": f"attachment; filename=figma_export_all_frames.zip"
        },
    )


@router.get("/frame-details")
async def get_frame_details_route(
    file_key: str, frame_id: str, current_user=Depends(get_current_user)
):
    frame_details = get_frame_details(file_key, frame_id)
    return JSONResponse(content=frame_details)


@router.get("/frame-preview")
async def get_frame_preview(
    figma_link: str, frame_id: str, current_user=Depends(get_current_user)
):
    file_key = extract_file_key(figma_link)
    url = f"https://api.figma.com/v1/images/{file_key}"
    params = {"ids": frame_id, "scale": 2, "format": "png"}
    headers = {"X-Figma-Token": get_figma_access_token()}

    response = requests.get(url, params=params, headers=headers, timeout=300)
    response.raise_for_status()
    preview_url = response.json()["images"].get(frame_id)

    if not preview_url:
        raise HTTPException(status_code=404, detail="Frame preview not found")
    return JSONResponse(content={"previewUrl": preview_url})


@router.get("/frame-thumbnail")
async def get_frame_thumbnail(
    figma_link: str, frame_id: str, current_user=Depends(get_current_user)
):
    file_key = extract_file_key(figma_link)
    url = f"https://api.figma.com/v1/images/{file_key}"
    params = {"ids": frame_id, "scale": 0.5, "format": "png"}
    headers = {"X-Figma-Token": get_figma_access_token()}

    response = requests.get(url, params=params, headers=headers, timeout=300)
    response.raise_for_status()
    thumbnail_url = response.json()["images"].get(frame_id)

    if not thumbnail_url:
        raise HTTPException(status_code=404, detail="Frame thumbnail not found")
    return JSONResponse(content={"thumbnailUrl": thumbnail_url})


# Update the link-frames endpoint to include image URLs
@router.post("/link-frames")
async def link_frames_to_design(
    frame_link: FrameLink,
    current_user=Depends(get_current_user),
    db: NodeDB = Depends(get_node_db),
):
    file_key = extract_file_key(frame_link.figma_link)
    if not file_key:
        raise HTTPException(status_code=400, detail="Invalid Figma link")

    design_node = await db.get_node_by_label_id(frame_link.design_id, "Design")
    if not design_node:
        raise HTTPException(status_code=404, detail="Design node not found")

    if not frame_link.frame_ids:
        updated_design = await db.update_node_by_id(
            frame_link.design_id, {"LinkedFigmaFrames": "[]"}
        )
        return JSONResponse(
            content={
                "message": "Frames linked successfully",
                "updated_design": updated_design,
            }
        )

    url = f"https://api.figma.com/v1/files/{file_key}/nodes"
    params = {"ids": ",".join(frame_link.frame_ids)}
    headers = {"X-Figma-Token": get_figma_access_token()}

    response = requests.get(url, params=params, headers=headers, timeout=300)
    response.raise_for_status()

    frames_data = response.json()["nodes"]

    # Fetch image URLs for the frames
    image_urls = fetch_frame_images(file_key, frame_link.frame_ids)

    linked_frames = design_node.get("properties", {}).get("LinkedFigmaFrames", [])
    if linked_frames:
        linked_frames = json.loads(linked_frames)
    if isinstance(linked_frames, str):
        linked_frames = json.loads(linked_frames)

    # Create a set of existing frame IDs for quick lookup
    existing_frame_ids = set(frame["id"] for frame in linked_frames)

    for frame_id, frame_data in frames_data.items():
        # Only add the frame if it doesn't already exist
        if frame_id not in existing_frame_ids:
            linked_frames.append(
                {
                    "id": frame_id,
                    "name": frame_data["document"]["name"],
                    "file_key": file_key,
                    "imageUrl": image_urls.get(frame_id),
                    "thumbnailUrl": f"https://api.figma.com/v1/images/{file_key}?ids={frame_id}&scale=0.5&format=png",
                }
            )
            existing_frame_ids.add(frame_id)  # Add the new frame ID to the set

    updated_design = await db.update_node_by_id(
        frame_link.design_id, {"LinkedFigmaFrames": json.dumps(linked_frames)}
    )

    return JSONResponse(
        content={
            "message": "Frames linked successfully",
            "updated_design": updated_design,
        }
    )


@router.post("/link-figma-components")
async def link_figma_components(
    frame_link: FrameLink,
    design_id: str,
    current_user=Depends(get_current_user),
):
    try:
        db = get_node_db()
        design_id = int(design_id)
        if not design_id:
            raise HTTPException(status_code=400, detail="Design ID is required")

        if not frame_link.figma_link:
            raise HTTPException(status_code=400, detail="Figma link is required")

        file_key = extract_file_key(frame_link.figma_link)
        if not file_key:
            raise HTTPException(status_code=400, detail="Invalid Figma link format")

        existing_design = await db.get_node_by_label_id(design_id, "Design")
        if not existing_design:
            raise HTTPException(status_code=404, detail="Design node not found")

        # Get existing components
        existing_components = []
        if existing_design.get("properties", {}).get("FigmaComponents"):
            try:
                existing_components = json.loads(
                    existing_design["properties"]["FigmaComponents"]
                )
                if not isinstance(existing_components, list):
                    existing_components = []
            except json.JSONDecodeError:
                existing_components = []

        if frame_link.unlink:
            # Remove the component if it exists
            existing_components = [
                comp for comp in existing_components if comp.get("file_key") != file_key
            ]
            message = "Figma component unlinked successfully"
        else:
            # Check if component already exists
            component_exists = False
            for component in existing_components:
                if component.get("file_key") == file_key:
                    component.update(
                        {
                            "figma_link": frame_link.figma_link,
                            "updated_at": datetime.utcnow().isoformat(),
                            "name": frame_link.name,  # Update name if changed
                        }
                    )
                    component_exists = True
                    break

            # If component doesn't exist, add it
            if not component_exists:
                new_component = {
                    "file_key": file_key,
                    "figma_link": frame_link.figma_link,
                    "name": frame_link.name,  # Store the name
                    "created_at": datetime.utcnow().isoformat(),
                    "updated_at": datetime.utcnow().isoformat(),
                    "created_by": {
                        "username": current_user.get("sub"),
                        "name": current_user.get("custom:Name"),
                        "email": current_user.get("email"),
                    },
                }
                existing_components.append(new_component)
            message = "Figma component linked successfully"

        # Update the design node
        design_node = await db.update_node_by_id(
            design_id, {"FigmaComponents": json.dumps(existing_components)}
        )

        if not design_node:
            raise HTTPException(
                status_code=500, detail="Failed to update design node with components"
            )

        return JSONResponse(
            content={
                "message": message,
                "updated_design": design_node,
                "components": existing_components,
            }
        )

    except HTTPException as e:
        raise e
    except json.JSONDecodeError:
        raise HTTPException(
            status_code=400, detail="Invalid JSON format in existing components"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"An unexpected error occurred: {str(e)}"
        )


@router.post("/check_design_in_db/{tenant_id}/{project_id}")
async def check_design_in_db(tenant_id: str, project_id: str, figmaDesigns: List[Dict]):
    try:
        deleted_info = []
        mongo_handler = get_mongo_db(
            db_name=(settings.MONGO_DB_NAME), collection_name="figma_designs"
        )
        for design in figmaDesigns:
            foundDesign = await mongo_handler.get_one(
                filter={"file_key": design["file_key"], "project_id": project_id},
                db=mongo_handler.db,
            )
            if foundDesign:
                deleted = False
            else:
                deleted = True
            deleted_info.append({design["file_key"]: deleted})
        return {"data": deleted_info}
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"An unexpected error occurred: {str(e)}"
        )

class StartExtractionRequest(BaseModel):
    selected_design_id:str
    session_name:str = Field(default="Untitled")

@router.post("/start_discussion")
async def start_discussion(
    project_id: int,
    request: StartExtractionRequest,
    extraction_type: ExtractionTypes = ExtractionTypes.Figma,
    current_user=Depends(get_current_user),
):
    try:
        selected_design_id = request.selected_design_id
        session_name=request.session_name

        discussion_id = Extraction.start_discussion(project_id=project_id, 
                                                    selected_design_id=selected_design_id,
                                                    session_name=session_name,
                                                    extraction_type=extraction_type)
        
        return JSONResponse(content={"discussion_id": discussion_id})
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"An unexpected error occurred: {str(e)}"
        )

@router.post("/figma_extraction")
async def figma_extraction(
    request: FigmaExtractionRequest,
    current_user=Depends(get_current_user)
):
    discussion_id = request.discussion_id
    user_request = request.user_request
    user_message = user_request.user_message
    file_attachments = user_request.file_attachments
    selected_frame = request.selected_frame
    # Use discussion_id as the task_id
    task_id = discussion_id
    
    discussion = Extraction(discussion_id)
    if file_attachments:
        discussion.set_file_attachments(file_attachments)
    discussion.set_current_user(current_user=current_user.get("cognito:username"))
    if selected_frame:
        discussion.set_selected_frame(selected_frame)
    await discussion._initialize()
    await discussion.load_figma_data()
    end_response = {"stop": True}
    
    # Create WebSocket session
    ws_client = create_websocket_session(task_id, metadata={
        'task_type': 'figma_extraction',
        'discussion_id': discussion_id,
        'user_id': current_user.get("cognito:username")
    })
    
    # Send initial connection message
    ws_client.send_message("connected", {
        "task_id": task_id,
        "status": "processing"
    })
    
    # This function will process the discussion and send via WebSocket
    async def process_discussion():
        try:
            async for llm_response in discussion.main_discussion(user_message, stream=True,ws_client=ws_client):
                # Send to WebSocket
                ws_client.send_message("data", llm_response)
        except Exception as e:
            import traceback
            traceback.print_exc()
            error_msg = f"Error: {str(e)}"
            print(error_msg)
            # Send error to WebSocket
            ws_client.send_message("error", {"message": error_msg})
        finally:
            # Send end response to WebSocket
            ws_client.send_message("end", {"end":True})
            print(f"DEBUG: End Messsage : {end_response}")
            # Clean up WebSocket session
            # Delay cleanup by 5 seconds
            await asyncio.sleep(5)
            cleanup_websocket_session(task_id)
    
    # Start the processing in a background task
    asyncio.create_task(process_discussion())
    
    # Return the task_id for client to connect to WebSocket
    return {
        "task_id": task_id,
        "status": "processing in background"
    }
@router.get("/register_agent/{session_id}")
def register_agent(session_id: str):
    try:
        # Initialize WebSocket client and reporter
        ws_client = WebSocketClient(session_id, settings.WEBSOCKET_URI)
        reporter = Reporter(ws_client)
        reporter.initialize()
        
        return {
            "status": "success",
            "message": f"Agent successfully registered with session ID: {session_id}",
            "session_id": session_id
        }
        
    except ConnectionError as e:
        return {
            "status": "error",
            "message": f"Failed to establish WebSocket connection: {str(e)}",
            "session_id": session_id
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Failed to register agent: {str(e)}",
            "session_id": session_id
        }

@router.get("/messages_history")
async def messages_history(discussion_id: str, current_user=Depends(get_current_user)):
    messages = Extraction.get_messages_history(discussion_id)
    return JSONResponse(content={"messages": messages})


@router.get("/past_discussions/{project_id}")
async def past_discussions(
    project_id: int,
    selected_design_id: str = None,
    current_user=Depends(get_current_user),
):
    print(f"Project ID: {project_id}")
    discussions = Extraction.get_past_discussions(project_id, selected_design_id)
    return JSONResponse(content={"discussions": discussions})


@router.get("/files/{discussion_id}")
async def files_content(discussion_id: str):
    """
    Returns a list of files generated for a specific Figma extraction discussion.

    Args:
        discussion_id (str): The unique identifier for the discussion

    Returns:
        JSONResponse: List of files with their paths and content
    """
    try:
        # Create the base directory path for this discussion
        base_dir = f"{BASE_PATH}/{discussion_id}"

        # Check if directory exists
        if not os.path.exists(base_dir):
            return JSONResponse(
                status_code=404,
                content={"message": f"No files found for discussion {discussion_id}"},
            )

        # Walk through the directory to find all files ignore logs folder and .log files
        file_list = []
        for root, dirs, files in os.walk(base_dir):
            for file in files:
                if file.endswith(".log") or file.startswith("logs"):
                    continue
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path, base_dir)

                # Get file stats
                stats = os.stat(file_path)

                # Read file content if it's not too large
                content = ""
                if stats.st_size < 1024 * 1024:  # Limit to files < 1MB
                    try:
                        with open(file_path, "r") as f:
                            content = f.read()
                    except UnicodeDecodeError:
                        # Handle binary files
                        content = "(Binary file content not shown)"
                else:
                    content = f"(File too large: {stats.st_size / 1024:.2f} KB)"
                # if its a design_file.html, then make it index.html
                # if file == "design_file.html":
                #     file = "index.html"
                file_list.append(
                    {
                        "name": file,
                        "path": relative_path,
                        "full_path": file_path,
                        "size": stats.st_size,
                        "modified": stats.st_mtime,
                        "content": content,
                    }
                )

        # Sort files by name for consistency
        file_list.sort(key=lambda x: x["path"])

        return JSONResponse(
            status_code=200,
            content={
                "discussion_id": discussion_id,
                "base_path": base_dir,
                "files": file_list,
                "file_count": len(file_list),
            },
        )

    except Exception as e:
        return JSONResponse(
            status_code=500, content={"message": f"Error retrieving files: {str(e)}"}
        )


IMAGE_TEMPLATE = {
    "figma_ext_id" : "",
    "added_by" : {
        "username" : "",
        "name" : "",
        "email" : ""
    },
    "images" : [],
    "completed_frames" : 0,
    "failed_frames" : 0,
    "file_key" : "",
    "name" : "",
    "project_id" : "",
    "status" : "",
    "tenant_id" : "",
    "time_created" : "",
    "time_updated" : "",
    "total_frames" : 0,
    "path" : "",
    "error_message" : ""
}


@router.get("/get_ext_images/{project_id}/{image_ext_id}")
async def get_ext_images(project_id: str, image_ext_id: str ):
    image = get_mongo_db().db["figma_ext_images"].find_one({"project_id": project_id, "figma_ext_id": image_ext_id})
    if image:
        image.pop("_id")
        return JSONResponse(content={"image": image})
    else:
        return JSONResponse(content={"image": None})
    
class MergeChangesRequest(BaseModel):
    project_id: str
    discussion_id: str
    figma_ext_id: str
    type_of : str
@router.post("/merge_changes")
async def merge_changes(
    request: MergeChangesRequest,
    current_user=Depends(get_current_user),
    node_db: NodeDB = Depends(get_node_db)
):
    """
    Stores the file_path to the appropriate collection when a merge is performed.
    Also updates the project node in node_db with assets information.

    Args:
        request (MergeChangesRequest): The merge request data
        current_user: The authenticated user
        node_db: Database for node operations

    Returns:
        JSONResponse: Status of the merge operation
    """
    try:
        mongo_db = get_mongo_db().db
        if request.type_of == "image":
            collection = "figma_ext_images"
        elif request.type_of == "figma":
            collection = "figma_designs"
        else:
            return JSONResponse(
                status_code=400, 
                content={"message": "Type should be of image or figma"}
            )

        file_path = f"{BASE_PATH}/{request.discussion_id}/.assets"
        
        # Construct a query that uses the correct ID field based on type
        query = {"project_id": request.project_id}
        if request.type_of == "image":
            query["figma_ext_id"] = request.figma_ext_id
        else:  # figma type
            query["id"] = request.figma_ext_id
        
        # Find the existing record
        record = mongo_db[collection].find_one(query)
        
        if not record:
            return JSONResponse(
                status_code=404,
                content={"message": f"No record found for project {request.project_id} and ID {request.figma_ext_id}"}
            )
        
        # Create merged_by user info
        merged_by_info = {
            "username": current_user["sub"],
            "name": current_user["custom:Name"],
            "email": current_user["email"]
        }
        
        # Update data with the new merge information
        current_time = generate_timestamp()
        
        # If there are already merged files, add this one to the list
        if "merged_files" in record:
            # Add new file to the list of merged files
            update_data = {
                "time_updated": current_time,
                "$push": {
                    "merged_files": {
                        "extraction_path": file_path,
                        "file_path": file_path,
                        "discussion_id": request.discussion_id,
                        "merged_by": merged_by_info,
                        "merged_at": current_time
                    }
                }
            }
        else:
            # Create the initial merged_files array
            update_data = {
                "time_updated": current_time,
                "merged_files": [{
                    "extraction_path": file_path,
                    "file_path": file_path,
                    "discussion_id": request.discussion_id,
                    "merged_by": merged_by_info,
                    "merged_at": current_time
                }]
            }
        
        # Update the MongoDB record
        if "merged_files" in record:
            # Use update with $push operator when adding to existing array
            mongo_db[collection].update_one(
                query,
                {"$set": {"time_updated": current_time}, 
                 "$push": update_data["$push"]}
            )
        else:
            # Use regular update when creating the initial array
            mongo_db[collection].update_one(
                query,
                {"$set": update_data}
            )
        
        # Update project node in node_db with assets information
        import json
        
        # Try several ways to get the project node
        project_id = int(request.project_id)  # Ensure it's an integer
        
        # First try without specifying node type (more permissive)
        project_node = await node_db.get_node_by_id(project_id)
        
        # If that fails, try with a direct query that doesn't check is_active
        if not project_node:
            query = """
            MATCH (n) 
            WHERE ID(n) = $node_id 
            RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties
            """
            query_result = await node_db.async_run(query, node_id=project_id)
            result = query_result.data()
            if result and len(result) > 0:
                project_node = result[0]
            else:
                return JSONResponse(
                    status_code=404,
                    content={"message": f"No project node found for project {request.project_id}"}
                )
        
        # Get current assets or initialize empty dict
        assets = {}
        if "assets" in project_node["properties"] and project_node["properties"]["assets"]:
            try:
                assets = json.loads(project_node["properties"]["assets"])
            except json.JSONDecodeError:
                # If assets data is not valid JSON, start with empty dict
                assets = {}
        
        # Update assets with the new design information
        design_id = request.figma_ext_id
        assets[design_id] = {"extracted_assets_path": file_path}
        
        # Update the project node with the new assets information
        project_properties = project_node["properties"].copy()
        project_properties["assets"] = json.dumps(assets)
        
        # Update the node in the database
        update_result = await node_db.update_node_by_id(project_id, project_properties)
        
        # If update fails, log it but don't fail the whole request
        if not update_result:
            print(f"Warning: Failed to update assets in project node {project_id}")
        
        return JSONResponse(
            status_code=200,
            content={
                "message": f"File {file_path} merged successfully",
                "project_id": request.project_id,
                "discussion_id": request.discussion_id,
                "figma_ext_id": request.figma_ext_id,
                "file_path": file_path,
                "extraction_path": file_path,
            }
        )
        
    except Exception as e:
        traceback.print_exc()
        print(f"Error in merge_changes: {str(e)}")
        return JSONResponse(
            status_code=500, 
            content={"message": f"An error occurred while merging changes: {str(e)}"}
        )
#################################################################################
@router.post("/create_ext_images")
async def add_ext_images(
    project_id: str,
    group_name: str,
    files: List[UploadFile] = File(...),
    current_user=Depends(get_current_user),
):
    try:
        mongo_db = get_mongo_db().db
        collection = "figma_ext_images"
        id = str(uuid.uuid4())[:8]    
        #create a fresh copy with deepcopy of IMAGE_TEMPLATE    
        tmp_image = deepcopy(IMAGE_TEMPLATE)
        
        tmp_image["added_by"]["username"] = current_user["sub"]
        tmp_image["added_by"]["name"] = current_user["custom:Name"]
        tmp_image["added_by"]["email"] = current_user["email"]
        tmp_image["project_id"] = project_id
        tmp_image["tenant_id"] = get_tenant_id()
        tmp_image["time_created"] = generate_timestamp()
        tmp_image["time_updated"] = generate_timestamp()
        
        tmp_image["figma_ext_id"] = id
        tmp_image["name"] = group_name
        tmp_image["status"] = "pending"
        tmp_image["total_frames"] = len(files)
        tmp_image["completed_frames"] = 0
        tmp_image["failed_frames"] = 0
        tenant_id = get_tenant_id()

        results = []
        path_extends = f"{BASE_PATH}/{tenant_id}/{project_id}/{id}/"
        os.makedirs(path_extends, exist_ok=True)
        tmp_image["images"] = []
        for file in files:
            try:
                # Save the uploaded file to the mongodb in base64url format
                file_content = await file.read()
                # Compress the image to 50%
                compressed_content, content_type, compression_ratio = compress_image(
                    file_content, target_ratio=0.5
                )
                
                base64_content = base64.b64encode(compressed_content).decode('utf-8')
                base64url = f"data:{file.content_type};base64,{base64_content}"
                image = ImageTemplate(
                    filename=file.filename, 
                    size=file.size,
                    file_type=file.content_type,
                    base64url=base64url,
                    error_message=""
                )
                tmp_image["images"].append(image.model_dump())

                tmp_image["completed_frames"] += 1
                tmp_image["time_updated"] = generate_timestamp()

                results.append({
                    "filename": file.filename,
                    "status": "success",
                    "base64url": base64url,
                    "compression_ratio": compression_ratio,
                    "original_size": len(file_content),
                    "compressed_size": len(compressed_content)
                })

            except Exception as e:
                tmp_image["failed_frames"] += 1
                tmp_image["time_updated"] = generate_timestamp()
                tmp_image["error_message"] = str(e)

                results.append({
                    "filename": file.filename,
                    "status": "error",
                    "error": str(e)
                })
                
        tmp_image["path"] = path_extends
        tmp_image["status"] = "completed" if tmp_image["failed_frames"] == 0 else "failed"
        tmp_image["time_updated"] = generate_timestamp()

        
        mongo_db[collection].update_one(
            {"figma_ext_id": id},
            {"$set": tmp_image},
            upsert=True
        )

        return JSONResponse(
            status_code=200, 
            content={
                "message": f"Processed {len(files)} files", 
                "results": results
            }
        )

    except Exception as e:
        #traceback
        traceback.print_exc()
        print(f"Error in add_ext_images: {str(e)}")  # Fixed the debug print message
        return JSONResponse(
            status_code=500, content={"message": f"An error occurred: {str(e)}"}
        )
    
    

    
@router.post("/generate_assets")
async def generate_assets(
    project_id: int,
    selected_design_id: str,
    discussion_id: str,
    current_user=Depends(get_current_user)
):
    try:
        pass
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An unexpected error occurred: {str(e)}"
        )
    
@router.delete("/delete_ext_images/{figma_ext_id}")
async def delete_ext_images(
    figma_ext_id: str,
    current_user=Depends(get_current_user),
):
    try:
        mongo_db = get_mongo_db().db
        collection = "figma_ext_images"
        
        # First, fetch the document to get necessary info
        image_doc = mongo_db[collection].find_one({"figma_ext_id": figma_ext_id})
        
        if not image_doc:
            return JSONResponse(
                status_code=404,
                content={"message": f"Image with ID {figma_ext_id} not found"}
            )
        
        # Verify user has permission to delete
        # Option 1: Only creator can delete
        if image_doc["added_by"]["username"] != current_user["sub"]:
            return JSONResponse(
                status_code=403,
                content={"message": "You don't have permission to delete this resource"}
            )
        
        # Delete associated files from the filesystem
        # path_extends = image_doc["path"]
        # if os.path.exists(path_extends):
        #     try:
        #         shutil.rmtree(path_extends)
        #     except Exception as e:
        #         print(f"Warning: Could not delete directory {path_extends}: {str(e)}")
        
        # Delete from MongoDB
        delete_result = mongo_db[collection].delete_one({"figma_ext_id": figma_ext_id})
        
        if delete_result.deleted_count == 1:
            return JSONResponse(
                status_code=200,
                content={"message": f"Successfully deleted image group with ID {figma_ext_id}"}
            )
        else:
            return JSONResponse(
                status_code=500,
                content={"message": f"Failed to delete image group with ID {figma_ext_id}"}
            )
            
    except Exception as e:
        traceback.print_exc()
        print(f"Error in delete_ext_images: {str(e)}")
        return JSONResponse(
            status_code=500, 
            content={"message": f"An error occurred: {str(e)}"}
        )
    
@router.post("/add_more_images/{project_id}/{figma_ext_id}")
async def add_more_images(
    project_id: str,
    figma_ext_id: str,
    files: List[UploadFile] = File(...),
    current_user=Depends(get_current_user),
):
    """Add more images to an existing image group
    
    Args:
        project_id (str): Project identifier
        figma_ext_id (str): Existing image group identifier
        files (List[UploadFile]): List of files to add
        current_user: Current authenticated user
    """
    try:
        mongo_db = get_mongo_db().db
        collection = "figma_ext_images"
        
        # Get existing image document
        image_doc = mongo_db[collection].find_one({
            "figma_ext_id": figma_ext_id,
            "project_id": project_id
        })
        
        if not image_doc:
            return JSONResponse(
                status_code=404,
                content={"message": f"Image group with ID {figma_ext_id} not found"}
            )
        
        # Verify user has permission
        if image_doc["added_by"]["username"] != current_user["sub"]:
            return JSONResponse(
                status_code=403,
                content={"message": "You don't have permission to modify this resource"}
            )
        
        results = []
        for file in files:
            try:
                # Convert file to base64url format
                file_content = await file.read()
                base64_content = base64.b64encode(file_content).decode('utf-8')
                base64url = f"data:{file.content_type};base64,{base64_content}"
                
                # Create image template
                image = ImageTemplate(
                    filename=file.filename,
                    size=file.size,
                    file_type=file.content_type,
                    base64url=base64url,
                    error_message=""
                )
                
                # Add to MongoDB array
                mongo_db[collection].update_one(
                    {"figma_ext_id": figma_ext_id},
                    {
                        "$push": {"images": image.model_dump()},
                        "$inc": {
                            "total_frames": 1,
                            "completed_frames": 1
                        },
                        "$set": {
                            "time_updated": generate_timestamp(),
                            "status": "completed"
                        }
                    }
                )

                results.append({
                    "filename": file.filename,
                    "status": "success",
                    "base64url": base64url
                })

            except Exception as e:
                # Update error count and status
                mongo_db[collection].update_one(
                    {"figma_ext_id": figma_ext_id},
                    {
                        "$inc": {
                            "total_frames": 1,
                            "failed_frames": 1
                        },
                        "$set": {
                            "time_updated": generate_timestamp(),
                            "status": "failed",
                            "error_message": str(e)
                        }
                    }
                )
                
                results.append({
                    "filename": file.filename,
                    "status": "error",
                    "error": str(e)
                })

        # Get updated document
        updated_doc = mongo_db[collection].find_one({"figma_ext_id": figma_ext_id})
        if updated_doc:
            updated_doc.pop("_id")  # Remove MongoDB _id before sending response
        
        return JSONResponse(
            status_code=200,
            content={
                "message": f"Added {len(files)} files to image group {figma_ext_id}",
                "results": results,
                "updated_document": updated_doc
            }
        )

    except Exception as e:
        traceback.print_exc()
        print(f"Error in add_more_images: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"message": f"An error occurred: {str(e)}"}
        )
@router.put("/rename_ext_image/{figma_ext_id}/{file_id}")
async def rename_ext_image(
    figma_ext_id: str,
    file_id: str,
    new_filename: str,
    current_user=Depends(get_current_user),
):
    """Rename a specific image in an image group
    
    Args:
        figma_ext_id (str): Image group identifier
        file_id (str): Image file identifier
        new_filename (str): New name for the file
        current_user: Current authenticated user
    """
    try:
        mongo_db = get_mongo_db().db
        collection = "figma_ext_images"
        
        # Get existing image document
        image_doc = mongo_db[collection].find_one({"figma_ext_id": figma_ext_id})
        
        if not image_doc:
            return JSONResponse(
                status_code=404,
                content={"message": f"Image group with ID {figma_ext_id} not found"}
            )
        
        # Verify user has permission
        if image_doc["added_by"]["username"] != current_user["sub"]:
            return JSONResponse(
                status_code=403,
                content={"message": "You don't have permission to modify this resource"}
            )
        
        # Find and update the specific image
        updated = mongo_db[collection].update_one(
            {
                "figma_ext_id": figma_ext_id,
                "images.file_id": file_id
            },
            {
                "$set": {
                    "images.$.filename": new_filename,
                    "time_updated": generate_timestamp()
                }
            }
        )
        
        if updated.modified_count == 0:
            return JSONResponse(
                status_code=404,
                content={"message": f"Image with filename {file_id} not found in group"}
            )
            
        # Get updated document
        updated_doc = mongo_db[collection].find_one({"figma_ext_id": figma_ext_id})
        if updated_doc:
            updated_doc.pop("_id")
        
        return JSONResponse(
            status_code=200,
            content={
                "message": f"Successfully renamed image {file_id} to {new_filename}",
                "updated_document": updated_doc
            }
        )

    except Exception as e:
        traceback.print_exc()
        print(f"Error in rename_ext_image: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"message": f"An error occurred: {str(e)}"}
        )

@router.delete("/delete_ext_image/{figma_ext_id}/{file_id}")
async def delete_ext_image(
    figma_ext_id: str,
    file_id: str,
    current_user=Depends(get_current_user),
):
    """Delete a specific image from an image group
    
    Args:
        figma_ext_id (str): Image group identifier
        file_id (str): Image file identifier
        current_user: Current authenticated user
    """
    try:
        mongo_db = get_mongo_db().db
        collection = "figma_ext_images"
        
        # Get existing image document
        image_doc = mongo_db[collection].find_one({"figma_ext_id": figma_ext_id})
        
        if not image_doc:
            return JSONResponse(
                status_code=404,
                content={"message": f"Image group with ID {figma_ext_id} not found"}
            )
        
        # Verify user has permission
        if image_doc["added_by"]["username"] != current_user["sub"]:
            return JSONResponse(
                status_code=403,
                content={"message": "You don't have permission to modify this resource"}
            )
        
        # Remove the specific image and update counters
        updated = mongo_db[collection].update_one(
            {"figma_ext_id": figma_ext_id},
            {
                "$pull": {"images": {"file_id": file_id}},
                "$inc": {"total_frames": -1},
                "$set": {"time_updated": generate_timestamp()}
            }
        )
        
        if updated.modified_count == 0:
            return JSONResponse(
                status_code=404,
                content={"message": f"Image with filename {file_id} not found in group"}
            )
        
        # Recalculate completed_frames and failed_frames
        updated_doc = mongo_db[collection].find_one({"figma_ext_id": figma_ext_id})
        completed_frames = sum(1 for img in updated_doc["images"] if not img.get("error_message"))
        failed_frames = sum(1 for img in updated_doc["images"] if img.get("error_message"))
        
        # Update the counts
        mongo_db[collection].update_one(
            {"figma_ext_id": figma_ext_id},
            {
                "$set": {
                    "completed_frames": completed_frames,
                    "failed_frames": failed_frames,
                    "status": "completed" if failed_frames == 0 else "failed"
                }
            }
        )
        
        # Get final updated document
        final_doc = mongo_db[collection].find_one({"figma_ext_id": figma_ext_id})
        if final_doc:
            final_doc.pop("_id")
        
        return JSONResponse(
            status_code=200,
            content={
                "message": f"Successfully deleted image {file_id}",
                "updated_document": final_doc
            }
        )

    except Exception as e:
        traceback.print_exc()
        print(f"Error in delete_ext_image: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"message": f"An error occurred: {str(e)}"}
        )    
@router.get("/download_figma_code/{discussion_id}")
async def download_code(discussion_id: str):
    """
    Download all Code for a specific figma extraction as a zip file.
    Provides a browser download experience similar to downloading images from Google.
    
    Args:
        discussion_id: discussion ID of the figma extraction
        
    Returns:
        Streaming response with zip file
    """
    try:
        
        code_dir = f"{FIGMA_BASE_PATH}/{discussion_id}/.assets"
        
        # Check if directory exists
        if not os.path.exists(code_dir):
            raise HTTPException(status_code=404, detail=f"No codes file found for {discussion_id}")
        
        # Create timestamp for unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"figma_extraction_{discussion_id}_{timestamp}.zip"
        
        # Create zip file in memory
        zip_io = io.BytesIO()
        with zipfile.ZipFile(zip_io, mode='w', compression=zipfile.ZIP_DEFLATED) as zipf:
            # Walk through the directory and add all files
            for root, _, files in os.walk(code_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    # Add file to zip with relative path
                    arcname = os.path.relpath(file_path, code_dir)
                    zipf.write(file_path, arcname)
        
        # Reset the pointer to the beginning of the BytesIO object
        zip_io.seek(0)
        
        # Return streaming response with appropriate headers for download
        headers = {
            'Content-Disposition': f'attachment; filename="{filename}"',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        }
        
        return StreamingResponse(
            zip_io, 
            media_type="application/zip",
            headers=headers
        )
        
    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        import traceback
        print(f"Error creating Code's zip file: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to create Code's File archive: {str(e)}")