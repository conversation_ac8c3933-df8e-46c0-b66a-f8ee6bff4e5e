import time
from app.connection.establish_db_connection import get_mongo_db
from app.connection.establish_db_connection import get_node_db, NodeDB ,get_vector_db  , connect_mongo_db, get_tenant_id
from app.connection.llm_init import get_llm_interface
from app.utils.auth_utils import get_current_user
from fastapi import APIRouter, Depends, Body , Query, Request, responses, HTTPException , BackgroundTasks
from typing import List, Union, Annotated, Dict, Optional
from fastapi.responses import J<PERSON>NResponse, StreamingResponse
from app.utils.k8_job import K8JobManager
from app.utils.node_utils import node_types, root_node_types, clone_node, create_node as node_creation 
from app.utils.node_utils import get_node_type
from datetime import datetime
import json
from app.core.constants import TASKS_COLLECTION_NAME as tasks_collection_name
from app.models.node_model import CreateNodeRequest, PropertyUpdate
from app.core.constants import NodeType, Node<PERSON>abel
from app.utils.user_utils import track_project_usage , delete_project_usage , delete_notifications
from app.core.function_schema_generator import load_json_file
from pydantic import BaseModel
from enum import Enum
from app.core.data_model_helper import data_model
from app.routes.users_route import get_user_by_id
from app.classes.Ec2Handler import Ec2Handler
from app.core.Settings import settings
from app.utils.project_utils import get_stage, safe_name
from typing import Dict, List, Any
from collections import defaultdict
from app.utils.datetime_utils import generate_timestamp
from typing import AsyncGenerator
from llm_wrapper.core.llm_interface import LLMInterface
import os
import asyncio
from app.utils.stream_utils import format_response_for_stream
from app.routes.batch_route import stream_start_workspace_status
from pydantic import BaseModel, Field
from typing import Optional
from app.models.project_model import ProjectVisibilityRequest, PublicProjectModel, ProjectInfoManual, ProjectInfoDirectCodeGen, ArcitectureInfo
from app.classes.MongoDB import MongoDBHandler
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME
from app.utils.k8.create_project_v2 import create_project_for_dev, create_project_for_qa, create_project_for_beta
from app.utils.logs_utils import get_path
from app.models.user_model import LLMModel

_SHOW_NAME = "node"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)




llm_interface = get_llm_interface()

class QueryType(str, Enum):
    REQUIREMENT = "requirement"
    ARCHITECTURE = "architecture"
    DEPLOYMENT = "deployment"

class Framework(str, Enum):
    REACT = "react"
    # ANGULAR = "angular"
    VUE = "vue"
    # NEXTJS = "nextjs"
    # SVELTE = "svelte"
    # LARAVEL = "laravel"

class Platform(str, Enum):
    COMMON = "common"
    WEB = "web"
    ANDROID = "android"
    IOS = "ios"

class ProjectProperties(BaseModel):
    """Model representing the properties of a project."""
    created_by: Optional[str] = None
    Description: Optional[str] = None
    is_active: Optional[bool] = None
    status: Optional[str] = None
    Requirement: Optional[str] = None
    Type: Optional[str] = None
    created_at: Optional[str] = None
    Title: Optional[str] = None


class ProjectNodes(BaseModel):
    """Model representing a project node from the database."""
    id: Optional[int] = None
    labels: Optional[List[str]] = None
    properties: Optional[ProjectProperties] = None

class FeaturesInfo(BaseModel):
    id: str
    name: str
    description: str
    isEnabled: bool
class InitProjectData(BaseModel):
    """Model representing a project node from the database."""
    description: Optional[str] = None
    features: List[FeaturesInfo] = None
    layoutDescription: Optional[str] = None

class TechStack(BaseModel):
    frontend: List[str]
    backend: List[str]
    language: List[str]

class Colors(BaseModel):
    primary: str
    secondary: str
    accent: str

class Blueprint(BaseModel):
    projectInfo: Optional[ProjectNodes] = None
    id: Optional[str] = None
    name: Optional[str] = None
    description: Optional[str] = None
    features: Optional[List[FeaturesInfo]] = None
    techStack: Optional[TechStack] = None
    colors: Optional[Colors] = None
    theme: Optional[str] = None
    estimatedTime: Optional[str] = None
    complexity: Optional[str] = None
    layoutDescription: Optional[str] = None

class ProjectRequest(BaseModel):
    requirement: str = Field(description="Project requirement", examples=["Create a todo list application"])
    framework: str = Framework.REACT.value
    platform: str = Platform.WEB.value
    # projectInfo: Optional[ProjectNodes] = Field(default=None, description="The project Node information")
    # Init_project_info: Optional[InitProjectData] = Field(default=None, description="The data to be updated")
    blueprint: Optional[Blueprint] =  Field(default=None, description="Blueprint info Init_project_info , projectInfo")


class StartProjectInit(BaseModel):
    usercomment: str
    type: Optional[str] = None  
    title: Optional[str] = None  

class CloneNode(BaseModel):
    title: Optional[str] = None

class CountResponse(BaseModel):
    counts: Dict[str, int]
@router.get("/")
async def get_nodes(node_type:str, db = Depends(get_node_db) ):
    print("Received query for node type:", node_type)
    if node_type not in node_types and node_type not in root_node_types:
        raise HTTPException(status_code=400, detail="Invalid node type")

    try:
        nodes = await db.get_nodes_by_label(node_type)
        return nodes
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

from typing import Dict, List, Any
from collections import defaultdict
from pydantic import BaseModel
from typing import Literal

def format_flowline_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Formats flowline data by sorting sections and transforming structure from {Root, Title} to [title, title].
    
    Args:
        data: Dictionary containing architecture components and other data
        
    Returns:
        Dictionary with sorted and restructured elements
    """
    
    if "architecture" not in data:
        return data
    def transform_and_sort_section(items: List[Dict[str, str]]) -> List[List[str]]:
        """Helper function to transform and sort items"""
        grouped_items = defaultdict(list)
        
        for item in items:
            root = item['Root'].lower()
            # Transform from {Root, Title} to [title, title]
            transformed_item = item['Title']
            grouped_items[root].append(transformed_item)
            
        sorted_roots = sorted(grouped_items.keys())
        sorted_items = []
        for root in sorted_roots:
            sorted_items.extend(grouped_items[root])
            
        return sorted_items

    def sort_container_list(containers: List[str]) -> List[str]:
        """Helper function to sort container list"""
        return sorted(containers, key=str.lower)

    # Create a deep copy of the input data
    result = data.copy()
    result['architecture'] = data['architecture'].copy()
    # Transform and sort sections
    if 'component' in result['architecture'] and result['architecture']['component']!=[]:
        result['architecture']['component'] = transform_and_sort_section(data['architecture']['component'])
    # Handle container section - filter None values before sorting
    if 'container' in result['architecture']:
        # First filter out None values
        result['architecture']['container'] = [
            c for c in result['architecture']['container'] 
            if c is not None
        ]
        # Then sort the remaining valid containers
        if result['architecture']['container']:  # Only sort if list is not empty
            result['architecture']['container'] = sort_container_list(
                result['architecture']['container']
            )
    if 'Requirement' in result['architecture'] and result['architecture']['Requirement']!=[]:
        result['architecture']['Requirement'] = {
            'Description': data['architecture']['Requirement']['Description'],
            'functional_requirements': data['architecture']['Requirement']['functional_requirements'],
            'architectural_requirements': data['architecture']['Requirement']['architectural_requirements']
        }
    if 'systemContext' in result['architecture']:
        result['architecture']['systemContext'] = {
            'Description': data['architecture']['systemContext']['Description'],
            'Users': data['architecture']['systemContext']['Users'],
            'External Systems': data['architecture']['systemContext']['ExternalSystems'],
            'System ContextDiagram': data['architecture']['systemContext']['SystemContextDiagram'],
            'ContainerDiagram': data['architecture']['systemContext']['ContainerDiagram'],

        }    
    if 'interface' in result['architecture'] and result['architecture']['interface']!=[]:
        result['architecture']['interface'] = transform_and_sort_section(data['architecture']['interface'])
    
    if 'design' in result['architecture'] and result['architecture']['design']!=[]:
        result['architecture']['design'] = transform_and_sort_section(data['architecture']['design'])
    print(data)
    return result

class ProjectContainerRequest(BaseModel):
    project_id: str
    env_name: str
    action: Literal["create", "delete"]
    
@router.post("/project-container")
async def manage_project_container(
    request: ProjectContainerRequest,
    current_user = Depends(get_current_user)
):
    """
    Create or delete a project container using Kubernetes jobs
    """
    try:
        k8_job_manager = K8JobManager()
        job_name = k8_job_manager.create_project_container_job(
            project_id=request.project_id,
            env_name=request.env_name,
            action=request.action
        )
        
        if job_name:
            return {
                "status": "success",
                "message": f"Job {job_name} created successfully",
                "job_name": job_name
            }
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to create Kubernetes job"
            )
            
    except ValueError as ve:
        raise HTTPException(
            status_code=400,
            detail=str(ve)
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred: {str(e)}"
        )
        
@router.get("/properties/{node_id}", summary="Fetch node properties")
async def fetch_node_properties(
    node_id: int,
    node_type: str,
    db: NodeDB = Depends(get_node_db),
    current_user = Depends(get_current_user)
):
    print(f"Fetching properties for node with ID: {node_id}")
    node_type = get_node_type(node_type)
    if node_type not in NodeType.__members__.values():
        raise HTTPException(status_code=400, detail="Invalid node type")
    
    try:
        node = await db.get_node_properties_by_id(node_id, node_type=node_type)
        if node:
            node["ui_metadata"] = data_model["model"][node_type]["ui_metadata"]
            return node
        else:
            return JSONResponse(content={"message": "Node not found"}, status_code=404)
    except Exception as e:
        print("Exception: ", e)
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")
    
    
@router.get("/{node_id}")  # Adjust response_model as necessary
async def get_node(
    node_id: int, 
    node_type: str, 
    db: NodeDB =Depends(get_node_db), 
    current_user=Depends(get_current_user),
    background_tasks: BackgroundTasks = BackgroundTasks()  # Added for background tasks
):
    print("Fetching node with ID:", node_id)
    node_type = get_node_type(node_type)

    if node_type not in NodeType.__members__.values():
        
        raise HTTPException(status_code=400, detail="Invalid node type")
    try:
        node = await db.get_node_by_id(node_id, node_type = node_type)
        if node:
            # Trigger project usage tracking in the background (optional)
            if get_node_type(node_type) in ["Project", "Product"] and current_user:
                try:
                    background_tasks.add_task(
                        track_project_usage, 
                        user_id=current_user.get("cognito:username"),
                        project_id=node_id,
                        project_name=node.get("properties", {}).get("Title",node.get("properties", {}).get("Name"))
                    )
                    # ec2_handler = Ec2Handler()
                    # print("Invoking ec2 instance")
                    # tenant_id = current_user.get("custom:tenant_id")
                    # project_id = node_id
                    # stage = get_stage() 
                    # instance_name =f"{tenant_id}-{project_id}-{stage}"
                    # background_tasks.add_task(
                    #     ec2_handler.wake_up_instance,
                    #     instance_name=instance_name)
                except Exception as e:
                    print("Error tracking project usage: ", str(e))
                    pass
                    
                
            # print(node.get("properties"))

            node["ui_metadata"] = data_model["model"][node_type]["ui_metadata"]
            return node
        else:
            return JSONResponse(content={"message": "Node not found"})
    except Exception as e:
        print("Exception: ", e)
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")

@router.get("/v2/data_model/{node_id}")
async def get_node_v2(
    node_id: int, 
    node_type: str, 
    exclude_relationships: bool = Query(False, description="Exclude relationships from the response"),
    skip_ui_metadata: bool = Query(False, description="Skip UI metadata from the response"),
    db: NodeDB = Depends(get_node_db),
    current_user = Depends(get_current_user),
    background_tasks: BackgroundTasks = BackgroundTasks()  # Added for background tasks
    ):
    node_type = get_node_type(node_type)
    if node_type not in NodeType.__members__.values():
        raise HTTPException(status_code=400, detail="Invalid node type")
    
    # if get_node_type(node_type) in ["Project", "Product"]:
    #     try:
    #         ec2_handler = Ec2Handler()
    #         print("Invoking ec2 instance")
    #         tenant_id = current_user.get("custom:tenant_id")
    #         project_id = node_id
    #         stage = get_stage() 
    #         instance_name =f"{tenant_id}-{project_id}-{stage}"
    #         background_tasks.add_task(
    #             ec2_handler.wake_up_instance,
    #             instance_name=instance_name)
    #     except Exception as e:
    #         print("Error tracking project usage: ", str(e))
    #         pass
    node = await db.get_node_based_on_data_model(node_id, node_type, data_model, exclude_relationships=exclude_relationships,skip_ui_metadata=skip_ui_metadata)
    
    if node is None:
        raise HTTPException(status_code=404, detail="Node not found")
    
    return node
        
@router.get("/list_projects/")
async def get_projects(db:NodeDB = Depends(get_node_db), current_user = Depends(get_current_user)):
    tenant_id = current_user.get("custom:tenant_id")
    if tenant_id.startswith("default"):
        tenant_id = settings.KAVIA_B2C_CLIENT_ID
    email = current_user.get("email")
    print("email", email, "tenant_id", tenant_id)
    projects = await db.get_projects()
    if tenant_id == settings.KAVIA_B2C_CLIENT_ID:
        projects = [project for project in projects if project.get("creator_email") == email]
    return projects
  
# Route for change visibility
  
@router.post("/")
async def create_node(request: dict, current_user = Depends(get_current_user)):
    print(type(request))
    name = request.get('name')
    description = request.get('description')
    node_type = request.get('node_type')
    properties = request.get('properties', {})
    
    properties['created_by'] = current_user.get('cognito:username')
    properties['created_at'] = generate_timestamp()
    
    if node_type not in NodeType.__members__.values():
        raise HTTPException(status_code=400, detail="Invalid node type")

    try:
        return await node_creation(node_type, name, description, properties)
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")

@router.post("/v2/")
async def create_node_v2(request: CreateNodeRequest, db:NodeDB = Depends(get_node_db)):
  
    
    properties = request.model_dump()
    node_type = properties.pop('node_type')
    node_type = get_node_type(node_type)
    parent_id = properties.pop('parent_id', None)
    
    properties['created_at'] = generate_timestamp()
    properties['updated_at'] = generate_timestamp()
    
    labels = []
    if node_type == "Design":
        # There must be only one design node per architecture
        get_child_nodes = await db.get_child_nodes(parent_id, "Design")
        if get_child_nodes:
            return JSONResponse(content="Design node already exists", status_code=400)
                
    if node_type == "Epic":
        labels = NodeLabel.EPIC.value
    elif node_type == "UserStory":
        labels = NodeLabel.USERSTORY.value
    elif node_type == "Task":
        labels = NodeLabel.TASK.value
    else:
        labels = [node_type]
        
    labels = list(set(labels))
    result = await db.create_node( labels , properties, parent_id)
    return result
     
@router.patch("/{node_id}")
async def update_node(node_id: int, request: dict, current_user = Depends(get_current_user)):
    db = get_node_db()
    vector_db = get_vector_db()
    print("Received request to update node", node_id)
    node_type = request.get('node_type')
    name = request.get('name')
    description = request.get('description')
    scope = request.get('scope')
    node_types_to_omit = ['Discussion', 'User'] # Nodes that should not be updated in the vector database FIXME: This should be a configuration
    if not node_id or not node_type:
        raise HTTPException(status_code=400, detail='Node id or type not provided')

    if node_type not in node_types:
        raise HTTPException(status_code=400, detail='Invalid node type')
    properties = {
        'Name': name,
        'Title': name, 
        'Description': description,
        'Scope':scope
    }
    
    print("properties", properties)
    properties['updated_by'] = current_user.get('cognito:username')
    properties['updated_at'] = generate_timestamp()
    
    node = await db.update_node_by_id(node_id, properties, node_type)

    # Update the node in the vector database. For that first get the full set of properties for the node
    if node_type not in node_types_to_omit:
        # await db.add_embedding_to_node(node_id, node.get('properties'))
        await vector_db.update_node_in_vector_db(node_id, node.get('properties'), node_type)

    if node:

        return node

    raise HTTPException(status_code=500, detail='Failed to update node')

@router.patch("/v2/{node_id}")
async def update_node_properties(
    node_id: int,
    node_type: str,
    properties: dict = Body(...),
    current_user: dict = Depends(get_current_user)
):
    db = get_node_db()
    
    if not node_id or not node_type:
        raise HTTPException(status_code=400, detail='Node id or type not provided')
    node_type = get_node_type(node_type)
    
    if node_type not in NodeType.__members__.values():
        raise HTTPException(status_code=400, detail="Invalid node type")
    
    properties['updated_by'] = current_user.get('cognito:username')
    properties['updated_at'] = generate_timestamp()
    
    node = await db.update_node_by_id(node_id, properties, node_type)
    
    return node


@router.patch("/property/{node_id}")
async def update_property_in_node(
    node_id: int, 
    update_data: PropertyUpdate,
    db: NodeDB = Depends(get_node_db)
):
    """
    Update a field in a node using request body
    """
    response = await db.update_property_by_id(
        node_id, 
        update_data.property_name, 
        update_data.property_value,
        update_data.session_id
    )
    if response:
        return JSONResponse(content="success", status_code=200)
    
    return JSONResponse(content="failed", status_code=404)

@router.delete("/{node_id}")
async def delete_node(node_id: int, node_type: str, node_db = Depends(get_node_db), background_tasks: BackgroundTasks = BackgroundTasks()):
    vector_db = get_vector_db()
    print("Received request to delete node", node_id)
    project_id = node_id
    
    if not node_id or not node_type:
        raise HTTPException(status_code=400, detail='Node id or type not provided')

    if node_type not in node_types and node_type not in root_node_types:
        raise HTTPException(status_code=400, detail='Invalid node type')

    if node_type == "Project":
        deleted_node_ids = await node_db.delete_associates_and_deactivate_project(node_id, ['HAS_CHILD'])
    else:
        # Delete the node and all its 'HAS_CHILD' nodes recursively
        deleted_node_ids = await node_db.delete_node_and_associates(node_id, ['HAS_CHILD'])
    
    # if not deleted_node_ids or len(deleted_node_ids) == 0:
        # If no nodes were deleted then return an error
        # raise HTTPException(status_code=404, detail='Node not found')
    
    # Move vector DB deletions to background tasks
    for deleted_node_id in deleted_node_ids:
        background_tasks.add_task(vector_db.delete_node_from_vector_db, deleted_node_id)

    # Move conversation deletion to background tasks
    background_tasks.add_task(node_db.delete_conversations, project_id)
    
    if get_node_type(node_type) in ["Project", "Product"]:
        # Delete project usage tracking in the background (optional)
        
        background_tasks.add_task(delete_project_usage, project_id)
        background_tasks.add_task(delete_notifications, project_id)
    
    return deleted_node_ids


@router.delete("/")
async def delete_multiple_nodes(
    node_ids: List[int] = Query(..., description="List of node IDs to delete"),
    node_type: str = Query(..., description="Type of nodes to delete"),
    node_db: NodeDB = Depends(get_node_db),
    vector_db = Depends(get_vector_db),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    print(f"Received request to delete multiple nodes of type {node_type}: {node_ids}")

    if not node_ids or not node_type:
        raise HTTPException(status_code=400, detail='Node ids or type not provided')

    if node_type not in node_types and node_type not in root_node_types:
        raise HTTPException(status_code=400, detail='Invalid node type')

    deleted_node_ids = []
    for node_id in node_ids:
        try:
            # Delete the node and all its 'HAS_CHILD' nodes recursively
            deleted_ids = await node_db.delete_node_and_associates(node_id, ['HAS_CHILD', 'HAS_USER'])
            deleted_node_ids.extend(deleted_ids)

            if get_node_type(node_type) in ["Project", "Product"]:
                # Delete project usage tracking in the background (optional)
                background_tasks.add_task(delete_project_usage, node_id)
                background_tasks.add_task(delete_notifications, node_id)

            # Delete from vector database
            for deleted_id in deleted_ids:
                await vector_db.delete_node_from_vector_db(deleted_id)

        except Exception as e:
            print(f"Error deleting node {node_id}: {str(e)}")
            # Continue with other nodes even if one fails

    if not deleted_node_ids:
        raise HTTPException(status_code=404, detail='No nodes were deleted')

    return {"deleted_node_ids": deleted_node_ids}

@router.get("/get_child_nodes/")
async def get_child_nodes(node_id: int , node_type: str, child_node_type: str, db = Depends(get_node_db)):
    print("Received request to get child nodes for node", node_id)
    if (not node_id and node_id!=0 ) or not node_type or not child_node_type:
        raise HTTPException(status_code=400, detail='Node id, type, and child node type not provided')
    # Fetch child nodes
    nodes = await db.get_child_nodes(node_id, child_node_type)
    
    if nodes:
        return nodes
    else:
        return []
    
@router.get("/get_node_tree/")
async def get_node_tree(node_id: int, node_type: str, db = Depends(get_node_db)):
    if not node_id or not node_type:
        raise HTTPException(status_code=400, detail='Node id and type not provided')

    # Get the node tree
    node_tree = await db.get_node_tree(node_id, node_type)

    if node_tree:
        return node_tree
    else:
        raise HTTPException(status_code=404, detail='Node not found')

@router.get("/get_similar_nodes/")
async def get_similar_nodes(node_id: int, max_results: int = Query(10, ge=1), db = Depends(get_node_db), vector_db = Depends(get_vector_db)):
    if not node_id:
        raise HTTPException(status_code=400, detail='Node id not provided')

    # Assuming 'db.get_node_by_id' fetches the node and its properties
    parent_node = await db.get_node_by_id(node_id)
    if not parent_node:
        raise HTTPException(status_code=404, detail='Parent node not found')

    # Use the vector DB to find similar nodes
    similar_node_ids = await vector_db.find_similar_nodes(parent_node['properties'], max_results=max_results)

    similar_nodes = await db.get_nodes_by_ids(similar_node_ids)

    return similar_nodes

@router.get("/get_connected_nodes/")
async def get_connected_nodes(node_id: int, node_type: str, linked_node_type: str, db = Depends(get_node_db)):
    if not node_id or not node_type or not linked_node_type:
        raise HTTPException(
            status_code=400, 
            detail='Node id, type, and linked node type not provided'
        )
    
    # Make sure that the node is something that we allow to fetch
    if node_type not in node_types:
        raise HTTPException(
            status_code=400, 
            detail='Invalid node type'
        )

    nodes = await db.get_connected_nodes(node_id, linked_node_type)
    if nodes:
        return nodes
    else:
        return []

@router.post("/create_node_association/")
async def create_node_association(message_body: dict):
    db = get_node_db()
    start_node_id = message_body.get('start_node_id')
    end_node_id = message_body.get('end_node_id')
    relationship_type = message_body.get('relationship_type')
    
    if not start_node_id or not end_node_id or not relationship_type:
        raise HTTPException(status_code=400, detail="Invalid request")
    
    # Call your asynchronous function to create node association here
    relationship_type = await db.create_relationship(int(start_node_id), int(end_node_id), relationship_type)

    return {'relationship_type': relationship_type}

@router.get("/get_associated_nodes/")
async def get_associated_nodes(node_id: int, node_type: str, associated_node_type: str, relationship_type: str):
    db = get_node_db()
    allowed_types = ['Product', 'Project']
    if node_type not in allowed_types or associated_node_type not in allowed_types:
        raise HTTPException(status_code=400, detail="Invalid node type")

    # Call your asynchronous function to get associated nodes here
    node = await db.get_associated_item(node_id, node_type, associated_node_type, relationship_type)
    if node:
        return node
    return []

@router.get("/get_likely_associated_nodes/")
async def get_likely_associated_nodes(node_id: int, node_type: str, associated_node_type: str, db = Depends(get_node_db), vector_db = Depends(get_vector_db)):
    allowed_types = ['Product', 'Project']
    if node_type not in allowed_types or associated_node_type not in allowed_types:
        raise HTTPException(status_code=400, detail="Invalid node type")

    # Call your asynchronous function to get likely associated nodes here
    node = await db.get_node_by_id(node_id)
    node['properties']['node_id'] = node['id']
    similar_nodes = await vector_db.find_similar_nodes(node['properties'])
    nodes = await db.get_nodes_by_ids(similar_nodes)
    filtered_nodes = [node for node in nodes if associated_node_type in node['labels']]
    
    if filtered_nodes:
        return filtered_nodes
    return []
 
@router.get("/get_graph_diagram/{node_id}")
async def get_graph_diagram(node_id: int ,node_types: Annotated[Union[List[str], None], Query()] = None , levels: int = Query(999, ge=0, le=999), node_db: NodeDB = Depends(get_node_db)):
    print(node_types)
    if node_types:
        node_types = node_types[0].split(',')
    result = await node_db.get_graph_nodes(node_id, node_types , levels)    

    if result:
        return JSONResponse(result, status_code=200)
    else:
        return JSONResponse({'message': 'Id not found'}, status_code= 404)

@router.get("/tasks_count/", response_description="Get Total Number of Task")
async def get_tasks_count(
    project_id: int,
    db = Depends(get_node_db)
):
    try:
        return await db.get_tasks_count(project_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    

async def project_counts_v2(project_id: int, query_type: QueryType, node_db: NodeDB):
    queries = {
        QueryType.REQUIREMENT: f"""
            MATCH (p:Project)
            WHERE ID(p) = {project_id}
            WITH p
            OPTIONAL MATCH (p)-[:HAS_CHILD|CONTAINS*]->(epic:Epic)
            OPTIONAL MATCH (epic)-[:HAS_CHILD|CONTAINS*]->(story:UserStory)
            OPTIONAL MATCH (story)-[:HAS_CHILD|CONTAINS*]->(task:Task)
            RETURN 
                COUNT(DISTINCT epic) as epicCount,
                COUNT(DISTINCT story) as userStoryCount,
                COUNT(DISTINCT task) as taskCount
        """,
        QueryType.ARCHITECTURE: f"""
            MATCH (p:Project)
            WHERE ID(p) = {project_id}
            WITH p
            OPTIONAL MATCH (p)-[:HAS_CHILD|CONTAINS*]->(sc:SystemContext)
            WHERE NOT sc:Discussion
            OPTIONAL MATCH (sc)-[:HAS_CHILD|CONTAINS*]->(c:Container)
            WHERE NOT c:Discussion
            OPTIONAL MATCH (c)-[:HAS_CHILD|CONTAINS*]->(comp:Component)
            WHERE NOT comp:Discussion
            OPTIONAL MATCH (c)-[:HAS_CHILD|CONTAINS*]->(des:Design)
            WHERE NOT des:Discussion
            OPTIONAL MATCH (c)-[:HAS_CHILD|CONTAINS*]->(i:Interface)
            WHERE NOT i:Discussion
            OPTIONAL MATCH (c)-[:HAS_CHILD|CONTAINS*]->(d:Deployment)
            WHERE NOT d:Discussion
            RETURN
                COUNT(DISTINCT sc) as systemContextCount,
                COUNT(DISTINCT c) as containerCount,
                COUNT(DISTINCT comp) as componentCount,
                COUNT(DISTINCT des) as designCount,
                COUNT(DISTINCT i) as interfaceCount,
                COUNT(DISTINCT d) as deploymentCount
        """,
        QueryType.DEPLOYMENT: f"""
            MATCH (p:Project)
            WHERE ID(p) = {project_id}
            WITH p
            OPTIONAL MATCH (p)-[:HAS_CHILD|CONTAINS*]->(sc:SystemContext)
            WHERE NOT sc:Discussion
            OPTIONAL MATCH (sc)-[:HAS_CHILD|CONTAINS*]->(c:Container)
            WHERE NOT c:Discussion
            OPTIONAL MATCH (c)-[:HAS_CHILD|CONTAINS*]->(d:Deployment)
            WHERE NOT d:Discussion
            RETURN COUNT(DISTINCT d) as deploymentCount
        """
    }
    
    try:
        cursor = await node_db.async_run(queries[query_type])
        
        # Try to get the first result from cursor
        record = None
        try:
            for row in cursor:
                record = row
                break
        except:
            pass

        if query_type == QueryType.REQUIREMENT:
            result = {
                "epicCount": record["epicCount"] if record else 0,
                "userStoryCount": record["userStoryCount"] if record else 0,
                "taskCount": record["taskCount"] if record else 0
            }
        elif query_type == QueryType.ARCHITECTURE:
            result = {
                "systemContextCount": record["systemContextCount"] if record else 0,
                "containerCount": record["containerCount"] if record else 0,
                "componentCount": record["componentCount"] if record else 0,
                "designCount": record["designCount"] if record else 0,
                "interfaceCount": record["interfaceCount"] if record else 0,
                "deploymentCount": record["deploymentCount"] if record else 0
            }
        else:  # DEPLOYMENT
            result = {
                "deploymentCount": record["deploymentCount"] if record else 0
            }

        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Database query error: {str(e)}"
        )
        
@router.get("/project_counts/{project_id}/{query_type}", response_model=CountResponse)
async def project_counts(
    project_id: int,
    query_type: QueryType,
    node_db = Depends(get_node_db)
) -> CountResponse:
    try:
        counts = await project_counts_v2(project_id, query_type, node_db)
        return CountResponse(counts=counts)
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )
    

def convert_counts_to_boolean(data: dict) -> dict:
    """
    Convert nested count values to boolean based on whether they're greater than 0.
    
    Args:
        data (dict): Nested dictionary containing count values
        
    Returns:
        dict: Dictionary with count values converted to boolean
    """
    result = {}
    
    for key, value in data.items():
        if isinstance(value, dict):
            # Recursively convert nested dictionaries
            result[key] = convert_counts_to_boolean(value)
        elif isinstance(value, (int, float)):
            # Convert numbers to boolean based on whether they're greater than 0
            result[key] = bool(value > 0)
        else:
            # Keep other values as is
            result[key] = value
            
    return result


async def flowline(project_id: int, node_db: NodeDB):
    unified_query = """
        MATCH (p:Project) 
        WHERE ID(p) = $project_id
        WITH p, ID(p) as pid, properties(p) as pprops

        // Get work item count directly
        OPTIONAL MATCH (p)-[:HAS_CHILD]->(wir:WorkItemRoot)
        WITH p, pid, pprops, wir
        OPTIONAL MATCH (wir)-[:HAS_CHILD]->(w:WorkItem)
        WITH p, pid, pprops, COUNT(w) as workItemCount

        // Rest of your existing query...
        OPTIONAL MATCH (p)-[:HAS_CHILD|CONTAINS*]->(epic:Epic)
        WHERE NOT epic:Discussion
        OPTIONAL MATCH (epic)-[:HAS_CHILD|CONTAINS*]->(story:UserStory)
        WHERE NOT story:Discussion  
        OPTIONAL MATCH (story)-[:HAS_CHILD|CONTAINS*]->(task:Task)
        WHERE NOT task:Discussion

        // Get requirements
        OPTIONAL MATCH (p)-[:HAS_CHILD|CONTAINS*]->(requirement:Requirement)
        WHERE NOT requirement:Discussion

        // Get architectural components
        OPTIONAL MATCH (p)-[:HAS_CHILD*]->(ar:ArchitecturalRequirement)
        WHERE NOT ar:Discussion

        // Get system context and its children
        OPTIONAL MATCH (p)-[:HAS_CHILD|CONTAINS*]->(sc:SystemContext)
        WHERE NOT sc:Discussion
        OPTIONAL MATCH (sc)-[:HAS_CHILD|CONTAINS*]->(c:Container)
        WHERE NOT c:Discussion
        OPTIONAL MATCH (c)-[:HAS_CHILD|CONTAINS*]->(comp:Component)
        WHERE NOT comp:Discussion AND comp.Type = 'Component'
        OPTIONAL MATCH (c)-[:HAS_CHILD|CONTAINS*]->(des:Design)
        WHERE NOT des:Discussion

        // Get interfaces from containers and components
        OPTIONAL MATCH (c)-[:HAS_CHILD]->(interface:Interface)
        WHERE NOT interface:Discussion
        OPTIONAL MATCH (comp)-[:HAS_CHILD]->(comp_interface:Interface)
        WHERE NOT comp_interface:Discussion

        WITH DISTINCT pid, pprops, workItemCount,
            COLLECT(DISTINCT epic) as epics,
            COLLECT(DISTINCT story) as stories,
            COLLECT(DISTINCT task) as tasks,
            COLLECT(DISTINCT requirement) as requirements,
            COLLECT(DISTINCT ar) as architecturalRequirements,
            COLLECT(DISTINCT sc) as systemContexts,
            COLLECT(DISTINCT c) as containers,
            COLLECT(DISTINCT comp) as components,
            COLLECT(DISTINCT des) as designs,
            COLLECT(DISTINCT interface) as containerInterfaces,
            COLLECT(DISTINCT comp_interface) as componentInterfaces

        RETURN {
            project_setup: {
                description: pprops.Description IS NOT NULL,
                objective: pprops.Objective IS NOT NULL,
                scope: pprops.Scope IS NOT NULL,
                architecturePattern: pprops.ArchitecturePattern IS NOT NULL,
                architectureStrategy: pprops.ArchitectureStrategy IS NOT NULL,
                teamComposition: pprops.TeamComposition IS NOT NULL,
                additionalDetails: pprops.AdditionalDetails IS NOT NULL
            },
            workItems: {
                task: workItemCount
            },
            architecture: {
                Requirement: {
                    Description: ANY(r IN requirements WHERE r.Description IS NOT NULL),
                    functional_requirements: ANY(r IN requirements WHERE r.functional_requirements IS NOT NULL),
                    architectural_requirements: ANY(r IN requirements WHERE r.architectural_requirements IS NOT NULL)
                },
                systemContext: {
                    Description: ANY(sc IN systemContexts WHERE sc.Description IS NOT NULL),
                    Users: ANY(sc IN systemContexts WHERE sc.Users IS NOT NULL),
                    ExternalSystems: ANY(sc IN systemContexts WHERE sc.ExternalSystems IS NOT NULL),
                    SystemContextDiagram: ANY(sc IN systemContexts WHERE sc.SystemContextDiagram IS NOT NULL),
                    ContainerDiagram: ANY(sc IN systemContexts WHERE sc.ContainerDiagram IS NOT NULL)
                },
                container: [c IN containers | c.Title],
                component: [comp IN components | {
                    Title: comp.Title,
                    Root: [c IN containers WHERE (c)-[:HAS_CHILD|CONTAINS*]->(comp)][0].Title
                }],
                design: REDUCE(arr = [], des IN designs | 
                    CASE 
                        WHEN NOT des.Title IN arr 
                        THEN arr + {
                            Title: des.Title,
                            Root: [c IN containers WHERE (c)-[:HAS_CHILD|CONTAINS*]->(des)][0].Title
                        }
                        ELSE arr 
                    END
                ),
                interface: REDUCE(arr = [], interface IN (containerInterfaces + componentInterfaces) | 
                    CASE 
                        WHEN interface IS NOT NULL AND NOT interface.Title IN arr 
                        THEN arr + {
                            Title: interface.Title,
                            Root: [c IN containers WHERE (c)-[:HAS_CHILD|CONTAINS*]->(interface)][0].Title
                        }
                        ELSE arr 
                    END
                )
            },
            requirements: {
                epic: SIZE(epics),
                userStory: SIZE(stories)
            }
        } as data
    """

    try:
        cursor = await node_db.async_run(
            unified_query, 
            parameters={"project_id": project_id}
        )
        
        record = next(cursor, None)
        if not record:
            # Return empty data structure that matches expected format
            return {
                "project_setup": {
                    "description": False,
                    "objective": False,
                    "scope": False,
                    "architecturePattern": False,
                    "architectureStrategy": False,
                    "teamComposition": False,
                    "additionalDetails": False
                },
                "workItems": {
                    "task": 0
                },
                "architecture": {
                    "Requirement": {
                        "Description": False,
                        "functional_requirements": False,
                        "architectural_requirements": False
                    },
                    "systemContext": {
                        "Description": False,
                        "Users": False,
                        "ExternalSystems": False,
                        "SystemContextDiagram": False,
                        "ContainerDiagram": False
                    },
                    "container": [],
                    "component": [],
                    "design": [],
                    "interface": []
                },
                "requirements": {
                    "epic": 0,
                    "userStory": 0
                }
            }
            
        return record["data"] if record else {}
        
    except Exception as e:
        print(f"Error in flowline query: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Database query error: {str(e)}"
        )
@router.get("/flowline/{project_id}", response_model=dict)
async def get_timeline(
    project_id: int,
    node_db: NodeDB = Depends(get_node_db)
) -> dict:
    """Get flowline data for a project, including node counts."""
    try:
            mongo_handler = get_mongo_db(
                db_name=settings.MONGO_DB_NAME,
                collection_name='project_repositories'
            )

            _result = await mongo_handler.get_one(
                filter={
                    'project_id': project_id,
                },
                db=mongo_handler.db
            )
                
            if not _result:
                repositories = []
            else:
                repositories = _result.get('repositories', [])
            data =await flowline(project_id, node_db)
            # print(data)
            data['project_assets']= {"repo":len(repositories)}
            mongo_handler = get_mongo_db(
                db_name=settings.MONGO_DB_NAME,
                collection_name=tasks_collection_name
            ) 
            _result = list(mongo_handler.db[tasks_collection_name].find(
                {
                    'project_id': project_id,
                }
            ).sort("start_time", -1).limit(1) )  
            print(_result)
            if not _result:
                status="pending"
            else:
                status = _result[0].get('status',"pending")
            data['codegen_status']=status
            # responses = convert_counts_to_boolean(data)
            return format_flowline_data(data)

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch timeline data: {str(e)}"
        )
    

@router.get("/get_related_nodes/{node_id}/{node_type}/{relationship_type}")
async def get_related_nodes(
    node_id: int,
    node_type: str,
    relationship_type: str,
    node_db: NodeDB = Depends(get_node_db)
):
    try:
        # Verify node exists
        node = await node_db.get_node_by_id(node_id)
        if not node:
            raise HTTPException(status_code=404, detail="Node not found")
        
        # Construct and execute query to find related nodes with specified relationship
        query = f"""
        MATCH (p:{node_type})-[r:{relationship_type}]-(q) 
        WHERE ID(p) = $node_id AND NOT 'Unused' IN LABELS(q)
        RETURN ID(q) AS id, LABELS(q) AS labels, properties(q) AS properties, 
               TYPE(r) AS relationship_type, properties(r) AS relationship_properties
        """
        
        result = await node_db.async_run(query, node_id=node_id)
        related_nodes = result.data()
        
        return related_nodes
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get related nodes: {str(e)}")

@router.post("/start-project", response_class=StreamingResponse)
async def start_project(
    request: ProjectRequest,
    current_user = Depends(get_current_user),
    llm_service = Depends(get_llm_interface),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """
    Start a new project from a requirement string.
    Streams updates back to the client as the project is created and analyzed.
    
    Request body should contain:
    - requirement: string - The project requirement
    """
    requirement = request.requirement
    framework = request.framework
    platform = request.platform
    blueprint = request.blueprint

    project_node = None
    init_project_info = {}
    if blueprint is not None and blueprint.projectInfo is not None:
        print('Using projectInfo from blueprint')
        project_node = blueprint.projectInfo.model_dump()

    if blueprint is not None:
        # Create a dictionary to store all blueprint data
        blueprint_data = blueprint.model_dump()
        
        # Remove projectInfo from blueprint_data to avoid duplication
        if 'projectInfo' in blueprint_data:
            del blueprint_data['projectInfo']
            
        init_project_info.update(blueprint_data)    


    print(f"Requirement: {project_node}")  # Debugging
    node_db = get_node_db()
    start_time = time.time()
        
    if not requirement:
        raise HTTPException(status_code=400, detail="Requirement string is required")
    
    async def stream_project_creation(project_node, init_project_info) -> AsyncGenerator[bytes, None]:
        try:
            yield format_response_for_stream({
                "status": "processing", 
                "message": "Analyzing requirement and generating project structure..."
            })
            
            # OPTIMIZATION: Create project node with a temporary name immediately
            # This allows us to start working with the project ID right away
            temp_project_name = f"Project-{generate_timestamp()}"
            
            # Create project node with temporary name
            properties = {
                'Title': temp_project_name,
                'Description': requirement,
                'Type': NodeType.PROJECT.value.lower(),
                'created_by': current_user.get('cognito:username'),
                'created_at': generate_timestamp(),
                'Requirement': requirement,
                'status': 'initializing'  # Mark as initializing until LLM response
            }

            if not project_node :
                # Create the project node first to get an ID to work with
                print('Creating new project node')
                project_node = await node_db.create_node([NodeType.PROJECT.value], properties)

            if init_project_info and isinstance(init_project_info, dict):
                keymap = {
                    "layoutDescription": "Layout_Description", 
                    "colors": "Colors", 
                    "features": "Features",
                    "projectTitle": "Title",
                    "description": "Description",
                    "techStack":"Tech_Stack",
                    "theme": "Theme",
                    "estimatedTime": "Estimated_Time",
                    "complexity": "Complexity",
                }

                update_data = {
                    'Init_project_info': json.dumps(init_project_info),
                    'configuration_state': 'configured'
                }

                for key, value in init_project_info.items():
                    db_key = keymap.get(key, key)
                    if key == 'id' or key == 'name':
                        continue
                    if isinstance(value, dict) or isinstance(value, list):
                        # Convert complex objects (dict/list) to JSON strings
                        update_data[db_key] = json.dumps(value)
                    elif value is None:
                        # Handle None values
                        update_data[db_key] = ""
                    else:    
                        update_data[db_key] = value

                resp = await node_db.update_node_by_id(project_node['id'], update_data, NodeType.PROJECT.value.lower())

            if not project_node:
                yield format_response_for_stream({
                    "status": "error",
                    "message": "Failed to create project node",
                    "end": True
                })
                return
                
            # Extract project ID from the response
            project_id = project_node["id"]
            # create a asynio task
            stage = get_stage(settings)
            safe_name = str(get_tenant_id()) + str(project_id)
            if stage == "dev":
                # Create project in the background
                background_tasks.add_task(create_project_for_dev, safe_name)
            elif stage == "qa":
                background_tasks.add_task(create_project_for_qa, safe_name)
            elif stage == "pre_prod":
                background_tasks.add_task(create_project_for_beta, safe_name)
                
            yield format_response_for_stream({
                "status": "processing", 
                "message": f"Project created with ID: {project_id}",
                "project_id": project_id,
                "project_name": temp_project_name
            })
            
            # OPTIMIZATION: Only use LLM to generate the project name - SINGLE STREAMLINED LLM CALL
            system_prompt = "You are an expert system architect who creates concise and relevant project names."
            user_prompt = f"""
            Based on the following requirement, generate a suitable project name.

            Requirement: "{requirement}"

            Format your response as a JSON with the following structure:
            {{
                "project_name": "Generated Project Name"
            }}
            """
            
            # Start creating basic structure concurrently with LLM call
            yield format_response_for_stream({
                "status": "processing", 
                "message": "Creating basic project structure ..."
            })
            
            # Launch LLM call - don't await it yet
            llm_task = asyncio.create_task(
                llm_service.llm_interaction_wrapper(
                    messages=[],
                    user_prompt=user_prompt,
                    system_prompt=system_prompt,
                    response_format={'type': 'json_object'},
                    model='gpt-4.1-nano',
                    stream=False
                )
            )
                
            # OPTIMIZATION: Prepare and execute all node creation tasks in parallel
            # Level 1 nodes - directly connected to project
            level1_nodes_properties = [
                # 1. RequirementRoot
                {
                    "labels": ["RequirementRoot", "Requirement"],
                    "properties": {
                        "Title": f"Root requirement for {temp_project_name}",
                        "Description": requirement,
                        "Type": "RequirementRoot",
                        "created_by": current_user.get('cognito:username'),
                        "created_at": generate_timestamp(),
                        "project_id": project_id
                    }
                },
                # 2. ArchitectureRoot
                {
                    "labels": ["ArchitectureRoot", "Architecture"],
                    "properties": {
                        "Title": f"Root architecture for {temp_project_name}",
                        "Description": requirement,
                        "Type": "ArchitectureRoot",
                        "created_by": current_user.get('cognito:username'),
                        "created_at": generate_timestamp(),
                        "project_id": project_id
                    }
                },
                # 3. WorkItemRoot
                {
                    "labels": ["WorkItemRoot", "WorkItem"],
                    "properties": {
                        "Title": f"Root work item for {temp_project_name}",
                        "Description": requirement,
                        "Type": "WorkItemRoot",
                        "created_by": current_user.get('cognito:username'),
                        "created_at": generate_timestamp(),
                        "project_id": project_id
                    }
                },
                # 4. SystemContext - TEMPLATED INSTEAD OF LLM GENERATED
                {
                    "labels": ["SystemContext"],
                    "properties": {
                        "Title": f"System Context for {temp_project_name}",
                        "Description": f"Description of system context for {temp_project_name}",
                        "Type": "SystemContext",
                        "created_by": current_user.get('cognito:username'),
                        "created_at": generate_timestamp(),
                        "project_id": project_id
                    }
                }
            ]
            
            # Create Level 1 nodes
            level1_node_tasks = []
            for node_props in level1_nodes_properties:
                level1_node_tasks.append(node_db.create_node(node_props["labels"], node_props["properties"]))
            
            # Execute level 1 node creation in parallel
            level1_nodes = await asyncio.gather(*level1_node_tasks)
            
            # Extract level 1 nodes
            requirement_node = level1_nodes[0]
            architecture_node = level1_nodes[1]
            work_item_node = level1_nodes[2]
            system_context_node = level1_nodes[3]
            
            # Create level 1 relationships in parallel
            level1_relationship_tasks = [
                node_db.create_relationship(project_id, requirement_node["id"], "HAS_CHILD"),
                node_db.create_relationship(project_id, architecture_node["id"], "HAS_CHILD"),
                node_db.create_relationship(project_id, work_item_node["id"], "HAS_CHILD"),
                node_db.create_relationship(project_id, system_context_node["id"], "HAS_CHILD"),
            ]
            
            # Execute level 1 relationships in parallel
            await asyncio.gather(*level1_relationship_tasks)
            
            yield format_response_for_stream({
                "status": "processing", 
                "message": "Created base project structure, ..."
            })
            
            # Level 2 nodes' properties - we'll create these after getting LLM response
            # Wait for LLM response now
            try:
                # Now await the LLM task to get the response
                completion_response = await llm_task
                
                # Extract the text content from the response
                if hasattr(completion_response, 'choices') and len(completion_response.choices) > 0:
                    if hasattr(completion_response.choices[0], 'message'):
                        response_text = completion_response.choices[0].message.content
                    else:
                        response_text = completion_response.choices[0].text
                else:
                    response_text = str(completion_response)
                
                # Parse the JSON response
                try:
                    llm_data = json.loads(response_text)
                    # Extract the LLM-generated project name
                    base_project_name = llm_data.get("project_name", "Project")
                    # Create unique project name by combining LLM name with project ID
                    project_name = f"{base_project_name}-{project_id}"
                except json.JSONDecodeError:
                    # Fallback if JSON parsing fails
                    project_name = f"Project-{project_id}"
                
                # Update the project node with the unique combined name
                await node_db.update_node_by_id(
                    project_id,
                    properties={"Title": project_name, "status": "active"}
                )
                # Update all level 1 nodes to use the real project name
                level1_update_tasks = [
                    # Update RequirementRoot node
                    node_db.update_node_by_id(
                        requirement_node["id"],
                        properties={"Title": f"Root requirement for {base_project_name}"}
                    ),
                    # Update ArchitectureRoot node
                    node_db.update_node_by_id(
                        architecture_node["id"],
                        properties={"Title": f"Root architecture for {base_project_name}"}
                    ),
                    # Update WorkItemRoot node
                    node_db.update_node_by_id(
                        work_item_node["id"],
                        properties={"Title": f"Root work item for {base_project_name}"}
                    ),
                    # Update SystemContext node
                    node_db.update_node_by_id(
                        system_context_node["id"],
                        properties={"Title": f"System Context for {base_project_name}","Description": f"Description of system context for {base_project_name}"}
                    )
                ]
                
                # Execute all level 1 node updates in parallel
                await asyncio.gather(*level1_update_tasks)
                yield format_response_for_stream({
                    "status": "processing", 
                    "message": f"Updated project name to: {project_name}",
                    "project_name": project_name
                })
                
                # Level 2 nodes' properties
                level2_nodes_properties = [
                    # 1. ArchitectureRequirement - TEMPLATED
                    {
                        "labels": ["ArchitecturalRequirement"],
                        "properties": {
                            "Title": f"Architecture Requirement for {base_project_name}",
                            "Description": f"Architectural requirements for {base_project_name}",
                            "Type": "ArchitecturalRequirement",
                            "created_by": current_user.get('cognito:username'),
                            "created_at": generate_timestamp(),
                            "project_id": project_id
                        }
                    },
                    # 2. Container - TEMPLATED
                    {
                        "labels": ["Container"],
                        "properties": {
                            "Title": f"Main Container for {base_project_name}",
                            "Description": f"Primary container for {base_project_name} implementation: {requirement}",
                            "Type": "Container",
                            "project_name": project_name,
                            "created_by": current_user.get('cognito:username'),
                            "created_at": generate_timestamp(),
                            "project_id": project_id,
                            "framework": framework.value if isinstance(framework, Framework) else framework,
                            "platform": platform.value if isinstance(platform, Platform) else platform,
                            "created_by": current_user.get('cognito:username'),
                            "created_at": generate_timestamp(),
                            
                        }
                    },
                    # 3. Component - TEMPLATED
                    {
                        "labels": ["Component", "Architecture"],
                        "properties": {
                            "Title": f"Core Component for {base_project_name}",
                            "Description": f"{requirement}",
                            "is_architectural_leaf": "yes",
                            "Type": "Component",
                            "project_name": project_name,
                            "created_by": current_user.get('cognito:username'),
                            "created_at": generate_timestamp(),
                            "project_id": project_id
                        }
                    }
                ]
                
                # Design node properties - TEMPLATED INSTEAD OF LLM GENERATED
                design_properties = {
                    "labels": ["Design"],
                    "properties": {
                        "Title": f"Design for Core Component",
                        "Description": f"{requirement}",
                        "Type": "Design",
                        "project_name": project_name,
                        "component_name": "Core Component",
                        "interfaces": "Default user interface elements and interaction points",
                        "algorithms": "Default implementation algorithms and approaches",
                        "pseudocode": "",
                        "framework": framework.value if isinstance(framework, Framework) else framework,
                        "platform": platform.value if isinstance(platform, Platform) else platform,
                        "created_by": current_user.get('cognito:username'),
                        "created_at": generate_timestamp(),
                        "project_id": project_id
                    }
                }
                
                yield format_response_for_stream({
                    "status": "processing", 
                    "message": "Creating detailed project structure..."
                })
                
                # Create Level 2 nodes in parallel
                level2_node_tasks = []
                for node_props in level2_nodes_properties:
                    level2_node_tasks.append(node_db.create_node(node_props["labels"], node_props["properties"]))
                
                # Create design node in parallel with others
                design_node_task = node_db.create_node(design_properties["labels"], design_properties["properties"])
                
                # Execute level 2 node creation in parallel
                level2_results = await asyncio.gather(*level2_node_tasks, design_node_task)
                
                # Extract nodes from results
                architecture_requirement_node = level2_results[0]
                container_node = level2_results[1]
                component_node = level2_results[2]
                design_node = level2_results[3]
                
                # Build level 2 relationship creation tasks
                level2_relationship_tasks = [
                    # Level 2 relationships
                    node_db.create_relationship(architecture_node["id"], architecture_requirement_node["id"], "HAS_CHILD"),
                    node_db.create_relationship(system_context_node["id"], container_node["id"], "HAS_CHILD"),
                    node_db.create_relationship(container_node["id"], component_node["id"], "HAS_CHILD"),
                    
                    # Design relationship
                    node_db.create_relationship(component_node["id"], design_node["id"], "HAS_CHILD")
                ]
                
                # Execute all level 2 relationships in parallel
                await asyncio.gather(*level2_relationship_tasks)

                yield format_response_for_stream({
                    "status": "processing", 
                    "message": "Created project structure with Architectural Requirement, System Context, Container and Component"
                })
                
                workspace_start_time = time.time()
                
                # Start workspace
                async for message in stream_start_workspace_status(
                    project_id=project_id, 
                    container_id=container_node['id'], 
                    session_name=project_name, 
                    description=requirement,
                    mongo_db=get_mongo_db(), 
                    node_db=node_db, 
                    current_user=current_user, 
                    new_repo_creation=True
                ):
                    yield message

                # Calculate elapsed time after workspace is created
                workspace_end_time = time.time()
                workspace_elapsed_time = workspace_end_time - workspace_start_time
                workspace_elapsed_seconds = int(workspace_elapsed_time)

                before_workspace = workspace_start_time - start_time
                project_creation_seconds = int(before_workspace)
                
                yield format_response_for_stream({
                    "status": "complete", 
                    "message": "Project creation complete with full design hierarchy",
                    "project_id": project_id,
                    "container_id": container_node['id'],
                    "component_id": component_node['id'],
                    "project_name": project_name,
                    "project_creation_time_seconds": project_creation_seconds,
                    "workspace_creation_time_seconds": workspace_elapsed_seconds

                })
                
            except json.JSONDecodeError as e:
                yield format_response_for_stream({
                    "status": "error", 
                    "message": f"Failed to parse project name from LLM: {str(e)}"
                })
                return
            except Exception as e:
                yield format_response_for_stream({
                    "status": "error", 
                    "message": f"Error processing project creation: {str(e)}"
                })
                return
            
        except Exception as e:
            error_message = str(e)
            print(f"Error in stream_project_creation: {error_message}")
            yield format_response_for_stream({
                "status": "error", 
                "message": f"Error creating project: {error_message}"
            })
    
    return StreamingResponse(
        stream_project_creation(project_node, init_project_info),
        media_type="text/event-stream",
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # This is for Nginx specifically
        }
    )


@router.get("/project/visibility/{tenant_id}/{project_id}", summary="Check if a project is public")
async def get_project_visibility(
    tenant_id: str,
    project_id: int,
    current_user = Depends(get_current_user),
    
):
    
    if tenant_id.startswith("default"):
        tenant_id = settings.KAVIA_B2C_CLIENT_ID
 
    """
    Check if a specific project is public or private.
    """
    # Connect to the public_projects collection
    mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME, collection_name="public_projects")
    mongo_handler = mongo_db
    
    # Verify project exists
    db = get_node_db(tenant_id=tenant_id)
    project = await db.get_node_by_id(project_id, node_type="Project")
    print(f"Project: {project}")
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
     
    # Check if project is in the public projects collection
    try:
        public_project = await mongo_handler.get_one(
                filter={"project_id": project_id, "tenant_id": tenant_id},
                db=mongo_handler.db
            )
    except Exception as e:
        print(f"Error getting public project: {e}")
        public_project = {}

    # if tenant_id.startswith("")
    
    # Check visibility status - project is public if it exists and visibility is "public"
    is_public = bool(public_project and public_project.get("visibility", "") != "private")
    
    # Serialize ObjectId to string if public_project exists
    if public_project and '_id' in public_project:
        public_project['_id'] = str(public_project['_id'])
        # Convert any other ObjectId that might be in the document
        for key, value in list(public_project.items()):
            if hasattr(value, '_type_marker') and str(value._type_marker) == 'ObjectId':
                public_project[key] = str(value)
    else:
        if public_project==None:
            if tenant_id==settings.KAVIA_B2C_CLIENT_ID: 
                return {
                "project_id": project_id,
                "tenant_id": tenant_id,
                "is_public": True,
                "visibility": "public",
                "public_info": None
                }
            else:
                return {
                "project_id": project_id,
                "tenant_id": tenant_id,
                "is_public": False,
                "visibility": "private",
                "public_info": None
                }

    return {
        "project_id": project_id,
        "tenant_id": tenant_id,
        "is_public": is_public,
        "visibility": "public" if is_public else "private",
        "public_info": public_project if is_public else None
    }


@router.post("/project/visibility", summary="Change project visibility (public/private)")
async def change_project_visibility(
    request: ProjectVisibilityRequest,
    current_user = Depends(get_current_user),
):
    """
    Change a project's visibility by updating its status in the public_projects collection.
    Setting make_public=true adds or updates the project with public visibility,
    setting it to false updates it with private visibility.
    """
    project_id = request.project_id
    make_public = request.make_public
    tenant_id = get_tenant_id()
    
    if tenant_id.startswith("default"):
        tenant_id = settings.KAVIA_B2C_CLIENT_ID
    
    mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME, collection_name="public_projects")
    
    # Connect to the public_projects collection
    mongo_handler = mongo_db
    
    # Get project details from Neo4j with creator information
    db = get_node_db()
    
    # Query project with creator information
    query = f"""
    MATCH (p:Project) WHERE ID(p) = {project_id}
    OPTIONAL MATCH (u:User) WHERE u.Username = p.created_by
    RETURN ID(p) AS id, p.Title AS Title, p.Description AS Description, 
           p.created_at AS created_at, p.created_by AS created_by,
           u.Name AS creator_name, u.Email AS creator_email, u.Picture AS creator_picture
    """
    
    result = await db.async_run(query)
    project_data = result.data()
    
    if not project_data or len(project_data) == 0:
        raise HTTPException(status_code=404, detail="Project not found")
    
    project = {
        "id": project_data[0]["id"],
        "properties": {
            "Title": project_data[0]["Title"],
            "Description": project_data[0]["Description"],
            "created_at": project_data[0]["created_at"],
            "created_by": project_data[0]["created_by"],
            "creator_name": project_data[0]["creator_name"],
            "creator_email": project_data[0]["creator_email"],
            "creator_picture": project_data[0]["creator_picture"]
        }
    }
    
    # Check if project is already in the public projects collection
    existing_public_project = await mongo_handler.get_one(
        filter={"project_id": project_id, "tenant_id": tenant_id},
        db=mongo_handler.db
    )
    
    current_timestamp = generate_timestamp()
    current_username = current_user.get("cognito:username")
    
    # If make_public is True, add or update the project with public visibility
    if make_public:
        # If the project doesn't exist in the collection, create it
        if not existing_public_project:
            # Create a public project record
            public_project = PublicProjectModel(
                project_id=project_id,
                title=project["properties"].get("Title", ""),
                tenant_id=tenant_id,
                description=project["properties"].get("Description", ""),
                created_at=project["properties"].get("created_at", ""),
                created_by=project["properties"].get("created_by", ""),
                creator_name=project["properties"].get("creator_name", ""),
                creator_email=project["properties"].get("creator_email", ""),
                creator_picture=project["properties"].get("creator_picture", ""),
                made_public_at=current_timestamp,
                made_public_by=current_username,
                visibility="public"
            )
            
            # Insert into MongoDB
            result = await mongo_handler.insert(
                element=public_project.model_dump(),
                db=mongo_handler.db
            )
            
            return {"status": "success", "message": f"Project {project_id} is now public", "made_public": True}
        else:
            # Project exists, update its visibility to public
            update_data = {
                "visibility": "public",
                "made_public_at": current_timestamp,
                "made_public_by": current_username
            }
            
            # Update MongoDB record
            result = await mongo_handler.update_one(
                filter={"project_id": project_id, "tenant_id": tenant_id},
                element=update_data,
                db=mongo_handler.db
            )
            
            return {"status": "success", "message": f"Project {project_id} is now public", "made_public": True}
    
    # If make_public is False, update the visibility to private if it exists
    else:
        # If the project exists in the collection, update its visibility
        if existing_public_project:
            update_data = {
                "visibility": "private",
                "made_private_at": current_timestamp,
                "made_private_by": current_username
            }
            
            # Update MongoDB record
            result = await mongo_handler.update_one(
                filter={"project_id": project_id, "tenant_id": tenant_id},
                element=update_data,
                db=mongo_handler.db
            )
            
            return {"status": "success", "message": f"Project {project_id} is now private", "made_public": False}
        else:
            public_project = PublicProjectModel(
                project_id=project_id,
                title=project["properties"].get("Title", ""),
                tenant_id=tenant_id,
                description=project["properties"].get("Description", ""),
                created_at=project["properties"].get("created_at", ""),
                created_by=project["properties"].get("created_by", ""),
                creator_name=project["properties"].get("creator_name", ""),
                creator_email=project["properties"].get("creator_email", ""),
                creator_picture=project["properties"].get("creator_picture", ""),
                made_public_at=current_timestamp,
                made_public_by=current_username,
                visibility="private"
            )
            result = await mongo_handler.insert(
                element=public_project.model_dump(),
                db=mongo_handler.db
            )
            
            # Project doesn't exist in the collection, so it's already effectively private
            return {"status": "success", "message": f"Project {project_id} is already private", "made_public": False}
        
        
@router.get("/projects/public", summary="Get list of public projects")
async def get_public_projects(
    skip: int = 0,
    limit: int = 20,
    current_user = Depends(get_current_user),
):
    """
    Retrieve a paginated list of all public projects.
    """
    public_projects = []
    mongo_handler = get_mongo_db(
        db_name=KAVIA_ROOT_DB_NAME, 
        collection_name="public_projects"
    )
    
    # Define the filter condition for public projects
    filter_condition = {"visibility": "public"}
    email = current_user.get("email")
    try:
        # Get paginated results
        cursor = await mongo_handler.get_all_documents(
            filter=filter_condition,
            db=mongo_handler.db
        )
        for doc in cursor:
            serialized_doc = doc.copy()
            if '_id' in serialized_doc:
                serialized_doc['_id'] = str(serialized_doc['_id'])
            
            # Convert any ObjectId fields to strings
            for key, value in serialized_doc.items():
                if hasattr(value, '_type_marker') and str(value._type_marker) == 'ObjectId':
                    serialized_doc[key] = str(value)
            
            public_projects.append(serialized_doc)
        # Convert cursor to list
        node_db = get_node_db(tenant_id=settings.KAVIA_B2C_CLIENT_ID)
        projects = await node_db.get_projects()
        for project in projects:
            project['tenant_id'] = settings.KAVIA_B2C_CLIENT_ID
            public_projects.append(project)

        print(f"Retrieved {len(projects)} projects from B2C tenant")
        print(f"Projects: {projects}")
        # Process and serialize the results
        if email:
            print(f"Filtering projects by email: {email}")
            # Filter projects by creator email
            filtered_projects = [project for project in public_projects if project.get("creator_email") != email]
        else:
            filtered_projects = public_projects 
            
        return {
        "public_projects": filtered_projects,
        "pagination": {
            "total": len(filtered_projects),
            "skip": skip,
            "limit": limit
        }
    }

        
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"Error fetching public projects: {str(e)}"
        )


@router.post("/clone_source_node")
async def create_node(source_node_id: int, request: CloneNode, current_user = Depends(get_current_user)):
    try:
        response = await clone_node(source_node_id, current_user, request.title)
        return {"message":"Project cloned successfully","node_info":response}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")


@router.post('/start_project_init')
async def start_project_init(
    request: StartProjectInit,
    current_user = Depends(get_current_user),
    node_db: NodeDB = Depends(get_node_db)
):
    try:
        if request.type == 'direct_code_gen':
            temp_project_name = f"Project-{generate_timestamp()}"
            response_format = ProjectInfoDirectCodeGen
            _system_prompt="Consider the user input while deriving the properties. Make assumption only for required fields when there is lack of sufficient information (optional can be skipped).\n\nAnd share data in proper response_format as requested"

        elif request.type == 'new_project_manual':
            temp_project_name = request.title
            response_format = ProjectInfoManual
            _system_prompt=f"Consider the user input while deriving the properties. Make assumption only for required fields when there is lack of sufficient information (optional can be skipped).\n\nAnd share data in proper response_format as requested\n\nWhen selecting architecturePattern refer this info :\n{ArcitectureInfo} "

        else:
            # Prepare node properties
            temp_project_name = f"Project-{generate_timestamp()}"
            response_format = ProjectInfoDirectCodeGen
            _system_prompt="Consider the user input while deriving the properties. Make assumption only for required fields when there is lack of sufficient information (optional can be skipped).\n\nAnd share data in proper response_format as requested"

        # Set up LLM interface
        llm = LLMInterface(get_path(), 'discussion', current_user, None, 
                            'configuration', mongo_handler=get_mongo_db())
        # model_touse = LLMModel.bedrock_claude_3_5_haiku.value
        model_touse = LLMModel.gpt_4_1.value

        properties = {
            'Title': temp_project_name,
            'Description': request.usercomment,
            'Type': NodeType.PROJECT.value.lower(),
            'created_by': current_user.get('cognito:username'),
            'created_at': generate_timestamp(),
            'Requirement': request.usercomment,
            'status': 'initializing'
        }

        # Define coroutines to run in parallel
        async def create_node_task():
            created_node = await node_db.create_node([NodeType.PROJECT.value], properties)
            return created_node
        
        async def llm_task():
            # Uncomment the following lines to check which parameters are supported by the selected model.
            # This can be useful for debugging or extending model capabilities but is commented out to improve runtime performance.

            # params = get_supported_openai_params(model=model_touse)

            # Check if the selected model supports response schemas (useful for structured outputs).
            # Currently disabled to avoid unnecessary overhead during regular execution.

            # response = supports_response_schema(model=model_touse, custom_llm_provider="bedrock")

            # Debug prints to inspect the supported parameters and response schema support.
            # print('Supported parameters:', params)
            # print('Supports response schema:', response)
            
            resp = await llm.llm_interaction_wrapper(
                messages=[],
                user_prompt=request.usercomment,
                system_prompt=_system_prompt,
                response_format=response_format,
                model=model_touse,
                stream=False
            )

            return resp
        
        # Run both tasks concurrently
        project_node, llm_response = await asyncio.gather(
            create_node_task(),
            llm_task()
        )
        
        llm_response_obj = json.loads(llm_response)

        keymap = {
            "layoutDescription": "Layout_Description", 
            "colors": "Colors", 
            "features": "Features",
            "projectTitle": "Title",
            "description": "Description",
            "architecturePattern" : "ArchitecturePattern"
        }

        update_data = {
            'Init_project_info': llm_response
            # 'configuration_state': 'configured'
        }

        for key, value in llm_response_obj.items():
            db_key = keymap.get(key, key)
            if isinstance(value, dict) or isinstance(value, list):
                # Convert complex objects (dict/list) to JSON strings
                update_data[db_key] = json.dumps(value)
            elif value is None:
                # Handle None values
                update_data[db_key] = ""
            else:    
                update_data[db_key] = value

        # Update the node with LLM response
        resp = await node_db.update_node_by_id(
            project_node['id'], 
            update_data,
            NodeType.PROJECT.value.lower()
        )
        
        project_node['properties']['Description'] = llm_response_obj.get('description','')
        project_node['properties']['Title'] = llm_response_obj.get('projectTitle',temp_project_name)

        return {"llmResponse": llm_response_obj, "projectNodeInfo": project_node}
    
    except Exception as e:
        import traceback
        traceback.print_exc()
        print('Error in start_project_init', str(e))
        return {
            "status": "error", 
            "message": f"Error creating project: {str(e)}"
        }
