from app.connection.establish_db_connection import get_node_db, NodeDB ,get_vector_db  , connect_mongo_db
from app.connection.llm_init import get_llm_interface
from app.core.data_model_helper import data_model

from app.utils.auth_utils import get_current_user
from fastapi import APIRouter, Depends, Body , Query, Request, WebSocket, responses, HTTPException , BackgroundTasks
from typing import List, Union, Annotated,Dict
from app.core.function_schema_generator import load_json_file
from fastapi.responses import JSONResponse
from datetime import datetime
from typing import Optional
import json
import logging

logger = logging.getLogger(__name__)

_SHOW_NAME = "architecture"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)


llm_interface = get_llm_interface()

@router.get("/{architecture_id}")
async def get_architecture_by_id(architecture_id: int, project_id: int, db: NodeDB = Depends(get_node_db)):
    
    response = await db.get_architecture(architecture_id, project_id)
    if response:
        type = response.get("properties",{}).get("Type", "Architecture")
        response['ui_metadata'] = data_model.get(type, data_model.get("Architecture"))['ui_metadata']
    
    return response

@router.get("/architecture_leaf_nodes/{project_id}")
async def get_architecture_leaf_nodes(project_id: int,db: NodeDB = Depends(get_node_db)):
    """
    Get all leaf nodes of the architecture
    """
    response = await db.get_architecture_leaf_nodes(project_id)
    return response

@router.post("/design/")
async def create_design_node(architecture_id:int, design_properties: dict = {}, db: NodeDB = Depends(get_node_db)):
    """
    Create a design node
    """
    if design_properties:
        design_properties = design_properties.get("design_properties") 
    response = await db.create_design_node(architecture_id, design_properties)
    return response

@router.get("/design_nodes/{architecture_id}")
async def get_design_nodes(architecture_id: int, db: NodeDB = Depends(get_node_db)):
    """
    Get all design nodes of the architecture
    """
    response = await db.get_design_nodes(architecture_id)
    if response:
        response[0]["ui_metadata"] = data_model["model"]["Design"]["ui_metadata"]
        return response
    return []

@router.get("/root_architecture/{project_id}")
async def get_root_architecture(project_id:int, db:NodeDB = Depends(get_node_db)):
    result = await db.get_root_architecture(project_id)
    ui_metadata = data_model["model"]["Architecture"]["ui_metadata"]
    result["ui_metadata"] = ui_metadata
    return result

@router.get("/child_architectures/{architecture_id}")
async def get_child_architectures(architecture_id:int, db: NodeDB = Depends(get_node_db)):
    result = await db.get_child_architectures(architecture_id)
    return result

@router.get("/get_architectural_element_by_property/{parent_architecture_id}")
async def get_architectural_element_by_property(
    parent_architecture_id: int,
    db: NodeDB = Depends(get_node_db),
    property_name: str = Query(..., alias="property_name"),
    property_value: str = Query(..., alias="property_value")
):
    result = await db.get_architectural_element_by_property(parent_architecture_id, properties={property_name: property_value})
    return result

@router.get("/interfaces_with_list/{project_id}")
async def get_interfaces_with_list(project_id: int, db: NodeDB = Depends(get_node_db)):
    result = await db.get_interfaces_with_relationship_list(project_id)
    return result

@router.post("/interface/")
async def create_interface_node_for_edge(
    project_id: int,
    relationship_id: int,
    interface_properties: dict = Body(...),
    db: NodeDB = Depends(get_node_db)
):
    try:
        response = await db.create_interface_node(project_id, relationship_id, interface_properties)
        if response:
            return {"message": "Interface node created successfully", "data": response}
        else:
            raise HTTPException(status_code=404, detail="Failed to create interface node")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")

@router.get("/interface/{interface_id}")
async def get_interface(interface_id:int, is_relationship: Optional[bool]= Query(None, description="Set this to true if you want to retrive a edge") ,db: NodeDB = Depends(get_node_db)):
    
    if is_relationship:
        result = await db.get_edge_by_id(interface_id, "INTERFACES_WITH")
        if result and result.get("properties") and result.get("properties").get("interface_node_id"):
            interface_id = result.get("properties").get("interface_node_id")
            interface_node = await db.get_node_by_label_id(interface_id, "Interface")
            result["interface_node"] = interface_node
            
    else:   
        result = await db.get_node_by_label_id(interface_id, "Interface")
    
    if not result:
        return JSONResponse(status_code=200, content={"message": "Interface not found"})

    result["ui_metadata"] = data_model["model"]["Interface"]["ui_metadata"]
        
    return result

@router.get("/interface_childs/{interface_id}")
async def get_interface_childs(interface_id:int, db: NodeDB = Depends(get_node_db)):
    result = await db.get_interface_childs(interface_id)
    return result

# @router.get("/interfaces/{project_id}")
# async def get_interfaces(project_id:int, db: NodeDB = Depends(get_node_db)):
#     result = await db.get_interfaces_list(project_id)
#     return result

@router.get("/interfaces_with/{architecture_id}")
async def get_interfaces_with(architecture_id:int, db: NodeDB = Depends(get_node_db)):
    result = await db.interfaces_with(architecture_id)
    return result

@router.get("/architectural_requirement/{project_id}")
async def get_architectural_requirement(project_id: int, db: NodeDB = Depends(get_node_db)):
    result = await db.get_architectural_requirement(project_id)
    result[0]["ui_metadata"] = data_model["model"]["ArchitecturalRequirement"]["ui_metadata"]
    return result

@router.get("/api_docs/{project_id}")
async def get_api_docs(project_id:int, db: NodeDB = Depends(get_node_db)):
    result = await db.get_architecture_documentation(project_id)
    return result

@router.get("/sad/{project_id}")
async def get_sad(project_id:int, db: NodeDB = Depends(get_node_db)):
    result = await db.get_sad_documentation(project_id)
    return result


# @router.get("/interfaces/{project_id}")
# async def get_interfaces_based_project(project_id: int, db: NodeDB = Depends(get_node_db)):
#     result = await db.get_interfaces_based_project(project_id)
#     result[0]["ui_metadata"] = data_model["model"]["Interface"]["ui_metadata"]
#     return result

@router.get("/interfaces/{project_id}")
async def get_interfaces_based_project(project_id: int, db: NodeDB = Depends(get_node_db)):
    result = await db.get_interfaces_based_project(project_id)
    
    # If no interfaces found, return empty list
    if not result:
        return []
        
    
    return result



@router.get("/architecture_requirements/{project_id}")
async def get_architectural_requirement_with_children(
    project_id: int, 
    db: NodeDB = Depends(get_node_db)
):
    try:
        result = await db.get_architectural_requirement_with_children(project_id)
        print("RESULT",result)
        if not result:
            raise HTTPException(status_code=404, detail="Architectural Requirement not found")
        
        result["ui_metadata"] = data_model["model"]["ArchitecturalRequirement"]["ui_metadata"]
        
        return result
    except Exception as e:
        logger.error(f"Error in get_architectural_requirement_with_children: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/architectural_requirements_with_related_userstories/{project_id}")
async def get_related_userstories(
    project_id: int, 
    db: NodeDB = Depends(get_node_db)
):
    try:
        result = await db.get_related_userstories(project_id)
        if not result:
            raise HTTPException(status_code=404, detail="Architectural Requirement not found")
        
        result["ui_metadata"] = data_model["model"]["ArchitecturalRequirement"]["ui_metadata"]
        
        return result
    except Exception as e:
        logger.error(f"Error in get_architectural_requirement_with_children_and_related: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
    

@router.get("/related_nodes/{project_id}/{child_id}/{child_type}")
async def get_related_nodes(
    project_id: int,
    child_id: int,
    child_type: str,
    db: NodeDB = Depends(get_node_db)
):
    try:
        result = await db.get_related_nodes(project_id, child_id, child_type)
        if not result:
            raise HTTPException(status_code=404, detail="Node or related nodes not found")
        
        return result
    except Exception as e:
        logger.error(f"Error in get_related_nodes: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
    

@router.get("/architecture_relationships/{project_id}/{query_type}")
async def get_architecture_relationships(
    project_id: int,
    query_type: str,
    db: NodeDB = Depends(get_node_db)
):
    try:
        result = await db.get_architecture_relationships(project_id, query_type)
        return result  # This will now return an empty list instead of None when no results are found
    except ValueError as ve:
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        logger.error(f"Error in get_architecture_relationships: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
    
    
### New C4 model integration endpoints

## Retrieves the system_context associated with the project and its related child container nodes
    

@router.get("/system_context_with_containers/{project_id}")
async def get_system_context_with_containers(
    project_id: int,
    db: NodeDB = Depends(get_node_db)
):
    try:
        result = await db.get_system_context_with_containers(project_id)
        if result is None:
            return {
                "data": None,
                "message": f"No system context found for project_id: {project_id}"
            }
        
        result["model"] = {
            "SystemContext": data_model["model"]["SystemContext"],
            "Container": data_model["model"]["Container"]
        }
        
        return result
    except Exception as e:
        logger.error(f"Error in get_system_context_with_containers: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

## Retrieves the specific container nodes and its related child component nodes

@router.get("/container_with_components/{project_id}/{container_id}")
async def get_container_with_components(
    project_id: int,
    container_id: int,
    db: NodeDB = Depends(get_node_db)
):
    try:
        result = await db.get_container_with_components(project_id, container_id)
        if not result:
            raise HTTPException(status_code=404, detail="Container or Components not found")
        
        result["model"] = {
            "Container": data_model["model"]["Container"],
            "Component": data_model["model"]["Component"],
            "Interface": data_model["model"]["Interface"]
        }
        
        return result
    except Exception as e:
        logger.error(f"Error in get_container_components: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
    
    
## Retrieves the specific component nodes and its related child nodes

@router.get("/component_with_associated_children/{project_id}/{component_id}")
async def get_component_with_children(
    project_id: int,
    component_id: int,
    db: NodeDB = Depends(get_node_db)
):
    try:
        result = await db.get_component_with_children(project_id, component_id)
        if not result:
            raise HTTPException(status_code=404, detail="Component or Children not found")

        result["model"] = {
            "Component": data_model["model"]["Architecture"],
            "Design": data_model["model"]["Design"],
            "Interface": data_model["model"]["Interface"]
        }
        
        return result
    except Exception as e:
        logger.error(f"Error in get_component_with_children: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

## Retrieves the specific design nodes and its related child nodes ( from the component node itself)


@router.get("/design_with_children/{project_id}/{component_id}/{design_id}")
async def get_design_with_children(
    project_id: int,
    component_id: int,
    design_id: int,
    db: NodeDB = Depends(get_node_db)
):
    try:
        result = await db.get_design_with_children(project_id, component_id, design_id)
        if not result:
            raise HTTPException(status_code=404, detail="Design or Children not found")
        
        result["model"] = {
            "Design": data_model["model"]["Design"],
            "ClassDiagram": data_model["model"]["ClassDiagram"],
            "Diagram": data_model["model"]["Diagram"],
            "DesignElement": data_model["model"]["DesignElement"],
            "StateLogic": data_model["model"]["StateLogic"],
            "StateDiagram": data_model["model"]["StateDiagram"],
            "RobustnessTest": data_model["model"]["RobustnessTest"],
            "Test": data_model["model"]["Test"]
        }
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_design_with_children: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
    
## Retrieves all the components node for the specific project

@router.get("/container_with_all_components/{project_id}")
async def get_container_with_all_components(
    project_id: int,
    db: NodeDB = Depends(get_node_db)
):
    try:
        result = await db.get_container_with_all_components(project_id)
        if not result:
            raise HTTPException(status_code=404, detail="Container or Components not found")
        
        result["model"] = {
            "Container": data_model["model"]["Container"],
            "Component": data_model["model"]["Component"]
        }
        
        return result
    except Exception as e:
        logger.error(f"Error in get_container_components: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
    
## Retrieves all the child node (design and interfaces) for the specific project

@router.get("/component_with_associated_all_children/{project_id}")
async def get_component_with_children(
    project_id: int,
    db: NodeDB = Depends(get_node_db)
):
    try:
        result = await db.get_component_with_all_children(project_id)
        if not result:
            raise HTTPException(status_code=404, detail="Component or Children not found")
        
        result["model"] = {
            "Design": data_model["model"]["Design"],
            "Interface": data_model["model"]["Interface"]
        }
        
        return result
    except Exception as e:
        logger.error(f"Error in get_component_with_children: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/component_interfaces/{project_id}/{component_id}")
async def get_interface_from_component(
    project_id: int,
    component_id: int,
    db: NodeDB = Depends(get_node_db)
):
    try:
        result = await db.get_interfaces_from_component(project_id, component_id)
        if not result:
            raise HTTPException(status_code=404, detail="Interfaces not found")
        
        result["model"] = {
            "Interface": data_model["model"]["Interface"]
        }
        
        return result
    except Exception as e:
        logger.error(f"Error in get_interface_from_component: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
    
    
# FastAPI Route
@router.get("/all_components_associated_with_project/{project_id}")
async def get_all_component_from_project(
    project_id: int,
    db: NodeDB = Depends(get_node_db)
):
    try:
        result = await db.get_all_component_from_project(project_id)
        if not result:
            return {
                "status": "success",
                "containers": [],
                "total_components": 0,
                "message": "No components found for this project"
            }
        
        result["model"] = {
            "Design": data_model["model"]["Design"],
            "Interface": data_model["model"]["Interface"]
        }
        
        return result
    except Exception as e:
        logger.error(f"Error in get_all_component_from_project: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/container_functional_requirements/{project_id}/{container_id}")
async def get_container_functional_requirements(
    project_id: int,
    container_id: int,
    db: NodeDB = Depends(get_node_db)
):
    try:
        result = await db.get_container_functional_requirements(project_id, container_id)
        # if not result:
        #     raise HTTPException(status_code=404, detail="Container or requirements not found")
        return result
    except Exception as e:
        logger.error(f"Error in get_container_functional_requirements: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/component_functional_requirements/{project_id}/{component_id}")
async def get_component_functional_requirements(
    project_id: int,
    component_id: int,
    db: NodeDB = Depends(get_node_db)
):
    try:
        result = await db.get_component_functional_requirements(project_id, component_id)
        if not result:
            return []
        return result
    except Exception as e:
        logger.error(f"Error in get_component_functional_requirements: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
    
@router.get("/component_deployments/{project_id}/{container_id}")
async def get_component_deployments(
    project_id: int,
    container_id: int,
    db: NodeDB = Depends(get_node_db)
):
    try:
        result = await db.get_component_deployments(project_id, container_id)
        if not result:
            raise HTTPException(status_code=404, detail="Container or deployments not found")
        return result
    except Exception as e:
        logger.error(f"Error in get_component_deployments: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")