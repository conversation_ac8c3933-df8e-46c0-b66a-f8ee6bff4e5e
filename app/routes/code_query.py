import asyncio
import json
import time
import traceback

from pydantic import BaseModel
from typing import  Dict, List, Optional
from fastapi import  APIRouter, Depends, Request, HTTPException,  BackgroundTasks
from fastapi.responses import StreamingResponse
from app.connection.establish_db_connection import get_mongo_db
from app.core.Settings import settings
from app.utils.auth_utils import get_current_user
import uuid
from app.utils.kg_inspect.knowledge import Knowledge, KnowledgeCodeBase
from app.utils.kg_inspect.knowledge_helper import Knowledge_Helper
from app.utils.kg_inspect.knowledge_reporter import Reporter
from code_generation_core_agent.agents.utilities import ModelNameSelector

import logging
import os
from datetime import datetime
from app.core.websocket.client import WebSocketClient
from datetime import datetime, timedelta
from app.utils.kg_inspect.answer_question import answer_question
from app.utils.kg_inspect.session_manager import KnowledgeInstance, SessionManager
from app.knowledge.redis_kg import getRedisKnowledge
from app.services.session_tracker import get_session_tracker

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

# Configure logging
logger = logging.getLogger('code_query')
logger.setLevel(logging.INFO)

# Create file handler
file_handler = logging.FileHandler('logs/code_query.log')
file_handler.setLevel(logging.INFO)

# Create formatter
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)

# Add handler to logger
logger.addHandler(file_handler)

_SHOW_NAME = "code_query"

router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)

#session model
class CodeQueryRequest(BaseModel):
    user_id: str
    tenant_id: str
    project_id: int
    build_ids: List[str]
    session_name:str = "untitled"
    description:str = ""

class KnowledgeSession:
    def __init__(self, session_id: str, codebase_paths: List[KnowledgeCodeBase]):
        self.session_id = session_id
        self.codebase_paths = codebase_paths
        self.created_at = datetime.now()
        self.last_accessed = datetime.now()
        self._lock = asyncio.Lock()
      
    async def update_last_accessed(self):
        async with self._lock:
            self.last_accessed = datetime.now()
              
    async def is_ready(self) -> bool:
        
        print("Checking the is ready : knowledge Session")
        """Check if all instances for this session are ready"""
        for path_info in self.codebase_paths:
            instance = session_manager.get_instance_by_session(self.session_id)

            if instance:
                print("Is ready : instance ")
                print(instance.is_initialized)
                print(instance.reporter)
                  
            if not instance or not instance.is_initialized or not instance.reporter.is_ready():
                print("Instance not present yet")
                
                return False
        return True 
          
session_manager = SessionManager()

async def wait_for_initialization(instance: KnowledgeInstance) -> bool:
    
    start_overall = time.time()
    performance_metrics = {}
    repository_metrics = {}
    
    max_wait_time = 600  # 10 minutes timeout
    wait_count = 0
    check_interval = 2  # seconds
    last_files_count = 0
    repo_file_counts = {}  # Track files processed per repository

    while wait_count < max_wait_time:
        current_files_count = 0
        current_repo_counts = {}
        
        # Update file count info
        if instance.knowledge_helper and instance.knowledge_helper.knowledge:
            knowledge = instance.knowledge_helper.knowledge
            if hasattr(knowledge, 'source_files'):
                current_files_count = len(knowledge.source_files)
                instance.files_processed = current_files_count
                
                # Count files per repository
                for file in knowledge.source_files:
                    # Extract repository information - find which codebase this file belongs to
                    code_base = knowledge._get_code_base_name(file)
                    if code_base:
                        if code_base not in current_repo_counts:
                            current_repo_counts[code_base] = 0
                        current_repo_counts[code_base] += 1
        
        current_time = time.time()
        timestamp = str(round(current_time - start_overall, 2))
        files_delta = current_files_count - last_files_count
        
        # Update overall metrics
        performance_metrics[timestamp] = {
            "files_processed": current_files_count,
            "new_files": files_delta,
            "files_per_second": int(round(files_delta / max(current_time - start_overall, 1.0), 0))
        }
        
        # Update per-repository metrics
        for repo_id, count in current_repo_counts.items():
            if repo_id not in repo_file_counts:
                repo_file_counts[repo_id] = 0
                
            repo_delta = count - repo_file_counts[repo_id]
            
            if repo_id not in repository_metrics:
                repository_metrics[repo_id] = {}
                
            repository_metrics[repo_id][timestamp] = {
                "files_processed": count,
                "new_files": repo_delta,
                "files_per_second": int(round(repo_delta / max(current_time - start_overall, 1.0), 0))
            }
            
            repo_file_counts[repo_id] = count
            
        last_files_count = current_files_count

        if instance.reporter.is_ready():
            instance.is_initialized = True
            
            # Final metrics
            total_time = time.time() - start_overall
            print(f"Initialization complete! Processed {instance.files_processed} files in {total_time:.2f} seconds")
            
            item = {
                    "bg_performance_metrics": performance_metrics,
                    "repository_metrics": repository_metrics
                }
            print(json.dumps(item, indent=2, default=str))

            monitor = get_mongo_db(
                db_name=settings.MONGO_DB_NAME,
                collection_name='monitor_code_query'
            )
            
            await monitor.update_one(
                {"session_id": instance.session_id},
                item,
                db=monitor.db
            )
            
            return True
        
        await asyncio.sleep(check_interval)
        wait_count += check_interval

    return False


import random
import string

def generate_random_prefix(length=6):
    """Generate a random string of specified length using letters and numbers"""
    characters = string.ascii_letters + string.digits
    return ''.join(random.choices(characters, k=length))

@router.get("/register_agent/{session_id}")
def register_agent(session_id: str):
    try:
        # Initialize WebSocket client and reporter
        ws_client = WebSocketClient(session_id, settings.WEBSOCKET_URI)
        reporter = Reporter(ws_client)
        reporter.initialize()
        
        return {
            "status": "success",
            "message": f"Agent successfully registered with session ID: {session_id}",
            "session_id": session_id
        }
        
    except ConnectionError as e:
        return {
            "status": "error",
            "message": f"Failed to establish WebSocket connection: {str(e)}",
            "session_id": session_id
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Failed to register agent: {str(e)}",
            "session_id": session_id
        }
    
@router.get("/get_available_code_query_models")
async def get_available_code_query_models(
    currect_user: dict = Depends(get_current_user) 
):
    #return the keys of the self.available_models dictionary from knowledge_reporter.py
    modelSelectorInstance = ModelNameSelector('GPT4.1')

    return {"models": modelSelectorInstance.get_available_models()}
    

@router.post("/initialize_code_query")
async def code_query(
    request: CodeQueryRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user)
):

    mongo_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='project_repositories'
        )

    try:
        # Generate random prefix for session_id
        random_prefix = generate_random_prefix()
        
        # Sort and combine build_ids
        sorted_build_ids = sorted(request.build_ids)  # Sort to ensure consistent ordering
        build_ids_string = "-".join(map(str, sorted_build_ids))
        
        # Combine prefix with build_ids to create final session_id
        session_id = f"{random_prefix}-{build_ids_string}"
        
        print("PASSING TO GET REDIS KNOWLEDGE", sorted_build_ids)
        
        # Check if knowledge exists in Redis for these build IDs
        existing_knowledge = getRedisKnowledge(id=sorted_build_ids, verbose=True)
        
        if existing_knowledge:
            print(settings.WEBSOCKET_URI)
            # If knowledge exists, initialize WebSocket and return session info
            ws_client = WebSocketClient(session_id, settings.WEBSOCKET_URI)
            reporter = Reporter(ws_client)
            reporter.initialize()
            
            project_data = await mongo_handler.get_one(
                filter={'project_id': int(request.project_id)},
                db=mongo_handler.db
            )
            # Iterate through repositories and their branches to find matching build IDs
            for repo in project_data.get('repositories', []):
                for branch in repo.get('branches', []):
                    branch_name = branch['name']
                    build_info = branch.get('builds', {})
                    if build_info.get('build_id') in request.build_ids:
                        
                        # Track incomplete builds
                        if build_info.get('kg_creation_status') == 2:
                            build_path = build_info.get('path')

                            os.chdir(build_path)
                            # First check build_path in builds object
                            build_path = build_info.get('path')
                            os.system(f'git switch {branch_name}')
                            os.chdir(os.path.dirname(os.path.dirname(build_path)))
            try:
                # Get session tracker
                tracker = get_session_tracker()
                
                # Initialize session tracking
                session_result = await tracker.initialize_session(
                    task_id=session_id,  # This is your task_id
                    tenant_id=request.tenant_id,
                    user_id=request.user_id,
                    project_id=request.project_id,
                    service_type="code-query",
                    session_data={
                        "session_name": request.session_name,
                        "description": request.description,
                    }
                )
                
                if session_result["success"]:
                    print(f"Session tracking initialized for task_id: {session_id}")
                else:
                    print(f"Failed to initialize session tracking: {session_result['error']}")
                        
            except Exception as session_error:
                print(f"Session tracking initialization error: {session_error}") 
            return {
                "status": "success",
                "session_id": session_id,
                "project_id": request.project_id,
            }

        else:
            raise HTTPException(
                status_code=500,
                detail=f"Redis knowledge not found"
            )
            
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error initializing code query: {str(e)}"
        )



@router.get("/session-status/{session_id}")
async def get_session_status(session_id: str, request: Request):
    
    # Split the session_id by hyphen
    session_parts = session_id.split('-')
    
    # Dynamically extract build IDs, excluding the first part
    build_ids = session_parts[1:] if len(session_parts) > 1 else []
        
    # Filter out empty build IDs and convert to a list
    build_ids_list = [bid for bid in build_ids if bid]
    
    # Check Redis knowledge with build_ids list
    redis_knowledge = getRedisKnowledge(id=build_ids_list)
    if redis_knowledge:
        
        total_files = 0
        files_processed = 0

        total_files += len(redis_knowledge.get('source_files', []))
        files_processed += len(redis_knowledge.get('source_files', []))

        progress = (files_processed / total_files * 100) if total_files > 0 else 0
    
        return {
            "session_id": session_id,
            "build_ids": build_ids_list,
            "files_processed": files_processed,
            "total_files": total_files,
            "is_ready": True,
        }
        
    # Log incoming request
    logger.info(f"SESSION STATUS REQUEST - Session ID: {session_id}")
    
    # Get request details
    client_host = request.client.host
    forwarded_for = request.headers.get("X-Forwarded-For")
    user_agent = request.headers.get("User-Agent")
    request_id = request.headers.get("X-Request-ID")

    data = {
        "session_id": session_id,
        "client_ip": client_host,
        "forwarded_for": forwarded_for,
        "user_agent": user_agent,
        "request_id": request_id,
        "timestamp": str(datetime.now()),
        "available_sessions": list(session_manager.sessions.keys())
    }

    logger.info(f"Request Data: {data}")
    
    async with session_manager._lock:
        session = session_manager.sessions.get(session_id)
        if not session:
            error_response = {
                "detail": "Session not found",
                "debug_info": data,
                "request_header": dict(request.headers)
            }
            logger.error(f"Session not found: {error_response}")
            raise HTTPException(status_code=200, detail=error_response)

    # Check status of all knowledge instances for this session
    is_ready = await session.is_ready()
    total_files = 0
    files_processed = 0

    for path_info in session.codebase_paths:
        instance = session_manager.get_instance_by_session(session_id)
        if instance:
            total_files += instance.total_files
            files_processed += instance.files_processed

    progress = (files_processed / total_files * 100) if total_files > 0 else 0

    response_data = {
        "session_id": session_id,
        "is_ready": is_ready,
        "progress": progress,
        "files_processed": files_processed,
        "total_files": total_files,
        "created_at": session.created_at.isoformat(),
        "last_accessed": session.last_accessed.isoformat(),
        "debug_info": data,
        "request_header": dict(request.headers)
    }
    
    # logger.info(f"SESSION STATUS RESPONSE: {response_data}")
    return response_data

@router.get("/knowledge-status-for-all")
async def get_knowledge_status(request: Request):
    logger.info("KNOWLEDGE STATUS REQUEST")
    
    try:
        # Get request details
        client_host = request.client.host if request.client else None
        forwarded_for = request.headers.get("X-Forwarded-For")
        user_agent = request.headers.get("User-Agent")
        request_id = request.headers.get("X-Request-ID")

        request_info = {
            "client_ip": client_host,
            "forwarded_for": forwarded_for,
            "user_agent": user_agent,
            "request_id": request_id,
            "timestamp": str(datetime.now()),
            "available_sessions": list(session_manager.sessions.keys())
        }
        
        logger.info(f"Request Info: {request_info}")
        
        status = {}
        for session_id, instance in session_manager._instances.items():
            session_id = instance.session_id or "no_session"
            if session_id not in status:
                status[session_id] = {
                    "request_info": request_info,
                    "instances": {}
                }

            status[session_id]["instances"][session_id] = {
                "initialized": instance.is_initialized,
                "files_processed": instance.files_processed,
                "total_files": instance.total_files,
                "ready_for_queries": instance.reporter.is_ready()
            }
        
        if not status:
            response_data = {
                "message": "No active sessions found",
                "status": "empty",
                "instances": {}
            }
            logger.info(f"KNOWLEDGE STATUS RESPONSE (Empty): {response_data}")
            return response_data
            
        logger.info(f"KNOWLEDGE STATUS RESPONSE: {status}")
        return status

    except Exception as e:
        error_response = {
            "request_info": request_info,
            "error": str(e),
            "status": "error",
            "instances": {}
        }
        # logger.error(f"Error in knowledge status: {error_response}")
        return error_response


@router.delete("/delete-session/{session_id}")
async def delete_session(request: Request, session_id: str):
    try:
        # Get request details
        client_host = request.client.host
        forwarded_for = request.headers.get("X-Forwarded-For")
        user_agent = request.headers.get("User-Agent")
        request_id = request.headers.get("X-Request-ID")

        data = {
            "session_id": session_id,
            "client_ip": client_host,
            "forwarded_for": forwarded_for,
            "user_agent": user_agent,
            "request_id": request_id,
            "timestamp": str(datetime.now()),
            "available_sessions": list(session_manager.sessions.keys())
        }
        session_tracker = get_session_tracker()
        await session_tracker.end_session(session_id, "completed")
        # Check if session exists
        session = session_manager.sessions.get(session_id)
        if not session:
            raise HTTPException(status_code=200, detail={
                "detail": "Session not found",
                "debug_info": data,
                "request_header": dict(request.headers)
            })

        # Get the specific instance for this session
        instance = session_manager.get_instance_by_session(session_id)
        
        # Release knowledge if instance exists
        if instance:
            try:
                if instance.knowledge_helper and instance.knowledge_helper.knowledge:
                    # Stop and cleanup knowledge processing
                    Knowledge.releaseKnowledge(session_id)
                    logger.info(f"Knowledge released for session: {session_id}")
            except Exception as instance_error:
                logger.error(f"Error releasing knowledge for session {session_id}: {str(instance_error)}")

        # Remove the specific instance from knowledge_manager
        await session_manager.remove_instance_by_session(session_id)
        logger.info(f"Instance removed for session: {session_id}")

        # Remove the specific session from session_manager
        del session_manager.sessions[session_id]
        logger.info(f"Session removed: {session_id}")

        return {
            "status": "success",
            "message": f"Session {session_id} has been deleted",
            "session_id": session_id,
            "deleted": True
        }

    except HTTPException as he:
        logger.error(f"HTTP Exception in delete_session: {str(he)}")
        raise he
    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"Error in delete_session: {error_details}")
        raise HTTPException(
            status_code=500,
            detail=f"Error deleting session: {str(e)}"
        )

@router.delete("/delete-kg-repository")
async def delete_repository(build_id: str, project_id: int, repo_service: str, current_user=Depends(get_current_user)):
    try:
        # Get MongoDB handler
        mongo_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='project_repositories'
        )

        # Fetch the project data
        project_data = await mongo_handler.get_one(
            filter={"project_id": project_id},
            db=mongo_handler.db
        )
        if not project_data:
            raise HTTPException(status_code=404, detail="Project not found")
        
        repo_to_delete = None
        if repo_service == "localFiles":
            for repo in project_data["repositories"]:
                if repo['service'] == "localFiles":
                    if repo['builds']['build_id'] == build_id:
                        repo_to_delete = repo
            
            if not repo_to_delete:
                raise HTTPException(status_code=404, detail="Build ID not found in project")
            
            mongo_handler.db[mongo_handler.collection].update_one(
                {"project_id": project_id},
                {
                    "$pull": {
                        "repositories": {
                            "repo_id": repo_to_delete["repo_id"]
                        }
                    }
                }
            )
        else:
            # Locate the repository, branch, and build with the given build_id
            for repo in project_data["repositories"]:
                if repo['service'] != "localFiles":
                    for branch in repo["branches"]:
                        if branch.get("builds", {}).get("build_id") == build_id:
                            repo_to_delete = {
                                "repo": repo,
                                "branch": branch,
                                "build": branch["builds"]
                            }
                            branches_length = len(repo['branches'])
                            break

            if not repo_to_delete:
                raise HTTPException(status_code=404, detail="Build ID not found in project")
            
            

            # Delete the folder for the build
            # build_path = repo_to_delete["build"]["path"]
            # if os.path.exists(build_path):
            #     shutil.rmtree(build_path)
            #     logging.info(f"Deleted build folder: {build_path}")
            # else:
            #     logging.warning(f"Build path {build_path} does not exist. Skipping folder deletion.")

            # Check and delete the project folder if empty
            # project_folder = os.path.dirname(build_path)
            # if os.path.exists(project_folder) and not os.listdir(project_folder):
            #     os.rmdir(project_folder)
            #     logging.info(f"Deleted empty project folder: {project_folder}")

            # # Check and delete the owner folder if empty
            # owner_folder = os.path.dirname(project_folder)
            # if os.path.exists(owner_folder) and not os.listdir(owner_folder):
            #     os.rmdir(owner_folder)
            #     logging.info(f"Deleted empty owner folder: {owner_folder}")

            # Remove the branch containing the build
            mongo_handler.db[mongo_handler.collection].update_one(
                {"project_id": project_id},
                {
                    "$pull": {
                        "repositories.$[repo].branches": {"builds.build_id": build_id}
                    }
                },
                array_filters=[{"repo.git_url": repo_to_delete["repo"]["git_url"]}]
            )


            if branches_length > 1: #remove the selected_repo_field
                mongo_handler.db[mongo_handler.collection].update_one(
                    {"project_id": project_id},
                    {
                        "$unset": {
                            "repositories.$[repo].selected_branch": 1  # Completely removes the field
                        }
                    },
                    array_filters=[{"repo.git_url": repo_to_delete["repo"]["git_url"]}]
                )

            # Remove the repository if the `branches` array is empty
            mongo_handler.db[mongo_handler.collection].update_one(
                {"project_id": project_id},
                {
                    "$pull": {
                        "repositories": {"branches": {"$size": 0}}
                    }
                }
            )

        # Verify if the entire project should be deleted
        project_data = await mongo_handler.get_one(
            filter={"project_id": project_id},
            db=mongo_handler.db
        )
        if not project_data["repositories"]:  # If no repositories remain
            mongo_handler.db[mongo_handler.collection].delete_one(
                {"project_id": project_id}
            )
            logging.info(f"Deleted project with ID {project_id} as no repositories remain.")

        return {"message": f"Repository and build with ID {build_id} deleted successfully"}

    except Exception as e:
        logging.error(f"Failed to delete repository: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting repository: {str(e)}")


@router.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "active_sessions": len(session_manager.sessions),
        "knowledge_instances": len(session_manager._instances)
    }