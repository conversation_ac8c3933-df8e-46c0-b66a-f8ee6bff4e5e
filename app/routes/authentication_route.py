from fastapi import APIRouter, Depends, HTTPException, status, Request
import boto3,time
from botocore.exceptions import ClientError, ParamValidationError
from app.core.Settings import settings
from app.connection.establish_db_connection import get_node_db
from fastapi import Query, Body, BackgroundTasks
from app.models.auth_model import CognitoUser, SignUpUser
from app.routes.users_route import format_response_user
from app.utils.aws.cognito_main import TenantService
from app.utils.aws.cognito_userpool import Cognito<PERSON>ser<PERSON>ool<PERSON>reator
from app.utils.hash import decrypt_tenant_id, encrypt_tenant_id
from app.models.organization_models import Organization, User
from app.connection.establish_db_connection import get_mongo_db
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME
from app.connection.tenant_middleware import get_opentopublic
import logging
from datetime import datetime, timedelta
import random
import string
import requests
from fastapi.responses import RedirectResponse, JSONResponse
from typing import Optional, Dict, Any
import jwt
from pydantic import BaseModel
from app.utils.aws.cognito_user_manager import CognitoUserManager

DB_NAME = KAVIA_ROOT_DB_NAME

db = get_node_db()
tenant_service = TenantService()
default_tenant_id = settings.KAVIA_SUPER_TENANT_ID

# Set up logging
logger = logging.getLogger(__name__)

# Add this configuration setting near the top of the file after the imports
ENABLE_PASSWORDLESS_GOOGLE_AUTH = getattr(settings, 'ENABLE_PASSWORDLESS_GOOGLE_AUTH', True)  # Default to True

def verify_google_config():
    required_settings = [
        'GOOGLE_CLIENT_ID',
        'GOOGLE_CLIENT_SECRET',
        'GOOGLE_REDIRECT_URI'
    ]
    
    missing = [setting for setting in required_settings 
              if not getattr(settings, setting, None)]
    
    if missing:
        logger.error(f"Missing required Google OAuth settings: {', '.join(missing)}")
        raise ValueError(f"Missing required Google OAuth settings: {', '.join(missing)}")

# Call this when your application starts
verify_google_config()

def decode_tenant_id(tenant_id: str):
    try:
        if '%' in tenant_id:
            from urllib.parse import unquote
            tenant_id = unquote(tenant_id)
        return tenant_id
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to decode tenant ID: {str(e)}")

async def get_tenant_credentials(tenant_id: str) -> dict:
    """Helper function to get the appropriate credentials based on tenant_id"""
    if  tenant_id == default_tenant_id:
        return {
            'user_pool_id': settings.AWS_COGNITO_USER_POOL_ID,
            'client_id': settings.AWS_COGNITO_APP_CLIENT_ID
        }
    else:
        print("External Tenant cred", tenant_id)
        return await tenant_service.get_tenant_cred(tenant_id)

_SHOW_NAME = "auth"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}},
)

async def add_user_node_db(username, tenant_id):
    try:
        db = get_node_db(tenant_id)
        creds = await get_tenant_credentials(tenant_id)
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        user_details = client.admin_get_user(
            UserPoolId=creds['user_pool_id'],
            Username=username
        )
        
        # Add user to node database
        await db.upsert_user_db(user_details.get('Username'), format_response_user(user_details))
        
        logger.info(f"Successfully added user {username} to node database for tenant {tenant_id}")
        return True
    except Exception as e:
        logger.error(f"Error adding user to node database: {str(e)}")
        return False

@router.get("/get_organization_name")
async def get_organization_name(tenant_id: str):
    try:
        tenant_id = decrypt_tenant_id(decode_tenant_id(tenant_id))
        print("Decrypted Tenant ID", tenant_id)
        if tenant_id == settings.KAVIA_SUPER_TENANT_ID:
            return {
                "name": "Super Admin",
                "id": settings.KAVIA_SUPER_TENANT_ID,
                "created_at": None
            }
        organization = Organization(**(await Organization.get(tenant_id)))
        return {
            "name": organization.name,
            "id": organization.id,
            "created_at": organization.created_at
        }
    except Exception as e:
        logger.error(f"Error getting organization name: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/get_organization_name_by_id")
async def get_organization_name_by_id(tenant_id: str):
    if tenant_id == settings.KAVIA_SUPER_TENANT_ID:
        return {
            "name": "Super Admin",
            "id": settings.KAVIA_SUPER_TENANT_ID,
            "created_at": None,
            "login_id": encrypt_tenant_id(settings.KAVIA_SUPER_TENANT_ID)
        }
    organization = Organization(**(await Organization.get(tenant_id)))
    return {
        "name": organization.name,
        "id": organization.id,
        "created_at": organization.created_at,
        "login_id": encrypt_tenant_id(organization.id)
    }

@router.post("/login", summary="Login for existing users")
async def login(user: CognitoUser):
    try:
        tenant_id = decrypt_tenant_id(decode_tenant_id(user.organization_id))
        creds = await get_tenant_credentials(tenant_id)
        
        # First, update the app client to enable USER_PASSWORD_AUTH
        cognito_pool = CognitoUserPoolCreator()
        cognito_pool.update_app_client_auth_flows(
            user_pool_id=creds['user_pool_id'],
            client_id=creds['client_id']
        )
        
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        try:
            # Now attempt login
            response = client.initiate_auth(
                ClientId=creds['client_id'],
                AuthFlow='USER_PASSWORD_AUTH',
                AuthParameters={
                    'USERNAME': user.email,
                    'PASSWORD': user.password
                }
            )
        except client.exceptions.NotAuthorizedException:
            raise HTTPException(status_code=401, detail="Invalid username or password")
        except client.exceptions.UserNotFoundException:
            raise HTTPException(status_code=401, detail="User not found")

        # Check token expiry times
        auth_result = response['AuthenticationResult']
        access_token_exp = auth_result.get('ExpiresIn', 3600)
        
        # If token expiry is less than desired (e.g., 24 hours = 86400 seconds)
        if access_token_exp < 86400:  # 24 hours in seconds
            # Update token validity
            cognito_pool.update_app_client_token_validity(
                user_pool_id=creds['user_pool_id'],
                client_id=creds['client_id'],
                access_token_validity=24,  # 24 hours
                id_token_validity=24,      # 24 hours
                refresh_token_validity=30   # 30 days
            )
            
            try:
                # Perform login again to get new tokens with updated expiry
                response = client.initiate_auth(
                    ClientId=creds['client_id'],
                    AuthFlow='USER_PASSWORD_AUTH',
                    AuthParameters={
                        'USERNAME': user.email,
                        'PASSWORD': user.password
                    }
                )
            except client.exceptions.NotAuthorizedException:
                raise HTTPException(status_code=401, detail="Invalid username or password")
            except client.exceptions.UserNotFoundException:
                raise HTTPException(status_code=401, detail="User not found")

        # Format response
        id_token = response['AuthenticationResult'].pop('IdToken')
        refresh_token = response['AuthenticationResult'].pop('RefreshToken')
        
        # Get user details from Cognito to extract custom attributes
        user_details = client.admin_get_user(
            UserPoolId=creds['user_pool_id'],
            Username=user.email
        )
        
        # Extract custom attributes from user details
        is_admin = False
        is_free_user = False
        _tenant_id = tenant_id
        tenant_id = ''
        
        for attr in user_details.get('UserAttributes', []):
            if attr['Name'] == 'custom:is_admin' and attr['Value'].lower() == 'true':
                is_admin = True
            elif attr['Name'] == 'custom:free_user' and attr['Value'].lower() == 'true':
                is_free_user = True
            elif attr['Name'] == 'custom:tenant_id':
                tenant_id = attr['Value']
        if tenant_id == '':
            tenant_id = _tenant_id
        response_to_return = {
            "message": "Login successful",
            "id_token": id_token,
            "refresh_token": refresh_token,
            "tenant_id": settings.KAVIA_B2C_CLIENT_ID if tenant_id.startswith("default") else tenant_id,
            "is_admin": is_admin,
            "is_free_user": is_free_user,
            "is_super_admin": tenant_id == settings.KAVIA_SUPER_TENANT_ID,
            **response['AuthenticationResult']
        }

        
        return response_to_return
    
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Login failed: {str(e)}")

@router.post("/signup", summary="Sign up a new user")
async def signup(user: SignUpUser):
    try:
        random_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
        org_id = settings.KAVIA_B2C_CLIENT_ID
        tenant_id = decrypt_tenant_id(decode_tenant_id(user.organization_id))
        
        if tenant_id != settings.KAVIA_B2C_CLIENT_ID:
            raise HTTPException(status_code=400, detail="Signup is not allowed for this organization")
        logger.info(f"Signing up user {user.email} for organization {tenant_id}")
        
        creds = await get_tenant_credentials(tenant_id)
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)

        user_manager = CognitoUserManager(
            user_pool_id=creds['user_pool_id'],
            client_id=creds['client_id']
        )

        # Update Cognito custom attributes
        user_manager.add_free_tier_to_pool()

        # Sign up the user in Cognito
        response = client.sign_up(
            ClientId=creds['client_id'],
            Username=user.email,
            Password=user.password,
            UserAttributes=[
                {'Name': 'email', 'Value': user.email},
                {'Name': 'custom:Name', 'Value': user.name},
                {'Name': 'custom:Designation', 'Value': user.designation},
                {'Name': 'custom:Department', 'Value': user.department},
                {'Name': 'custom:tenant_id', 'Value': org_id},
                {'Name': 'custom:is_admin', 'Value': 'true'},
                {'Name': 'custom:free_user', 'Value': 'true'}
            ],
            ValidationData=[
                {'Name': 'email', 'Value': user.email},
            ],
        )
        
        # Create user in MongoDB (will be activated during confirm_signup)
        user_id = response['UserSub']
        designation = user.designation
        opentopublic = get_opentopublic()
        
        # Create user data object for B2C tenant
        user_data = {
            "_id": user_id,
            "email": user.email,
            "name": user.name,
            "contact_number": "",  # This can be updated later if needed
            "department": user.department,
            "organization_id": tenant_id,
            "group_ids": [],
            "is_admin": False,  # Default to non-admin
            "designation": designation,
            "status": "active" if opentopublic else "inactive",  # Will be activated during confirm_signup
            "free_user": True
        }
        
        
        active_subscription = get_mongo_db(db_name=DB_NAME, collection_name="active_subscriptions")
        subscription_data = {
                "tenant_id": "b2c",
                "price_id": "price_1RWBNuCI2zbViAE2N6TkeNVB",
                "credits": 50000,
                "created_at": datetime.utcnow().isoformat()
            }

            # Insert or update the subscription data
        await active_subscription.update_one_data(
            {"user_id": user_id},
            {"$set" :subscription_data},
            upsert=True
        )
        llm_cost_collection = get_mongo_db(db_name=DB_NAME, collection_name="llm_costs")
        llm_cost_data = {
               "user_id": user_id,
                "type": "llm_interaction",
                "user_cost": "$0",
                "projects": [], 
                "cost": "$0",
                "plans_history": [],  
                "current_plan": "price_1RWBNuCI2zbViAE2N6TkeNVB",  
                "_task_tracking": {}, 
            }
        await llm_cost_collection.update_one_data(
            {"organization_id": "b2c"},
            {"$push": {"users": llm_cost_data}},
            upsert=True

        )
        
        # Save user to database for B2C tenant
        try:
            await User.create(user_data)
        except Exception as e:
            if "duplicate key error" in str(e):
                logger.warning(f"User {user_id} already exists in B2C tenant, skipping creation")
            else:
                raise e
        
        logger.info(f"User {user.email} created successfully with ID {user_id}")
        
        return {
            "message": "User created successfully. Verification code sent.", 
            "user_sub": user_id,
            "tenant_id": tenant_id,  # Include tenant_id in the response
            "organization_id": org_id  # Include the organization ID
        }
    except ClientError as e:
        # Handle all boto3 client errors in one place
        if e.__class__.__name__ == 'UsernameExistsException':
            logger.warning(f"Signup failed: Username {user.email} already exists")
            raise HTTPException(status_code=400, detail="Username (email) already exists")
        elif e.__class__.__name__ == 'InvalidPasswordException':
            logger.warning(f"Signup failed: Invalid password for user {user.email}")
            raise HTTPException(status_code=400, detail="Invalid password. Password must meet Cognito requirements.")
        elif e.__class__.__name__ == 'ParamValidationError':
            logger.error(f"Signup failed: Parameter validation error for user {user.email}: {e}")
            raise HTTPException(status_code=400, detail=f"Parameter validation error: {e}")
        else:
            logger.error(f"Signup failed: Cognito API error for user {user.email}: {e}")
            raise HTTPException(status_code=500, detail=f"Cognito API error: {e}")
    except Exception as e:  # Catch-all for unexpected errors
        logger.error(f"Signup failed: Internal server error for user {user.email}: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

@router.post("/change_password")
async def change_password(username: str, new_password: str, tenant_id: str = Query(..., description="Tenant ID")):
    client = None
    try:
        creds = await get_tenant_credentials(tenant_id)
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        response = client.admin_set_user_password(
            UserPoolId=creds['user_pool_id'],
            Username=username,
            Password=new_password,
            Permanent=True
        )

        print("Password changed successfully.")
    except client.exceptions.UserNotFoundException:
        print("User not found.")
    except client.exceptions.InvalidPasswordException:
        print("Invalid password, please check password policy.")
    except Exception as e:
        print("An error occurred:", e)

@router.post("/confirm_signup", summary="Confirm user signup with verification code")
async def confirm_signup(
    username: str = Query(..., description="The user's email address (username)"),
    confirmation_code: str = Query(..., description="The confirmation code sent to the user's email"),
    tenant_id: str = Query(None, description="Tenant ID (optional - will be retrieved from user attributes if not provided)"),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    client = None
    try:
        # Handle tenant_id properly
        if tenant_id:
            try:
                tenant_id = decrypt_tenant_id(decode_tenant_id(tenant_id))
            except Exception as e:
                logger.warning(f"Error decoding tenant_id: {e}, using as-is")
        
        # If tenant_id is not provided or decoding failed, use default tenant
        if not tenant_id:
            tenant_id = default_tenant_id
            logger.info(f"No tenant_id provided, using default tenant: {tenant_id}")
        
        logger.info(f"Confirming signup for user {username} in organization {tenant_id}")
        
        creds = await get_tenant_credentials(tenant_id)
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        # Confirm signup in Cognito
        client.confirm_sign_up(
            ClientId=creds['client_id'],
            Username=username,
            ConfirmationCode=confirmation_code,
        )
        
        # Get user details from Cognito
        user_details = client.admin_get_user(
            UserPoolId=creds['user_pool_id'],
            Username=username
        )
        user_id = user_details.get('Username')
        
        # Get the organization ID from user attributes
        org_id = None
        for attr in user_details.get('UserAttributes', []):
            if attr['Name'] == 'custom:tenant_id':
                org_id = attr['Value']
                logger.info(f"Retrieved organization_id from user attributes: {org_id}")
                break
        
        # IMPORTANT: Initialize Neo4j database for the organization if it doesn't exist
        if org_id:
            try:
                # Import here to avoid circular imports
                from app.classes.NodeDB import NodeDB
                # Use the already imported settings from the module level
                
                # Prepend environment prefix to database name if needed
                env_prefix = getattr(settings, 'ENV_PREFIX', '')
                db_name = f"{env_prefix}{org_id}" if env_prefix else org_id
                # Replace hyphens with underscores for Neo4j compatibility
                db_name = db_name.replace('-', '_')
                logger.info(f"Initializing Neo4j database for organization: {db_name}")
                
                # Initialize NodeDB which will create the database if it doesn't exist
                node_db = NodeDB(
                    uri=settings.NEO4J_URI,
                    user=settings.NEO4J_USER,
                    password=settings.NEO4J_PASSWORD,
                    database=db_name
                )
                
                logger.info(f"Successfully initialized Neo4j database for organization: {db_name}")
            except Exception as e:
                logger.error(f"Error initializing Neo4j database for organization {org_id}: {e}")
                # Continue execution even if database initialization fails
        
        # Add user to node database for the current tenant
        background_tasks.add_task(add_user_node_db, user_id, tenant_id)
        
        # IMPORTANT: Use the correct collection name from organization_models.py
        # User.get_collection() returns the correct collection
        users_collection = User.get_collection()
        
        # Update user status in B2C tenant
        try:
            # Update by email
            '''
            Will be replace this operation by admin
            '''
            # result = users_collection.update_one(
            #     {"email": username, "organization_id": tenant_id},
            #     {"$set": {"status": "active", "updated_at": datetime.utcnow()}}
            # )
            
            # if result.modified_count > 0:
            #     logger.info(f"User {username} status updated to active in B2C tenant")
            # else:
            #     # Try by user_id
            #     result = users_collection.update_one(
            #         {"_id": user_id, "organization_id": tenant_id},
            #         {"$set": {"status": "active", "updated_at": datetime.utcnow()}}
            #     )
            #     if result.modified_count > 0:
            #         logger.info(f"User {user_id} status updated to active in B2C tenant (found by ID)")
            #     else:
            #         logger.warning(f"Could not find user in B2C tenant to update status")
        except Exception as e:
            logger.error(f"Error updating user status in B2C tenant: {e}")
        
        # Now handle the user's personal organization if we found one in attributes
        if org_id:
            try:
                logger.info(f"Activating user in their personal organization: {org_id}")
                
                # Add user to node database for their organization
                background_tasks.add_task(add_user_node_db, user_id, org_id)
                
                # DIRECT DB UPDATE: Update all users with this email in the organization
                # First try by email
                result = users_collection.update_many(
                    {"email": username, "organization_id": org_id},
                    {"$set": {"updated_at": datetime.utcnow()}}
                )
                
                if result.modified_count > 0:
                    logger.info(f"Updated {result.modified_count} users with email {username} in organization {org_id}")
                else:
                    # Try by cognito_id
                    result = users_collection.update_many(
                        {"cognito_id": user_id, "organization_id": org_id},
                        {"$set": {"updated_at": datetime.utcnow()}}
                    )
                    
                    if result.modified_count > 0:
                        logger.info(f"Updated {result.modified_count} users with cognito_id {user_id} in organization {org_id}")
                    else:
                        # Try with combined ID format
                        org_user_id = f"{user_id}-{org_id}"
                        result = users_collection.update_one(
                            {"_id": org_user_id},
                            {"$set": {"updated_at": datetime.utcnow()}}
                        )
                        
                        if result.modified_count > 0:
                            logger.info(f"Updated user with ID {org_user_id} in organization {org_id}")
                        else:
                            logger.warning(f"Could not find any users to update in organization {org_id}")
                
                # ADDITIONAL DIRECT UPDATE: Update all users with this user_id as admin_id in any organization
                orgs_collection = Organization.get_collection()
                orgs = list(orgs_collection.find({"admin_id": user_id}))
                
                for org in orgs:
                    org_id = org.get("_id")
                    logger.info(f"Found organization {org_id} with user {user_id} as admin, updating users")
                    
                    # Update all users in this organization
                    result = users_collection.update_many(
                        {"organization_id": org_id},
                        {"$set": {"updated_at": datetime.utcnow()}}
                    )
                    
                    if result.modified_count > 0:
                        logger.info(f"Updated {result.modified_count} users in organization {org_id}")
                    
            except Exception as e:
                logger.error(f"Error updating user status in organization {org_id}: {e}")
        
        logger.info(f"Signup confirmed successfully for user {username}")
        return {"message": "Signup confirmed successfully. User is now active."}
    
    except Exception as e:
        # Generic exception handler to catch all exceptions
        logger.error(f"Confirm signup failed: Error for user {username}: {e}")
        
        # Check specific exception types
        if client is not None:
            if hasattr(client, 'exceptions'):
                if isinstance(e, client.exceptions.CodeMismatchException):
                    logger.warning(f"Confirm signup failed: Invalid confirmation code for user {username}")
                    raise HTTPException(status_code=400, detail="Invalid confirmation code.")
                elif isinstance(e, client.exceptions.ExpiredCodeException):
                    logger.warning(f"Confirm signup failed: Expired confirmation code for user {username}")
                    raise HTTPException(status_code=400, detail="Confirmation code has expired.")
                elif isinstance(e, ClientError):
                    logger.error(f"Confirm signup failed: Cognito API error for user {username}: {e}")
                    raise HTTPException(status_code=400, detail=str(e))
        
        # If we get here, it's a general error
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post("/resend_confirmation_code")
async def resend_confirmation_code(
    username: str = Query(..., description="The user's email address (username)"),
    tenant_id: str = Query(None, description="Tenant ID (optional - will use default if not provided)")
):
    client = None
    try:
        # Handle tenant_id properly
        if tenant_id:
            try:
                tenant_id = decrypt_tenant_id(decode_tenant_id(tenant_id))
            except Exception as e:
                logger.warning(f"Error decoding tenant_id: {e}, using as-is")
        
        # If tenant_id is not provided or decoding failed, use default tenant
        if not tenant_id:
            tenant_id = default_tenant_id
            logger.info(f"No tenant_id provided, using default tenant: {tenant_id}")
            
        logger.info(f"Resending confirmation code for user {username} in organization {tenant_id}")
        
        creds = await get_tenant_credentials(tenant_id)
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        # First, check if the user exists and get their status
        try:
            user_details = client.admin_get_user(
                UserPoolId=creds['user_pool_id'],
                Username=username
            )
            
            user_status = user_details.get('UserStatus', '')
            logger.info(f"User {username} found with status: {user_status}")
            
            # If user is already confirmed, we need to handle differently
            if user_status == 'CONFIRMED':
                logger.info(f"User {username} is already confirmed. Sending password reset instead.")
                # Use forgot password flow instead
                client.forgot_password(
                    ClientId=creds['client_id'],
                    Username=username
                )
                return {"message": "User is already confirmed. Password reset code sent instead."}
                
        except client.exceptions.UserNotFoundException:
            logger.warning(f"User {username} not found in Cognito. Will attempt to resend code anyway.")
            # Continue with resend_confirmation_code even if admin_get_user fails
            # This is because the user might exist but the admin_get_user might not have permission
        
        # Try the standard resend confirmation code
        try:
            client.resend_confirmation_code(
                ClientId=creds['client_id'],
                Username=username,
            )
            logger.info(f"Confirmation code resent successfully for user {username}")
            return {"message": "Confirmation code resent successfully."}
        except client.exceptions.UserNotFoundException:
            # If user not found with resend_confirmation_code, try to sign them up again
            logger.warning(f"User {username} not found with resend_confirmation_code. Checking MongoDB.")
            
            # Check if user exists in MongoDB
            mongo_handler = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME, collection_name="tenant_users")
            db_user = await mongo_handler.get_one(
                filter={"email": username, "organization_id": tenant_id}, 
                db=mongo_handler.db.users
            )
            
            if not db_user:
                logger.error(f"User {username} not found in MongoDB either.")
                raise HTTPException(status_code=404, detail="User not found in our system.")
            
            # User exists in MongoDB but not in Cognito or is in an invalid state
            # We should return a more helpful message
            logger.error(f"User {username} exists in MongoDB but cannot receive confirmation code.")
            raise HTTPException(
                status_code=400, 
                detail="Unable to resend confirmation code. Please contact support or try the forgot password option."
            )
            
    except client.exceptions.LimitExceededException:
        logger.warning(f"Resend confirmation code failed: Rate limit exceeded for user {username}")
        raise HTTPException(status_code=429, detail="Too many requests. Please try again later.")
    except ClientError as e:
        if 'UserNotFoundException' in str(e):
            logger.error(f"User {username} not found in Cognito")
            raise HTTPException(status_code=404, detail="User not found.")
        logger.error(f"Resend confirmation code failed: Cognito API error for user {username}: {e}")
        raise HTTPException(status_code=400, detail=e.response['Error']['Message'])  # More informative error message
    except Exception as e:
        logger.error(f"Resend confirmation code failed: Internal server error for user {username}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/forgot_password")
async def forgot_password(
    email: str = Query(..., description="The user's email address"),
    tenant_id: str = Query(..., description="Tenant ID")
):
    client = None
    try:
        tenant_id = decrypt_tenant_id(decode_tenant_id(tenant_id))
        creds = await get_tenant_credentials(tenant_id)
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        # First check if user exists in this Cognito pool
        try:
            client.admin_get_user(
                UserPoolId=creds['user_pool_id'],
                Username=email
            )
        except client.exceptions.UserNotFoundException:
            logger.error(f"User {email} not found in Cognito user pool for tenant {tenant_id}")
            raise HTTPException(status_code=404, detail="User not found in this organization.")
        
        # If we get here, user exists, so proceed with forgot password
        client.forgot_password(
            ClientId=creds['client_id'],
            Username=email,
        )
        return {"message": "Password reset code sent to your email."}
    except (client.exceptions.LimitExceededException if client else Exception):
        raise HTTPException(status_code=429, detail="Too many requests. Please try again later.")
    except ClientError as e:  # Catch boto3 errors for better debugging
        if 'UserNotFoundException' in str(e):
            raise HTTPException(status_code=404, detail="User not found in this organization.")
        raise HTTPException(status_code=400, detail=e.response['Error']['Message'])
    except Exception as e:  # General exception catch
        raise HTTPException(status_code=500, detail=str(e))
    
    
@router.post("/confirm_forgot_password")
async def confirm_forgot_password(
    user: CognitoUser,
    confirmation_code: str = Query(..., description="Code from email"),
):
    client = None
    try:
        tenant_id = decrypt_tenant_id(decode_tenant_id(user.organization_id))
        creds = await get_tenant_credentials(tenant_id)
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        client.confirm_forgot_password(
            ClientId=creds['client_id'],
            Username=user.email,
            ConfirmationCode=confirmation_code,
            Password=user.password,
        )
        return {"message": "Password reset successful."}
    except client.exceptions.CodeMismatchException:
        raise HTTPException(status_code=400, detail="Invalid confirmation code.")
    except client.exceptions.ExpiredCodeException:
        raise HTTPException(status_code=400, detail="Confirmation code has expired.")
    except ClientError as e:
        raise HTTPException(status_code=400, detail=e.response['Error']['Message'])
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/refresh_token", summary="Refresh expired ID Token using refresh token")
async def refresh_token(
    refresh_token: str = Body(..., embed=True),
    tenant_id: str = Query(..., description="Tenant ID")
):
    client = None
    try:
        tenant_id = decrypt_tenant_id(decode_tenant_id(tenant_id))
        creds = await get_tenant_credentials(tenant_id)
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        response = client.initiate_auth(
            ClientId=creds['client_id'],
            AuthFlow="REFRESH_TOKEN_AUTH",
            AuthParameters={
                'REFRESH_TOKEN': refresh_token,
            }
        )
        id_token = response['AuthenticationResult']['IdToken']
        return {"message": "ID token refreshed successfully.", "id_token": id_token}
    except client.exceptions.NotAuthorizedException as e:
        raise HTTPException(status_code=401, detail="Invalid refresh token.")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/logout")
async def logout(
    user_id: str,
    refresh_token: str = Body(..., embed=True, default_factory=""),
    tenant_id: str = Query(..., description="Tenant ID")
):
    """Logs out a user by invalidating their tokens."""
    client = None
    try:
        tenant_id = decode_tenant_id(tenant_id)
        creds = await get_tenant_credentials(tenant_id)
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        client.admin_user_global_sign_out(
            UserPoolId=creds['user_pool_id'],
            Username=user_id,
        )

        client.revoke_token(
            Token=refresh_token,
            ClientId=creds['client_id'],
        )

        return {"message": "Logout successful. ID and refresh tokens invalidated."}

    except client.exceptions.InvalidParameterException as e:
        raise HTTPException(status_code=400, detail=f"Invalid parameter: {e}")

    except client.exceptions.NotAuthorizedException:
        raise HTTPException(status_code=401, detail="Unauthorized: ID token may be invalid or expired.")

    except ClientError as ce:
        raise HTTPException(status_code=500, detail=f"Cognito API error: {ce}")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

# Add Google OAuth routes
@router.get("/google")
async def login_google(request: Request):
    """Initiate Google OAuth flow"""
    try:
        # Decode tenant ID
        tenant_id = settings.KAVIA_B2C_CLIENT_ID
        tenant_id = decrypt_tenant_id(decode_tenant_id(tenant_id))
        
        # Get the action parameter (signin or signup)
        action = request.query_params.get("action", "signin")
        
        # Store the action and tenant_id in the state parameter to retrieve it in the callback
        state = f"{action}:{tenant_id}"
        
        google_auth_url = "https://accounts.google.com/o/oauth2/v2/auth"
        params = {
            "client_id": settings.GOOGLE_CLIENT_ID,
            "redirect_uri": settings.GOOGLE_REDIRECT_URI,
            "response_type": "code",
            "scope": "email profile openid",
            "access_type": "offline",
            "prompt": "consent",
            "state": state  # Pass the action and tenant_id as state
        }
        
        # Build the authorization URL with query parameters
        auth_url = f"{google_auth_url}?"
        auth_url += "&".join([f"{key}={value}" for key, value in params.items()])
        print("auth_url", auth_url)
        return RedirectResponse(auth_url)
    except Exception as e:
        logger.error(f"Error initiating Google OAuth flow: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error initiating Google OAuth: {str(e)}")

@router.get("/google/callback")
async def google_callback(
    request: Request,
    code: str, 
    state: Optional[str] = None,
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """Handle the Google OAuth callback"""
    try:
        # Parse state to get action and tenant_id
        state_parts = state.split(":") if state else ["signin", settings.KAVIA_B2C_CLIENT_ID]
        action = state_parts[0] if state_parts[0] in ["signin", "signup"] else "signin"
        # tenant_id = state_parts[1] if len(state_parts) > 1 else settings.KAVIA_B2C_CLIENT_ID
        tenant_id = settings.KAVIA_B2C_CLIENT_ID
        
        logger.info(f"Google callback received with action: {action}, tenant_id: {tenant_id}")
        
        # Exchange authorization code for tokens
        token_url = "https://oauth2.googleapis.com/token"
        token_data = {
            "code": code,
            "client_id": settings.GOOGLE_CLIENT_ID,
            "client_secret": settings.GOOGLE_CLIENT_SECRET,
            "redirect_uri": settings.GOOGLE_REDIRECT_URI,
            "grant_type": "authorization_code"
        }
        
        logger.info(f"Exchanging authorization code for tokens with Google")
        token_response = requests.post(token_url, data=token_data)
        token_response.raise_for_status()
        tokens = token_response.json()
        logger.info(f"Successfully obtained tokens from Google")
        
        # Get user info from Google
        google_user_info_url = "https://www.googleapis.com/oauth2/v3/userinfo"
        logger.info(f"Fetching user info from Google")
        user_info_response = requests.get(
            google_user_info_url,
            headers={"Authorization": f"Bearer {tokens['access_token']}"}
        )
        user_info_response.raise_for_status()
        user_info = user_info_response.json()
        logger.info(f"User information from Google: {user_info}")
        
        # Get Cognito credentials for the tenant
        logger.info(f"Getting Cognito credentials for tenant: {tenant_id}")
        creds = await get_tenant_credentials(tenant_id)
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        # Check if the user exists in Cognito
        user_exists = False
        try:
            # Try to find the user in Cognito
            logger.info(f"Checking if user {user_info['email']} exists in Cognito")
            user_response = client.admin_get_user(
                UserPoolId=creds['user_pool_id'],
                Username=user_info['email']
            )
            user_exists = True
            logger.info(f"User {user_info['email']} found in Cognito for tenant {tenant_id}")
        except client.exceptions.UserNotFoundException:
            user_exists = False
            logger.info(f"User {user_info['email']} not found in Cognito for tenant {tenant_id}")
        except Exception as e:
            logger.error(f"Error checking user existence: {str(e)}")
            # Default to not existing if there's an error
            user_exists = False
        
        # Handle signin vs signup based on action and user existence
        if action == "signin" and not user_exists:
            # User tried to sign in but doesn't exist
            logger.info(f"User {user_info['email']} tried to sign in but doesn't exist")
            return JSONResponse(
                content={
                    "authenticated": False,
                    "error": "user_not_found",
                    "message": "User does not exist. Please sign up first.",
                    "user_info": {
                        "email": user_info['email'],
                        "name": user_info.get('name', ''),
                        "picture": user_info.get('picture', '')
                    }
                }
            )

        elif action == "signup":
            if user_exists:
                # User already exists but trying to sign up
                # Instead of showing an error, automatically sign them in
                logger.info(f"User {user_info['email']} already exists but trying to sign up. Signing them in automatically.")
                
                # Use password-less authentication instead of setting a password
                try:
                    logger.info(f"Using password-less authentication for existing Google user {user_info['email']}")
                    
                    # Get user details without modifying password
                    user_details = client.admin_get_user(
                        UserPoolId=creds['user_pool_id'],
                        Username=user_info['email']
                    )
                    
                    # Extract user attributes for the response
                    is_admin = False
                    is_free_user = True
                    org_id = ""
                    cognito_user_id = user_details.get('Username')
                    
                    for attr in user_details.get('UserAttributes', []):
                        if attr['Name'] == 'custom:is_admin' and attr['Value'].lower() == 'true':
                            is_admin = True
                        elif attr['Name'] == 'custom:free_user' and attr['Value'].lower() == 'true':
                            is_free_user = True
                        elif attr['Name'] == 'custom:tenant_id':
                            org_id = attr['Value']
                    
                    # Enable user if disabled, but DON'T reset password
                    if user_details.get('Enabled', True) is False or user_details.get('UserStatus') == 'DISABLED':
                        logger.info(f"Enabling disabled user {user_info['email']} without password reset")
                        client.admin_enable_user(
                            UserPoolId=creds['user_pool_id'],
                            Username=user_info['email']
                        )
                    
                    # Create JWT tokens without password authentication
                    current_time = int(time.time())
                    
                    # Create JWT tokens that are compatible with your existing system
                    token_payload = {
                        "sub": cognito_user_id,
                        "email": user_info['email'],
                        "cognito:username": cognito_user_id,
                        "iss": f"kavia-google-sso",
                        "aud": creds['client_id'],
                        "iat": current_time,
                        "exp": current_time + 604800,  # 7 days
                        "custom:is_admin": str(is_admin).lower(),
                        "custom:free_user": str(is_free_user).lower(),
                        "custom:tenant_id": org_id,
                        "auth_provider": "google_sso",
                        "email_verified": True,
                        "token_use": "id"
                    }
                    
                    id_token = jwt.encode(token_payload, "kavia-google-sso-secret", algorithm="HS256")
                    
                    # Create refresh token
                    refresh_payload = {
                        "sub": cognito_user_id,
                        "token_use": "refresh",
                        "iat": current_time,
                        "exp": current_time + (30 * 24 * 60 * 60)  # 30 days
                    }
                    refresh_token = jwt.encode(refresh_payload, "kavia-google-sso-secret", algorithm="HS256")
                    
                    # Add user to node database if needed
                    background_tasks.add_task(add_user_node_db, user_info['email'], tenant_id)
                    
                    return JSONResponse(
                        content={
                            "authenticated": True,
                            "action": "signin",
                            "message": "User signed in automatically (password preserved)",
                            "provider": "google",
                            "user": {
                                "email": user_info['email'],
                                "name": user_info.get('name', ''),
                                "picture": user_info.get('picture', '')
                            },
                            "userId": cognito_user_id,
                            "username": user_info.get('name', ''),
                            "id_token": id_token,
                            "refresh_token": refresh_token,
                            "access_token": id_token,
                            "tenant_id": settings.KAVIA_B2C_CLIENT_ID if tenant_id.startswith("default") else tenant_id,
                            "organization_id": org_id,
                            "is_admin": is_admin,
                            "free_user": is_free_user,
                            "is_super_admin": tenant_id == settings.KAVIA_SUPER_TENANT_ID,
                            "ExpiresIn": 86400,
                            "TokenType": "Bearer",
                            "auth_method": "google_sso_passwordless"
                        }
                    )
                    
                except Exception as passwordless_error:
                    logger.error(f"Password-less authentication failed: {str(passwordless_error)}")
                    # Fall back to the original behavior if password-less auth fails
                    return JSONResponse(
                        content={
                            "authenticated": False,
                            "error": "user_exists",
                            "message": "User already exists. Please sign in instead.",
                            "user_info": {
                                "email": user_info['email'],
                                "name": user_info.get('name', ''),
                                "picture": user_info.get('picture', '')
                            }
                        }
                    )
            else:
                # Create the user in Cognito
                logger.info(f"Creating new user {user_info['email']} in Cognito")
                user_data = GoogleSignUpRequest(
                    email=user_info['email'],
                    name=user_info.get('name', ''),
                    picture=user_info.get('picture', ''),
                    tenant_id=tenant_id
                )
                logger.info(f"User data for signup: {user_data.model_dump()}")
                return await google_signup(user_data, background_tasks)

        # Default case: action is signin and user exists
        # Authenticate the user with Cognito using password-less method for Google SSO
        logger.info(f"Authenticating existing Google user {user_info['email']}")
        
        # Check if password-less authentication is enabled
        if ENABLE_PASSWORDLESS_GOOGLE_AUTH:
            try:
                logger.info(f"ENABLE_PASSWORDLESS_GOOGLE_AUTH is set to: {ENABLE_PASSWORDLESS_GOOGLE_AUTH}")
                logger.info(f"Attempting password-less authentication for user {user_info['email']}")
                # Use the new password-less authentication method
                auth_response_data = await authenticate_google_user_without_password(
                    user_info=user_info,
                    tenant_id=tenant_id,
                    client=client,
                    creds=creds
                )
                
                logger.info(f"Password-less authentication succeeded for user {user_info['email']}")
                # Add user to node database if needed
                background_tasks.add_task(add_user_node_db, user_info['email'], tenant_id)
                
                return JSONResponse(content=auth_response_data)
                
            except Exception as passwordless_error:
                logger.error(f"Password-less authentication failed for user {user_info['email']}: {str(passwordless_error)}")
                logger.error(f"Error type: {type(passwordless_error).__name__}")
                logger.warning(f"Password-less authentication failed, falling back to password method: {str(passwordless_error)}")
                # Continue to password-based authentication below
        else:
            logger.info(f"Password-less authentication is disabled (ENABLE_PASSWORDLESS_GOOGLE_AUTH={ENABLE_PASSWORDLESS_GOOGLE_AUTH}), using password-based method")

        # Fallback to password-based authentication
        logger.warning(f"Using password-based authentication for user {user_info['email']}")
        
        # IMPORTANT: For Google SSO users, we should NOT reset their password
        # Instead, let's try to authenticate them without changing their existing password
        try:
            # First, check if we can get user details without modifying anything
            user_details = client.admin_get_user(
                UserPoolId=creds['user_pool_id'],
                Username=user_info['email']
            )
            
            # Extract user attributes for the response
            is_admin = False
            is_free_user = True
            org_id = tenant_id
            cognito_user_id = user_details.get('Username')
            
            for attr in user_details.get('UserAttributes', []):
                if attr['Name'] == 'custom:is_admin' and attr['Value'].lower() == 'true':
                    is_admin = True
                elif attr['Name'] == 'custom:free_user' and attr['Value'].lower() == 'true':
                    is_free_user = True
                elif attr['Name'] == 'custom:tenant_id':
                    org_id = attr['Value']
            
            # Enable user if disabled, but DON'T reset password
            if user_details.get('Enabled', True) is False or user_details.get('UserStatus') == 'DISABLED':
                logger.info(f"Enabling disabled user {user_info['email']} without password reset")
                client.admin_enable_user(
                    UserPoolId=creds['user_pool_id'],
                    Username=user_info['email']
                )
            
            # Create a minimal response that indicates successful Google SSO without Cognito tokens
            # This preserves the user's existing password
        except client.exceptions.UserNotFoundException:
            logger.error(f"User {user_info['email']} not found in Cognito")
            raise HTTPException(status_code=404, detail="User not found in Cognito")
        
        # Extract user attributes for response
        is_admin = False
        is_free_user = True
        org_id = tenant_id
        
        for attr in user_details.get('UserAttributes', []):
            if attr['Name'] == 'custom:is_admin' and attr['Value'].lower() == 'true':
                is_admin = True
            elif attr['Name'] == 'custom:free_user' and attr['Value'].lower() == 'true':
                is_free_user = True
            elif attr['Name'] == 'custom:tenant_id':
                org_id = attr['Value']
        
        # Instead of creating custom tokens, try to generate tokens through Cognito without password
        # Use admin_create_user_auth_event or similar approach
        try:
            # Try to initiate auth using admin flow without password by creating temporary credentials
            # This is a workaround - we'll create a very short-lived session
            current_time = int(time.time())
            
            # Create a simple session token that your system can recognize as a Google SSO session
            # This mimics the structure your frontend expects but without using Cognito auth flows
            simple_session_token = jwt.encode({
                "sub": cognito_user_id,
                "email": user_info['email'],
                "cognito:username": user_info['email'],
                "iss": f"kavia-google-sso",
                "aud": creds['client_id'],
                "iat": current_time,
                "exp": current_time + 86400,  # 24 hours
                "custom:is_admin": str(is_admin).lower(),
                "custom:free_user": str(is_free_user).lower(),
                "custom:tenant_id": org_id,
                "auth_provider": "google_sso",
                "email_verified": True
            }, "kavia-secret-key", algorithm="HS256")
            
            # Create simple refresh token
            refresh_token = jwt.encode({
                "sub": cognito_user_id,
                "token_use": "refresh",
                "iat": current_time,
                "exp": current_time + (30 * 24 * 60 * 60)  # 30 days
            }, "kavia-secret-key", algorithm="HS256")
            
            logger.info(f"Created password-less session tokens for user {user_info['email']}")
            
            return {
                "authenticated": True,
                "action": "signin",
                "message": "User signed in successfully with Google SSO (password-less)",
                "provider": "google",
                "user": {
                    "email": user_info['email'],
                    "name": user_info.get('name', ''),
                    "picture": user_info.get('picture', ''),
                    "userId": cognito_user_id or user_info['email'],
                    "username": user_info.get('name', '')
                },
                "userId": cognito_user_id or user_info['email'],
                "username": user_info.get('name', ''),
                "id_token": simple_session_token,
                "refresh_token": refresh_token,
                "access_token": simple_session_token,  # Use same token
                "tenant_id": settings.KAVIA_B2C_CLIENT_ID if tenant_id.startswith("default") else tenant_id,
                "organization_id": org_id,
                "is_admin": is_admin,
                "free_user": is_free_user,
                "is_super_admin": tenant_id == settings.KAVIA_SUPER_TENANT_ID,
                "ExpiresIn": 86400,
                "TokenType": "Bearer"
            }
            
        except Exception as token_error:
            logger.error(f"Error creating session tokens: {str(token_error)}")
            # If session token creation fails, fall back to a simpler approach
            raise Exception(f"Session token creation failed: {str(token_error)}")
        
    except Exception as e:
        logger.error(f"Error in password-less Google authentication: {str(e)}")
        # Instead of raising HTTPException, just raise a regular exception to trigger fallback
        raise Exception(f"Password-less authentication failed: {str(e)}")

async def authenticate_google_user_without_password(user_info: dict, tenant_id: str, client, creds: dict) -> dict:
    """
    Authenticate a Google SSO user without resetting their password.
    """
    try:
        logger.info(f"Authenticating Google user {user_info['email']} without password reset")
        
        # Get user details
        user_details = client.admin_get_user(
            UserPoolId=creds['user_pool_id'],
            Username=user_info['email']
        )
        cognito_user_id = user_details.get('Username')
        
        # Extract user attributes
        is_admin = False
        is_free_user = True
        org_id = tenant_id
        
        for attr in user_details.get('UserAttributes', []):
            if attr['Name'] == 'custom:is_admin' and attr['Value'].lower() == 'true':
                is_admin = True
            elif attr['Name'] == 'custom:free_user' and attr['Value'].lower() == 'true':
                is_free_user = True
            elif attr['Name'] == 'custom:tenant_id':
                org_id = attr['Value']
        
        # Enable user if disabled (but don't reset password)
        if user_details.get('Enabled', True) is False:
            client.admin_enable_user(
                UserPoolId=creds['user_pool_id'],
                Username=user_info['email']
            )
        
        # Create JWT tokens without password authentication
        current_time = int(time.time())
        id_token = jwt.encode({
            "sub": cognito_user_id,
            "email": user_info['email'],
            "cognito:username": user_info['email'],
            "iss": "kavia-google-sso",
            "aud": creds['client_id'],
            "iat": current_time,
            "exp": current_time + 86400,
            "custom:is_admin": str(is_admin).lower(),
            "custom:free_user": str(is_free_user).lower(),
            "custom:tenant_id": org_id,
            "auth_provider": "google_sso",
            "email_verified": True
        }, "kavia-google-sso-secret", algorithm="HS256")
        
        refresh_token = jwt.encode({
            "sub": cognito_user_id,
            "token_use": "refresh",
            "iat": current_time,
            "exp": current_time + (30 * 24 * 60 * 60)
        }, "kavia-google-sso-secret", algorithm="HS256")
        
        return {
            "authenticated": True,
            "action": "signin",
            "message": "User signed in successfully with Google SSO (password preserved)",
            "provider": "google",
            "user": {
                "email": user_info['email'],
                "name": user_info.get('name', ''),
                "picture": user_info.get('picture', ''),
                "userId": cognito_user_id,
                "username": user_info.get('name', '')
            },
            "userId": cognito_user_id,
            "username": user_info.get('name', ''),
            "id_token": id_token,
            "refresh_token": refresh_token,
            "access_token": id_token,
            "tenant_id": settings.KAVIA_B2C_CLIENT_ID if tenant_id.startswith("default") else tenant_id,
            "organization_id": org_id,
            "is_admin": is_admin,
            "free_user": is_free_user,
            "is_super_admin": tenant_id == settings.KAVIA_SUPER_TENANT_ID,
            "ExpiresIn": 86400,
            "TokenType": "Bearer"
        }
        
    except Exception as e:
        logger.error(f"Error in password-less Google authentication: {str(e)}")
        raise Exception(f"Password-less authentication failed: {str(e)}")

@router.get("/debug/google-passwordless")
async def debug_google_passwordless():
    """Debug endpoint to check Google password-less authentication configuration"""
    return {
        "ENABLE_PASSWORDLESS_GOOGLE_AUTH": ENABLE_PASSWORDLESS_GOOGLE_AUTH,
        "settings_value": getattr(settings, 'ENABLE_PASSWORDLESS_GOOGLE_AUTH', 'NOT_SET'),
        "google_client_id": settings.GOOGLE_CLIENT_ID[:10] + "..." if settings.GOOGLE_CLIENT_ID else "NOT_SET",
        "message": "Password-less authentication is " + ("ENABLED" if ENABLE_PASSWORDLESS_GOOGLE_AUTH else "DISABLED")
    }

@router.get("/verify-token")
async def verify_token(token: str = Query(..., description="JWT token to verify")):
    """Verify a JWT token"""
    try:
        # Decode the token without verification (just to get the claims)
        # In a production environment, you should verify the signature
        decoded_token = jwt.decode(token, options={"verify_signature": False})
        
        return JSONResponse(content={"valid": True, "user": decoded_token})
    except Exception as e:
        logger.error(f"Error verifying token: {str(e)}")
        return JSONResponse(content={"valid": False, "error": str(e)})

@router.post("/update-auth-flows")
async def update_auth_flows(tenant_id: str = Query(..., description="Tenant ID")):
    """Update auth flows for a tenant's app client"""
    try:
        tenant_id = decrypt_tenant_id(decode_tenant_id(tenant_id))
        creds = await get_tenant_credentials(tenant_id)
        
        # Update the app client auth flows
        cognito_pool = CognitoUserPoolCreator()
        updated_client = cognito_pool.update_app_client_auth_flows(
            user_pool_id=creds['user_pool_id'],
            client_id=creds['client_id']
        )
        
        return {
            "message": "Auth flows updated successfully",
            "client_id": updated_client['ClientId'],
            "auth_flows": updated_client.get('ExplicitAuthFlows', [])
        }
    except Exception as e:
        logger.error(f"Error updating auth flows: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error updating auth flows: {str(e)}")

@router.get("/check-auth-flows")
async def check_auth_flows(tenant_id: str = Query(..., description="Tenant ID")):
    """Check current auth flows for a tenant's app client"""
    try:
        tenant_id = decrypt_tenant_id(decode_tenant_id(tenant_id))
        creds = await get_tenant_credentials(tenant_id)
        
        # Get the Cognito client
        cognito_pool = CognitoUserPoolCreator()
        
        # Get client details
        client_details = cognito_pool.cognito.describe_user_pool_client(
            UserPoolId=creds['user_pool_id'],
            ClientId=creds['client_id']
        )['UserPoolClient']
        
        return {
            "message": "Auth flows retrieved successfully",
            "client_id": client_details['ClientId'],
            "client_name": client_details.get('ClientName', ''),
            "auth_flows": client_details.get('ExplicitAuthFlows', []),
            "token_validity": {
                "access_token": f"{client_details.get('AccessTokenValidity', 0)} {client_details.get('TokenValidityUnits', {}).get('AccessToken', 'hours')}",
                "id_token": f"{client_details.get('IdTokenValidity', 0)} {client_details.get('TokenValidityUnits', {}).get('IdToken', 'hours')}",
                "refresh_token": f"{client_details.get('RefreshTokenValidity', 0)} {client_details.get('TokenValidityUnits', {}).get('RefreshToken', 'days')}"
            }
        }
    except Exception as e:
        logger.error(f"Error checking auth flows: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error checking auth flows: {str(e)}")

# Add a new model for Google user signup
class GoogleSignUpRequest(BaseModel):
    email: str
    name: str
    picture: Optional[str] = None
    tenant_id: str
    department: Optional[str] = "External"
    designation: Optional[str] = "Google User"

# Update the password generation to ensure it meets Cognito requirements (used only for new user creation, not Google SSO)
def generate_secure_password():
    # Ensure at least one of each required character type
    lowercase = ''.join(random.choices(string.ascii_lowercase, k=3))
    uppercase = ''.join(random.choices(string.ascii_uppercase, k=3))
    numbers = ''.join(random.choices(string.digits, k=3))
    special = ''.join(random.choices('!@#$%^&*()_+-=[]{}|', k=3))
    
    # Combine all characters and add some random ones to meet length requirement
    all_chars = lowercase + uppercase + numbers + special
    # Add extra random characters to make it 16 characters long
    extra = ''.join(random.choices(string.ascii_letters + string.digits + '!@#$%^&*()_+-=[]{}|', k=4))
    
    # Combine and shuffle
    password = list(all_chars + extra)
    random.shuffle(password)
    return ''.join(password)

@router.post("/google/signup")
async def google_signup(
    user_data: GoogleSignUpRequest,
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """
    Endpoint to create a new user account from Google authentication data.
    This creates NEW users without password operations for existing users.
    """
    try:
        # Generate a unique tenant ID for the new user
        random_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
        org_id = settings.KAVIA_B2C_CLIENT_ID
        tenant_id = user_data.tenant_id

        if tenant_id != settings.KAVIA_B2C_CLIENT_ID:
            raise HTTPException(status_code=400, detail="Signup is not allowed for this organization")
        
        logger.info(f"Creating Google user account for {user_data.email} in tenant {tenant_id}")
        
        # Get Cognito credentials for the tenant
        creds = await get_tenant_credentials(tenant_id)
        client = boto3.client('cognito-idp', region_name=settings.AWS_REGION,
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
        
        # Check if the user already exists
        try:
            client.admin_get_user(
                UserPoolId=creds['user_pool_id'],
                Username=user_data.email
            )
            # User already exists, use password-less sign in
            logger.info(f"User {user_data.email} already exists, using password-less sign in")
            
            # Return password-less authentication response
            user_details = client.admin_get_user(
                UserPoolId=creds['user_pool_id'],
                Username=user_data.email
            )
            
            # Extract user attributes
            is_admin = False
            is_free_user = True
            org_id = ""
            cognito_user_id = user_details.get('Username')
            
            for attr in user_details.get('UserAttributes', []):
                if attr['Name'] == 'custom:is_admin' and attr['Value'].lower() == 'true':
                    is_admin = True
                elif attr['Name'] == 'custom:free_user' and attr['Value'].lower() == 'true':
                    is_free_user = True
                elif attr['Name'] == 'custom:tenant_id':
                    org_id = attr['Value']
            
            # Create JWT tokens without password operations
            current_time = int(time.time())
            id_token = jwt.encode({
                "sub": cognito_user_id,
                "email": user_data.email,
                "cognito:username": user_data.email,
                "iss": f"kavia-google-sso",
                "aud": creds['client_id'],
                "iat": current_time,
                "exp": current_time + 86400,
                "custom:is_admin": str(is_admin).lower(),
                "custom:free_user": str(is_free_user).lower(),
                "custom:tenant_id": org_id,
                "auth_provider": "google_sso",
                "email_verified": True
            }, "kavia-google-sso-secret", algorithm="HS256")
            
            refresh_token = jwt.encode({
                "sub": cognito_user_id,
                "token_use": "refresh",
                "iat": current_time,
                "exp": current_time + (30 * 24 * 60 * 60)
            }, "kavia-google-sso-secret", algorithm="HS256")
            
            background_tasks.add_task(add_user_node_db, user_data.email, tenant_id)
            
            return JSONResponse(
                content={
                    "authenticated": True,
                    "action": "signin",
                    "message": "User signed in successfully (password preserved)",
                    "provider": "google",
                    "user": {
                        "email": user_data.email,
                        "name": user_data.name,
                        "picture": user_data.picture
                    },
                    "userId": cognito_user_id,
                    "username": user_data.name,
                    "id_token": id_token,
                    "refresh_token": refresh_token,
                    "access_token": id_token,
                    "tenant_id": settings.KAVIA_B2C_CLIENT_ID if tenant_id.startswith("default") else tenant_id,
                    "organization_id": org_id,
                    "is_admin": is_admin,
                    "free_user": is_free_user,
                    "is_super_admin": tenant_id == settings.KAVIA_SUPER_TENANT_ID,
                    "ExpiresIn": 86400,
                    "TokenType": "Bearer",
                    "auth_method": "google_sso_passwordless"
                }
            )

        except client.exceptions.UserNotFoundException:
            # User doesn't exist, proceed with creation (this is for NEW users only)
            pass
        except Exception as e:
            logger.error(f"Error checking user existence: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error checking user existence: {str(e)}")

        # Create NEW user in Cognito (this is the only place where we create passwords for NEW users)
        user_manager = CognitoUserManager(
            user_pool_id=creds['user_pool_id'],
            client_id=creds['client_id']
        )

        # Update Cognito custom attributes
        user_manager.add_free_tier_to_pool()

        # Only generate password for NEW user creation (not for existing Google SSO users)
        random_password = generate_secure_password()

        # Create the user in Cognito
        cognito_response = client.admin_create_user(
            UserPoolId=creds['user_pool_id'],
            Username=user_data.email,
            UserAttributes=[
                {'Name': 'email', 'Value': user_data.email},
                {'Name': 'email_verified', 'Value': 'true'},
                {'Name': 'custom:Name', 'Value': user_data.name},
                {'Name': 'custom:Designation', 'Value': user_data.designation},
                {'Name': 'custom:Department', 'Value': user_data.department},
                {'Name': 'custom:tenant_id', 'Value': org_id},
                {'Name': 'custom:is_admin', 'Value': 'true'},
                {'Name': 'custom:free_user', 'Value': 'true'}
            ],
            MessageAction='SUPPRESS' if tenant_id == settings.KAVIA_B2C_CLIENT_ID else 'RESEND'
        )
        
        # Set password for NEW user only
        client.admin_set_user_password(
            UserPoolId=creds['user_pool_id'],
            Username=user_data.email,
            Password=random_password,
            Permanent=True
        )

        # Create user in MongoDB
        user_id = cognito_response['User']['Username']
        opentopublic = get_opentopublic()
        
        # Create subscription
        active_subscription = get_mongo_db(db_name=DB_NAME, collection_name="active_subscriptions")
        subscription_data = {
            "tenant_id": org_id,
            "price_id": "price_1RWBNuCI2zbViAE2N6TkeNVB",
            "credits": 50000,
            "created_at": datetime.utcnow().isoformat()
        }

        await active_subscription.update_one_data(
            {"user_id": user_id},
            {"$set": subscription_data},
            upsert=True
        )
        llm_cost_collection = get_mongo_db(db_name=DB_NAME, collection_name="llm_costs")
        llm_cost_data = {
               "user_id": user_id,
                "type": "llm_interaction",
                "user_cost": "$0",
                "projects": [], 
                "cost": "$0",
                "plans_history": [],  
                "current_plan": "price_1RWBNuCI2zbViAE2N6TkeNVB",  
                "_task_tracking": {}, 
            }
        await llm_cost_collection.update_one_data(
            {"organization_id": org_id},
            {"$push": {"users": llm_cost_data}},
            upsert=True

        )

        # Create user data object
        mongo_user_data = {
            "_id": user_id,
            "email": user_data.email,
            "name": user_data.name,
            "contact_number": "",
            "department": user_data.department,
            "organization_id": org_id,
            "group_ids": [],
            "is_admin": True,
            "free_user": True,
            "designation": user_data.designation,
            "status": "active" if opentopublic else "inactive",
            "auth_provider": "google",
            "picture": user_data.picture or ""
        }
        
        # Save user to database
        try:
            await User.create(mongo_user_data)
        except Exception as e:
            if "duplicate key error" in str(e):
                logger.warning(f"User {user_id} already exists in database, skipping creation")
            else:
                logger.error(f"Error creating user in database: {str(e)}")

        # Add user to node database
        background_tasks.add_task(add_user_node_db, user_id, org_id)

        # Return success without authentication (user needs to sign in)
        return JSONResponse(
            content={
                "authenticated": False,
                "action": "signup",
                "message": "User created successfully. Please sign in with Google SSO.",
                "user_created": True,
                "email": user_data.email
            }
        )
    except Exception as e:
        logger.error(f"Error during Google signup: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error during Google signup: {str(e)}"
        )

@router.get("/user-organizations", summary="Get all organizations for a user")
async def get_user_organizations(email: str = Query(..., description="User's email address")):
    """
    Get all organizations/tenants associated with a user's email address.
    """
    try:
        # Initialize MongoDB connection
        users_collection = User.get_collection()
        org_collection = Organization.get_collection()
        
        # Query users with matching email
        users = list(users_collection.find({"email": email}))
        
        # Extract organization IDs
        org_ids = [user.get('organization_id') for user in users if user.get('organization_id')]
        
        # Remove duplicates while preserving order
        org_ids = list(dict.fromkeys(org_ids))
        
        # Get organization names for each organization ID
        orgs_cursor = org_collection.find(
            {"_id": {"$in": org_ids}},
            {"name": 1, "status": 1}
        )
        
        organizations = [
            {
                "id": org["_id"],
                "name": org.get("name", "Unknown Organization")
            }
            for org in list(orgs_cursor) if org.get("status") == "active"
        ]
        
        response = {
            "email": email,
            "organizations": organizations
        }
        
        return response
        
    except Exception as e:
        logger.error(f"Error getting organizations for user {email}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving organizations: {str(e)}"
        )