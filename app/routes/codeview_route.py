from fastapi import APIRouter, Depends
from fastapi.responses import StreamingResponse
from app.core.Settings import settings
from app.classes.Ec2Repository import Ec2Repository
from app.classes.CodeViewRepository import CodeViewRepository
from app.connection.establish_db_connection import get_node_db
from app.utils.auth_utils import check_token_and_get_user_id
from app.utils.auth_utils import get_current_user
import json,logging,asyncio
import asyncio
import json
from fastapi.responses import StreamingResponse
from fastapi import HTTPException
from fastapi.responses import StreamingResponse
from fastapi import HTTPException
import requests, threading, time
from fastapi import BackgroundTasks
from app.classes.Ec2Handler import Ec2<PERSON><PERSON>ler

# Set up logging
logging.basicConfig(level=logging.INFO)

node_db = get_node_db()

_SHOW_NAME = "codeview"

router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)

creds = {
    "aws_access_key_id":settings.AWS_ACCESS_KEY_ID,
    "aws_secret_access_key":settings.AWS_SECRET_ACCESS_KEY,
    "region_name":settings.AWS_REGION
}

print("AMI",settings.AWS_AMI)
_EC2_REPO = Ec2Repository(creds,ami_id=settings.AWS_AMI)

_CodeViewRepository = CodeViewRepository(_EC2_REPO)

def get_user_from_email(email):
    return email.split("@")[0]

@router.get("/status")
async def get_status(project_id,current_user=Depends(get_current_user)):
    user_name = current_user['cognito:username']
    # return _CodeViewRepository.get_instance_status(user_name,project_id)
    return {
  "status": "notfound",
  "url": "notfound"
}


@router.get("/get_user_project_url")
async def get_project_url(project_id,current_user=Depends(get_current_user)):
    user_name = current_user['cognito:username']
    try:
        clone_url = await node_db.get_clone_url_by_projectid(project_id)
    except:
        return {
            'error':True,
            'message':'Project Not Found',
            'end':True
        }
    if clone_url[0] == None:
        return {
            'error':'Repo not found',
            'end':True
        }
    project_obj = {
        "repo_url":clone_url[0],
        "repo_name":clone_url[1],
        **creds,
        "user_name":user_name,
        "email":current_user['email']
    }
    return _CodeViewRepository.get_instance_ip(project_obj,project_id)


@router.get("/get_user_project_url_stream_old")
async def get_project_url_stream(project_id,current_user=Depends(get_current_user)):
    user_name = current_user['cognito:username']
    try:
        clone_url = await node_db.get_clone_url_by_projectid(project_id)
    except:
        return {
            'error':True,
            'message':'Project Not Found',
            'end':True
        }
    if clone_url[0] == None:
        return {
            'message':'Repo Not found for this Project',
            'error':True,
            'end':True
        }
    project_obj = {
        "repo_url":clone_url[0],
        "repo_name":clone_url[1],
        **creds,
        "user_name":user_name,
        "email":current_user['email']
    }
    async_generator =  _CodeViewRepository.get_instance_ip_stream(project_obj,project_id)
    async def json_generator():
        async for message in async_generator:
            yield json.dumps(message) + "\n"
            await asyncio.sleep(2)
    return StreamingResponse(json_generator(), media_type="application/json")


@router.delete("/stop_project")
def stop_project_instance(project_id,current_user=Depends(get_current_user)):
    return _CodeViewRepository.stop_instance(current_user['cognito:username'],project_id)


@router.delete("/delete_project")
def stop_project_instance(project_id,current_user=Depends(get_current_user)):
    return _CodeViewRepository.terminate_instance(current_user['cognito:username'],project_id)

def fetch_data(api_url, result):
    """ Thread target function to fetch data from a given URL. """
    try:
        response = requests.get(api_url)
        result['data'] = json.loads(response.text)
    except Exception as e:
        result['error'] = str(e)

@router.get("/get_user_project_url_stream")
async def get_code_view_url(
    project_id: str,
    background_tasks: BackgroundTasks,
    current_user=Depends(get_current_user)
):
    max_retries = 3
    retry_count = 0
    ec2_handler = Ec2Handler()
    
    while retry_count < max_retries:
        try:
            # Check instance availability
            stage = 'dev' if settings.STAGE == 'develop' else settings.STAGE
            
            async def code_view_generator():
                try:
                    async for status in ec2_handler.get_project(project_id, stage):
                        if status.get('end', False):
                            # Final response with workspace URL
                            yield json.dumps({
                                "status": "running",
                                "ip": status["ip"],
                                "url": f'https://{status["ip"]}.nip.io/?folder=/home/<USER>/workspace',
                                "message": "Your Workspace is ready!",
                                "error": False,
                                "end": True
                            }) + "\n"
                            break
                        else:
                            # Progress updates
                            # current_stage = status["message"].split("|")[-1]
                            # messages = {
                            #     "install": "Setting up your workspace...",
                            #     "provision": "Preparing your environment...",
                            #     "config": "Configuring your workspace..."
                            # }
                            if "config" in status["message"]:
                                for wait in range(15): 
                                    yield json.dumps({
                                        "message": status["message"],
                                        "error": False,
                                        "end": False
                                    }) + "\n"
                                    await asyncio.sleep(2)
                            yield json.dumps({
                                "message": status["message"],
                                "error": False,
                                "end": False
                            }) + "\n"
                        
                        await asyncio.sleep(2)
                        
                except TimeoutError:
                    yield json.dumps({
                        "message": "Workspace setup timed out. Please try again.",
                        "error": True,
                        "end": True
                    }) + "\n"
                except Exception as e:
                    yield json.dumps({
                        "message": f"An error occurred: {str(e)}",
                        "error": True,
                        "end": True
                    }) + "\n"

            return StreamingResponse(code_view_generator(), media_type="application/json")

        except Exception as e:
            retry_count += 1
            if retry_count >= max_retries:
                return {
                    "message": "Please try again!",
                    "error": True,
                    "end": True
                }
            else:
                logging.warning(f"Attempt {retry_count} failed. Retrying... Error: {str(e)}")
                await asyncio.sleep(2)
