import argparse
import sys
import os
import json
import tempfile
from app.utils.aws.secrets_loader import get_vertex_secret
from code_generation_core_agent.config import config as code_generation_config
from app.utils.docker_utils import get_container_ports, _process_container_ports
def setup_google_credentials(persistent_path="google_credentials.json"):
    try:
        # Check if credentials file already exists
        if os.path.exists(persistent_path):
            print(f"Using existing credentials file at: {persistent_path}")
            # Set the environment variable
            os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = os.path.abspath(persistent_path)
            return os.path.abspath(persistent_path)
        
        # Get the vertex secret data
        credentials_data = get_vertex_secret()
        
        # Print type and sample of credentials
        print(f"Type of credentials: {type(credentials_data)}")

        # Write to the persistent path
        with open(persistent_path, 'w') as f:
            # Handle both dictionary and string cases
            if isinstance(credentials_data, dict):
                json.dump(credentials_data, f)
            else:
                # If it's already a string, write directly
                f.write(credentials_data)
        
        # Get absolute path
        absolute_path = os.path.abspath(persistent_path)
        
        # Set the environment variable
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = absolute_path
        
        print(f"GOOGLE_APPLICATION_CREDENTIALS set to: {absolute_path}")
        return absolute_path
    except Exception as e:
        print(f"Error setting up credentials: {e}")
        return None

# Call the function to setup credentials
credentials_file_path = setup_google_credentials()

# Print current path for debugging
print("Current PYTHONPATH:", os.environ.get("PYTHONPATH"))
print("Current sys.path:", sys.path)
os.environ['GOOGLE_PROJECT_ID'] = "vertex-ai-sandbox-447902"
os.environ['GOOGLE_LOCATION'] = "us-central1"


# Ensure the app directory is in the path
app_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if app_root not in sys.path:
    sys.path.insert(0, app_root)
    
from dotenv import load_dotenv
import os
from app.connection.establish_db_connection import get_mongo_db
from app.core.constants import TaskStatus, TASKS_COLLECTION_NAME
from app.core.code_generation import execute_code_generation_agent
from app.routes.batch_route import task_execute, task_execute_maintenance
from app.utils.async_utils import async_to_sync
import json
import traceback
import sys
# from app.telemetry.logger_config import set_task_id
from app.models.user_model import LLMModel
from app.connection.tenant_middleware import tenant_context
from app.celery_app import user_context
from app.models.code_generation_model import AgentParams, Message
from datetime import datetime
from app.core.websocket.client import WebSocketClient
from app.core.Settings import settings
import subprocess
from app.utils.datetime_utils import generate_timestamp
from app.utils.code_generation_utils import get_codegeneration_path
from app.utils.project_utils import find_host
print("PHASE 1, START TIME: ", generate_timestamp())
def parse_arguments():
    parser = argparse.ArgumentParser(description='Code Generation Job Runner')
    parser.add_argument('--input_args', type=str, required=True,
                      help='JSON string containing input arguments')
    parser.add_argument('--stage', type=str, required=True,
                      help='Stage for code generation')
    return parser.parse_args()

args = parse_arguments()
input_arguments = json.loads(args.input_args)
print(input_arguments)
user_id = input_arguments.get("user_id")
user_context.set(user_id)
if user_id is not None:
    os.environ["user_id"] = user_id
batch_job_trigger = os.environ.get("BATCH_JOB_TRIGGER", False)
print(batch_job_trigger)
task_id = input_arguments.get("task_id")
os.environ["task_id"] = task_id
retry = input_arguments.get("retry")
tenant_id = input_arguments.get("tenant_id")
print("Tenant_id for code generation", tenant_id)
tenant_context.set(tenant_id)
if tenant_id == settings.KAVIA_B2C_CLIENT_ID:
    db = get_mongo_db(user_id=user_id).db
else:
    db = get_mongo_db().db
    
ws_client = WebSocketClient(task_id, settings.WEBSOCKET_URI)
agent_name = input_arguments.get("agent_name")
if not agent_name:
    agent_name = "CodeGeneration"
        
try:
    ws_client.connect()
except:
    pass

path = get_codegeneration_path(agent_name=agent_name, task_id=task_id)
mount_path = "/home/<USER>/workspace"

if os.getenv("LOCAL_DEBUG"):
    mount_path = "/tmp/"
    

def pull_docker_image(image_name, docker_hub_image=None):
    """
    Pull a Docker image from Docker Hub and optionally tag it with a local name.
    
    Args:
        image_name: Local name to tag the image with
        docker_hub_image: Docker Hub image path. If None, uses image_name
        
    Returns:
        bool: True if successful, False otherwise
    """
    import subprocess
    
    # If no Docker Hub image is specified, use the image_name
    if docker_hub_image is None:
        docker_hub_image = image_name
    
    try:
        # Check if the image exists locally
        result = subprocess.run(
            ["docker", "images", image_name, "--format", "{{.Repository}}"],
            capture_output=True, text=True, check=False
        )
        
        if image_name not in result.stdout:
            print(f"Image {image_name} not found, pulling from Docker Hub...")
            
            # Pull the image from Docker Hub
            pull_result = subprocess.run(
                ["docker", "pull", docker_hub_image],
                capture_output=True, text=True, check=False
            )
            
            if pull_result.returncode != 0:
                print(f"Failed to pull image: {pull_result.stderr}")
                return False
            
            # Tag the pulled image if the names are different
            if docker_hub_image != image_name:
                tag_result = subprocess.run(
                    ["docker", "tag", docker_hub_image, image_name],
                    capture_output=True, text=True, check=False
                )
                
                if tag_result.returncode != 0:
                    print(f"Failed to tag image: {tag_result.stderr}")
                    return False
                    
            print(f"Successfully pulled and tagged image {image_name}")
            return True
        else:
            print(f"Image {image_name} already exists locally")
            return True
        
    except Exception as e:
        print(f"Error pulling Docker image: {str(e)}")
        return False

def setup_docker_container(mount_path="/home/<USER>/workspace", 
                           ports=None, fallback_without_ports=True):
    """
    Setup docker container for code generation with enhanced port forwarding.
    Checks if image exists, pulls if needed, and ensures container is running.
    
    Args:
        mount_path: Path to mount into container
        ports: List of port mappings (e.g. ["3001:3000", "8001:8000"])
        fallback_without_ports: Whether to try without ports if port binding fails
    
    Returns:
        bool: True if setup was successful, False otherwise
    """
    import subprocess
    import json
    import logging
    
    # Configure logging for better debugging
    logging.basicConfig(level=logging.INFO, 
                       format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)
    
    container_name = "kavia_default_container_custom"
    image_name = "kavia_default_container_image:latest"
    
    # Get ports from config if not provided
    if ports is None:
        raw_ports = get_container_ports(logger=logger)
        logger.info(f"Raw ports from config: {raw_ports}")
        
        # Post-process the ports based on the format returned by get_container_ports
        ports = _process_container_ports(raw_ports, logger)
        logger.info(f"Processed port mappings: {ports}")

    try:
        # Check if container is already running
        ps_result = subprocess.run(
            ["docker", "ps", "--filter", f"name={container_name}", "--format", "{{.Names}}"],
            capture_output=True, text=True, check=False
        )
        
        if container_name in ps_result.stdout:
            logger.info(f"Container {container_name} is already running")
            return True
        
        # Container not running - clean up any stopped instance
        logger.info(f"Container {container_name} not running, removing any stopped instance...")
        subprocess.run(
            ["docker", "rm", "-f", container_name],
            capture_output=True, text=True, check=False
        )

        # Build the docker run command with ports
        run_cmd = [
            "docker", "run", "-d", "--restart", "always",
            "-e", "LOCAL_UID=1000",
            "-e", "LOCAL_GID=1000",
            "-v", f"{mount_path}:{mount_path}:rw,shared",
            "--name", container_name
        ]
        
        # Add port mappings before the image name
        for port in ports:
            run_cmd.extend(["-p", port])
        
        # Add image name at the end
        run_cmd.append(image_name)
        
        # Log the command for debugging
        logger.info(f"Docker command: {' '.join(run_cmd)}")
        
        # Try to run with ports
        logger.info(f"Starting container {container_name} with ports: {ports}")
        run_result = subprocess.run(run_cmd, capture_output=True, text=True, check=False)
        
        # If port binding fails and fallback is enabled, try without ports
        if run_result.returncode != 0 and fallback_without_ports:
            logger.warning(f"Failed to start with ports: {run_result.stderr}")
            logger.info("Attempting to start without port forwarding...")
            
            # Rebuild command without ports
            run_cmd_no_ports = [
                "docker", "run", "-d", "--restart", "always",
                "-e", "LOCAL_UID=1000",
                "-e", "LOCAL_GID=1000",
                "-v", f"{mount_path}:{mount_path}:rw,shared",
                "--name", container_name,
                image_name
            ]
            
            run_result = subprocess.run(run_cmd_no_ports, capture_output=True, text=True, check=False)
        
        if run_result.returncode != 0:
            logger.error(f"Failed to run container: {run_result.stderr}")
            return False
            
        logger.info(f"Successfully started container {container_name}")
        return True
        
    except Exception as e:
        logger.exception(f"Error setting up Docker container: {str(e)}")
        return False
    
if os.getenv("LOCAL_DEBUG"):
    # docker run --privileged -d --restart always -p 3000:3000 -p 5000:5000 -p 8000:8000 -v /home/<USER>/workspace:/home/<USER>/workspace:rw,shared --user root --entrypoint "/bin/sh" --name kavia_default_container kavia_default_container_image:latest -c "while true; do sleep 30; done" 
    try:
        has_docker_started = setup_docker_container(mount_path)
        if not has_docker_started:
            print("Failed to setup docker container, exiting...")
            sys.exit(1)
    except Exception as e:
        print(f"Failed to run docker container: {str(e)}")
        raise e

task = db[TASKS_COLLECTION_NAME].find_one({ "_id": task_id })
new_repo_creation = task.get("new_repo_creation","")
os.environ["new_repo_creation"] = str(new_repo_creation)
project_repository = task.get("repositories")
os.environ["repositories"] = json.dumps(project_repository)
iframe = task.get("iframe")
os.environ["iframe"] = str(iframe)
if iframe:
    try:
        host = find_host(iframe)
        os.environ["host"] = host
        print("Host for iframe", host)
    except Exception as e:
        print("Error finding host for iframe", str(e))
        pass
    
if retry:
    
    project_id = task.get("project_id")
    container_id = task.get("container_id")
    architecture_id = task.get("architecture_id")
    test_case = task.get("test_case")
    print("Project_id ", project_id, "Architecture_id ", architecture_id, "Container_id: ", container_id)
    work_item = task.get("work_item_details")
    llm_model = task.get("llm_model") 
    agent_name = task.get("agent_name") or "CodeGeneration"
    input_arguments["project_id"] = project_id
    os.environ["input_arguments"] = json.dumps(input_arguments)
    if agent_name == "CodeMaintenance":
        
        project_details, work_items = async_to_sync(task_execute_maintenance(project_id))

    else:
        # project_details.get('manifest')
        # get_codegeneration_path
        project_details, work_items = async_to_sync(task_execute(project_id, container_id=container_id , test_case=test_case))
        
    
    agent_params = AgentParams(
        llm_model=llm_model,
        agent_name=agent_name
    )

    execute_code_generation_agent(project_details=project_details, work_item_details=task, task_id=task_id, agent_params=agent_params, retry=False)
else:  
    llm_model = code_generation_config.get("LLM", "model") or input_arguments.get("llm_model")
    project_id = int(input_arguments.get("project_id"))

    agent_params = AgentParams(
        llm_model=llm_model,
        agent_name=agent_name
    )
    print("agent details", agent_params.model_dump_json())
    if agent_name == "CodeMaintenance":
        print("++ STARTING CODE MAINTENANCE SESSION ++")
    elif agent_name == "DocumentCreation":
        print("++ STARTING CODE MAINTENANCE SESSION ++")
    else:
        container_id = int(input_arguments.get("container_id"))
        print("Starting code generation job")

    
    print(f"Executing code generation for project {project_id}")
    
    try:
        if agent_name == "CodeMaintenance":
            project_details, work_items = async_to_sync(task_execute_maintenance(project_id))

        elif agent_name == "DocumentCreation":
            
            project_details, work_items = async_to_sync(task_execute_maintenance(project_id))
            
            work_items = {
                "project_node_id": project_id,
                "github_links": [f"https://github.com/{repo}" for repo in task["repositories"]["selected_repos"]]
            }
        else:
            test_case = input_arguments.get("test_case")
            project_details, work_items = async_to_sync(task_execute(project_id, container_id=container_id, test_case=test_case))
        
        print("Project Details: ", project_details)
        print("WorkItem Detais: ", work_items)
        

        execute_code_generation_agent(project_details, work_items, task_id=task_id , agent_params=agent_params)
        
    except Exception as e:
        print(f"Failed to execute code generation: {str(e)}")
        error_traceback = traceback.format_exc()
        print("\nFull traceback:")
        print(error_traceback)

        db[TASKS_COLLECTION_NAME].update_one(
            {"_id": task_id}, 
            {"$set": {
                "status": TaskStatus.FAILED,
                "execution_status": TaskStatus.FAILED
            }}
        )
        sys.exit(1)

