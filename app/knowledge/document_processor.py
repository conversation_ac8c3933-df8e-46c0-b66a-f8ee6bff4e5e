import time
from app.connection.tenant_middleware import get_tenant_id
from app.utils.prodefn.projdefn import <PERSON><PERSON><PERSON><PERSON><PERSON>, ProjDefn<PERSON><PERSON>orter, ProjDefnDocSpecifier
from app.utils.prodefn.projdefn_helper import Reporter, Helpers, ProjDefn_Helper
from datetime import datetime
from app.core.Settings import Settings
from app.utils.file_utils.upload_utils import upload_and_process, s3_client, get_tenant_bucket
import asyncio
from app.connection.establish_db_connection import get_mongo_db
from app.core.websocket.client import WebSocketClient

settings = Settings()

class DocumentProcessor:
    
    def __init__(self, file_uuid=None):
        """
        Initialize the DocumentProcessor with a name.
        
        Args:
            name (str): Name identifier for this processor instance. Defaults to "nattu".
        """
        self.ws_client = None
        self.criteria_processed_count = 0
        self.total_criteria_count = 0
        self.elapsed_time = 0
        self.estimated_time = 0
        self.percent = 0
        if file_uuid:
            self.ws_client = WebSocketClient(file_uuid, settings.WEBSOCKET_URI)
        self.settings = Settings()
    
        """Handles document processing and WebSocket communication"""
    
  
    # async def process_document(self, temp_file_path, file_uuid, session_id, filename, 
    #                             project_id, tenant_id, user_id, user_name,  original_s3_path = None, file_size=0, file_type=None, file_extension=None):
    #     """Process a document and communicate progress via WebSocket"""
            
    #     # Create WebSocket client
    #     self.ws_client = WebSocketClient(file_uuid, settings.WEBSOCKET_URI)
    #     self.ws_client.connect()  # Ensure connection is attempted
        
    #     try:
    #         # Reset counters for this new document
    #         self.criteria_processed_count = 0
    #         self.total_criteria_count = 0
    #         self.percent = 0
    #         self.elapsed_time = 0
    #         self.estimated_time = 0
            
    #         # Send initial status
    #         self.ws_client.send_message("document_processing", {
    #             'status': 'processing',
    #             'progress': 0,
    #             'file_uuid': file_uuid,
    #             'message': 'Document processing started'
    #         })
            
    #         # Update MongoDB status
    #         await self.update_document_status(file_uuid, {
    #             "status": "processing",
    #             "start_time": datetime.now(),
    #             "filename": filename,
    #             "project_id": project_id,
    #             "tenant_id": tenant_id,
    #             "user_id": user_id,
    #             "user_name": user_name,
    #             "session_id": session_id,
    #             "original_s3_path" :  original_s3_path,
    #             "file_size": file_size,
    #             "file_type": file_type or "application/pdf",
    #             "file_extension": file_extension or "",
    #             "progress": {
    #                 "percent": 0,
    #                 "is_active": True
    #             }
    #         })
            
    #         # Release any existing ProjDefn instance first
    #         try:
    #             ProjDefn.releaseInstance("default")
    #         except Exception as e:
    #             print(f"Error releasing ProjDefn instance: {str(e)}")
    #             pass
            
    #         # Initialize project definition handler
    #         projdefn = self.initialize_projdefn(
    #             base_path="/tmp/doc_ingestion/dir",
    #             current_user=user_id,
    #             session_id=session_id,
    #             tenant_id=tenant_id,
    #             project_id=project_id
    #         )
            
    #         # Reset ProjDefn counters
    #         projdefn.current_criteria_index = 0
    #         projdefn.total_criteria_count = len(projdefn.criteria)
    #         projdefn.criteria_start_time = time.time()
    #         projdefn.criteria_processing_times = []
            
    #         # Set up progress tracking with improved callback
    #         async def on_criteria_processed(current_index, total_count, criteria_key, doc_title):
    #             # Calculate progress percentage
    #             progress = (current_index / total_count) * 100
                
    #             # Get metrics from projdefn
    #             metrics = projdefn.calculate_progress_metrics()
                
    #             # Send detailed update via WebSocket
    #             self.ws_client.send_message("document_processing", {
    #                 'status': 'processing',
    #                 'progress': progress,
    #                 'file_uuid': file_uuid,
    #                 'current_criteria': criteria_key,
    #                 'document_title': doc_title,
    #                 'metrics': metrics,
    #                 'message': f'Processing criteria {current_index}/{total_count}: {criteria_key}, estimated time remaining: {metrics["estimated_remaining_time"]} seconds'
    #             })
                
    #             await self.update_document_status(file_uuid, {
    #                 "status": "processing",
    #                 "progress": {
    #                     "percent": metrics["progress_percent"],
    #                     "current_criteria": metrics["criteria_processed"],
    #                     "total_criteria": metrics["total_criteria"],
    #                     "elapsed_time": metrics["elapsed_time"],
    #                     "is_active": True
    #                 },
    #             })
                
    #             # Update instance variables for tracking
    #             self.criteria_processed_count = metrics["criteria_processed"]
    #             self.total_criteria_count = metrics["total_criteria"]
    #             self.percent = metrics["progress_percent"]
    #             self.elapsed_time = metrics["elapsed_time"]
    #             self.estimated_time = metrics["estimated_remaining_time"]
                
    #             print(f"Processing criteria {current_index}/{total_count}: {criteria_key} - {progress:.1f}% complete")
    #             print(f"Estimated remaining time: {metrics['estimated_remaining_time']} seconds")
                
    #             # Check if processing is complete (when current_index equals total_count)
    #             if current_index == total_count:
    #                 bucket_name = get_tenant_bucket(tenant_id)
    #                 prefix = f"extracted-docs-{tenant_id}/project-{project_id}/digested-files/doc__{filename}.json"
                    
    #                 # Send completion message
    #                 self.ws_client.send_message("document_processing", {
    #                     'status': 'completed',
    #                     'progress': 100,
    #                     'file_uuid': file_uuid,
    #                     'message': 'Document processing completed',
    #                     's3_location': f"{bucket_name}/{prefix}"
    #                 })
                    
    #                 # Update database with completion status
    #                 await self.update_document_status(file_uuid, {
    #                     "status": "completed",
    #                     "progress": {
    #                         'progress': 100,
    #                         "percent": self.percent,
    #                         "current_criteria": self.criteria_processed_count,
    #                         "total_criteria": self.total_criteria_count,
    #                         "elapsed_time": self.elapsed_time,
    #                         "estimation_time": self.estimated_time,
    #                     },
    #                     "end_time": datetime.now(),
    #                     "s3_location": f"{bucket_name}/{prefix}",
    #                 })
                    
    #                 # Reset counters after completion
    #                 self.criteria_processed_count = 0
    #                 self.total_criteria_count = 0
            
    #         # Update the completion check function
    #         def is_criteria_processing_complete():
    #             print("self.criteria_processed_count", self.criteria_processed_count)
    #             print("self.total_criteria_count", self.total_criteria_count)
    #             if self.criteria_processed_count == 0 and self.total_criteria_count == 0:
    #                 return False
    #             return self.criteria_processed_count >= self.total_criteria_count and self.total_criteria_count > 0
            
    #         projdefn.on_criteria_processed = on_criteria_processed
            
    #         # Add document to ingest queue
    #         projdefn.addToIngestQueue(filename, temp_file_path, isS3=True)
            
    #         # Set up S3 path info
    #         bucket_name = get_tenant_bucket(tenant_id)
    #         prefix = f"extracted-docs-{tenant_id}/project-{project_id}/digested-files/doc__{filename}.json"
            
            
    #         # Wait for processing to complete with timeout
    #         process_timeout = 3600
    #         start_time = time.time()
    #         processing_complete = False
            
    #         while not processing_complete and (time.time() - start_time) < process_timeout:
    #             try:
    #                 # Check if criteria processing is complete
    #                 if is_criteria_processing_complete():
    #                     processing_complete = True
    #                     bucket_name = get_tenant_bucket(tenant_id)
    #                     prefix = f"extracted-docs-{tenant_id}/project-{project_id}/digested-files/doc__{filename}.json"
                        
    #                     # Send completion message if not already sent by the callback
    #                     self.ws_client.send_message("document_processing", {
    #                         'status': 'completed',
    #                         'progress': 100,
    #                         'file_uuid': file_uuid,
    #                         'message': 'Document processing completed',
    #                         's3_location': f"{bucket_name}/{prefix}"
    #                     })
                        
    #                     # Update database with completion status
    #                     await self.update_document_status(file_uuid, {
    #                         "status": "completed",
    #                         "progress": {
    #                             'progress': 100,
    #                             "percent": self.percent,
    #                             "current_criteria": self.criteria_processed_count,
    #                             "total_criteria": self.total_criteria_count,
    #                             "elapsed_time": self.elapsed_time,
    #                             "estimation_time": self.estimated_time,
    #                         },
    #                         "end_time": datetime.now(),
    #                         "s3_location": f"{bucket_name}/{prefix}",
    #                     })
                        
    #                     # Reset counters after completion
    #                     self.criteria_processed_count = 0
    #                     self.total_criteria_count = 0
    #                     return
    #                 else:
    #                     # Not complete yet, continue waiting and updating progress
    #                     progress_metrics = projdefn.calculate_progress_metrics()
    #                     print("progress metrics", progress_metrics)
    #                     progress_value = progress_metrics["progress_percent"]
                    
    #                     # Update progress via WebSocket
    #                     self.ws_client.send_message("document_processing", {
    #                         'status': 'processing',
    #                         'progress': progress_value,
    #                         'file_uuid': file_uuid,
    #                         'metrics': progress_metrics,
    #                         'message': f'Processing document, {progress_value:.1f}% complete'
    #                     })
                        
    #                     # Store the progress metrics for later use
    #                     self.criteria_processed_count = progress_metrics["criteria_processed"]
    #                     self.total_criteria_count = progress_metrics["total_criteria"]
    #                     self.percent = progress_metrics["progress_percent"]
    #                     self.elapsed_time = progress_metrics["elapsed_time"]
    #                     self.estimated_time = progress_metrics["estimated_remaining_time"]

    #                     # Update database
    #                     await self.update_document_status(file_uuid, {
    #                         "status": "processing",
    #                         "progress": {
    #                             "percent": progress_metrics["progress_percent"],
    #                             "current_criteria": progress_metrics["criteria_processed"],
    #                             "total_criteria": progress_metrics["total_criteria"],
    #                             "elapsed_time": progress_metrics["elapsed_time"],
    #                             "estimated_time": progress_metrics["estimated_remaining_time"],
    #                             "is_active": True
    #                         },
    #                     })
                    
    #                 # Add a small delay before the next check
    #                 await asyncio.sleep(2)
                        
    #             except Exception as e:
    #                 print(f"Document exception occurred: {str(e)}")
    #                 await asyncio.sleep(1)  # Add delay to avoid tight loop on errors

    #     except Exception as e:
    #         # Handle errors
    #         print(f"Document processing failed: {str(e)}")
    #         self.ws_client.send_message("document_processing", {
    #             'status': 'failed',
    #             'file_uuid': file_uuid,
    #             'message': f'Error processing document: {str(e)}'
    #         })
            
    #         await self.update_document_status(file_uuid, {
    #             "status": "failed",
    #             "end_time": datetime.now(),
    #             "error": str(e),
    #             "error_details": str(e)
    #         })
        
    #     finally:
    #         # Clean up
    #         if 'projdefn' in locals():
    #             ProjDefn.releaseInstance("default")
    
    
    async def process_document(self, temp_file_path, file_uuid, session_id, filename, 
                          project_id, tenant_id, user_id, user_name, original_s3_path=None, file_size=0, file_type=None, file_extension=None):
        """Process a document and communicate progress via WebSocket"""
            
        # Create WebSocket client
        self.ws_client = WebSocketClient(file_uuid, settings.WEBSOCKET_URI)
        self.ws_client.connect()  # Ensure connection is attempted
        
        try:
            # Reset counters for this new document
            self.criteria_processed_count = 0
            self.total_criteria_count = 0
            self.percent = 0
            self.elapsed_time = 0
            self.estimated_time = 0
            
            print(f"🚀 Starting document processing for: {filename} (UUID: {file_uuid})")
            
            # Send initial status
            self.ws_client.send_message("document_processing", {
                'status': 'processing',
                'progress': 0,
                'file_uuid': file_uuid,
                'message': 'Document processing started'
            })
            
            # Update MongoDB status
            await self.update_document_status(file_uuid, {
                "status": "processing",
                "start_time": datetime.now(),
                "filename": filename,
                "project_id": project_id,
                "tenant_id": tenant_id,
                "user_id": user_id,
                "user_name": user_name,
                "session_id": session_id,
                "original_s3_path": original_s3_path,
                "file_size": file_size,
                "file_type": file_type or "application/pdf",
                "file_extension": file_extension or "",
                "progress": {
                    "percent": 0,
                    "is_active": True
                }
            })
            print(f"📝 Updated MongoDB with initial status for {filename}")
            
            # Release any existing ProjDefn instance first
            try:
                print(f"🔄 Releasing any existing ProjDefn instance")
                ProjDefn.releaseInstance("default")
            except Exception as e:
                print(f"⚠️ Error releasing ProjDefn instance: {str(e)}")
                pass
            
            # Initialize project definition handler
            print(f"🔧 Initializing project definition handler for {filename}")
            projdefn = self.initialize_projdefn(
                base_path="/tmp/doc_ingestion/dir",
                current_user=user_id,
                session_id=session_id,
                tenant_id=tenant_id,
                project_id=project_id
            )
            
            # Reset ProjDefn counters
            projdefn.current_criteria_index = 0
            projdefn.total_criteria_count = len(projdefn.criteria)
            projdefn.criteria_start_time = time.time()
            projdefn.criteria_processing_times = []
            print(f"🔢 Reset ProjDefn counters for {filename}, total criteria: {projdefn.total_criteria_count}")

            # Update the completion check function
            def is_criteria_processing_complete():
                print(f"🔢 Checking completion status - processed: {self.criteria_processed_count}, total: {self.total_criteria_count}")
                if self.criteria_processed_count == 0 and self.total_criteria_count == 0:
                    return False
                return self.criteria_processed_count >= self.total_criteria_count and self.total_criteria_count > 0
            
            # Add document to ingest queue
            print(f"📋 Adding document to ingest queue: {filename}")
            docspec = ProjDefnDocSpecifier(temp_file_path, filename, file_uuid)
            projdefn.addToIngestQueueWithSpec(docspec)
            
            # Set up S3 path info
            bucket_name = get_tenant_bucket(tenant_id)
            prefix = f"extracted-docs-{tenant_id}/project-{project_id}/digested-files/doc__{filename}.json"
            
            # Wait for processing to complete with timeout
            process_timeout = 3600
            start_time = time.time()
            processing_complete = False
            
            print(f"⏳ Waiting for document processing to complete (timeout: {process_timeout}s)")
            while not processing_complete and (time.time() - start_time) < process_timeout:
                try:
                    # Check if criteria processing is complete
                    if is_criteria_processing_complete():
                        processing_complete = True
                        print(f"🎉 Document processing completed for {filename}!")
                        bucket_name = get_tenant_bucket(tenant_id)
                        prefix = f"extracted-docs-{tenant_id}/project-{project_id}/digested-files/doc__{filename}.json"
                        
                        # Send completion message if not already sent by the callback
                        self.ws_client.send_message("document_processing", {
                            'status': 'completed',
                            'progress': 100,
                            'file_uuid': file_uuid,
                            'message': 'Document processing completed',
                            's3_location': f"{bucket_name}/{prefix}"
                        })
                        
                        # Update database with completion status
                        await self.update_document_status(file_uuid, {
                            "status": "completed",
                            "progress": {
                                'progress': 100,
                                "percent": self.percent,
                                "current_criteria": self.criteria_processed_count,
                                "total_criteria": self.total_criteria_count,
                                "elapsed_time": self.elapsed_time,
                                "estimation_time": self.estimated_time,
                            },
                            "end_time": datetime.now(),
                            "s3_location": f"{bucket_name}/{prefix}",
                        })
                        
                        # Reset counters after completion
                        self.criteria_processed_count = 0
                        self.total_criteria_count = 0
                        return
                    else:
                        # Not complete yet, continue waiting and updating progress
                        progress_metrics = projdefn.reporter.calculate_progress_metrics()
                        progress_value = progress_metrics["progress_percent"]
                        
                        # Periodic update (not on every loop iteration to reduce log noise)
                        elapsed = time.time() - start_time
                        if int(elapsed) % 10 == 0:  # Log every 10 seconds
                            print(f"⏱️ Processing in progress: {progress_value:.1f}% complete, elapsed: {elapsed:.1f}s")
                        
                        # Update progress via WebSocket
                        self.ws_client.send_message("document_processing", {
                            'status': 'processing',
                            'progress': progress_value,
                            'file_uuid': file_uuid,
                            'metrics': progress_metrics,
                            'message': f'Processing document, {progress_value:.1f}% complete'
                        })
                        
                        # Store the progress metrics for later use
                        self.criteria_processed_count = progress_metrics["processed_operations"]
                        self.total_criteria_count = progress_metrics["total_operations"]
                        self.percent = progress_metrics["progress_percent"]
                        self.elapsed_time = progress_metrics["elapsed_time"]
                        self.estimated_time = progress_metrics["estimated_remaining_time"]

                        # Update database
                        await self.update_document_status(file_uuid, {
                            "status": "processing",
                            "progress": {
                                "percent": progress_metrics["progress_percent"],
                                "current_criteria": progress_metrics["criteria_processed"],
                                "total_criteria": progress_metrics["total_criteria"],
                                "elapsed_time": progress_metrics["elapsed_time"],
                                "estimated_time": progress_metrics["estimated_remaining_time"],
                                "is_active": True
                            },
                        })
                    
                    # Add a small delay before the next check
                    await asyncio.sleep(2)
                        
                except Exception as e:
                    print(f"❌ Document exception occurred: {str(e)}")
                    await asyncio.sleep(1)  # Add delay to avoid tight loop on errors

        except Exception as e:
            # Handle errors
            print(f"❌ Document processing failed: {str(e)}")
            self.ws_client.send_message("document_processing", {
                'status': 'failed',
                'file_uuid': file_uuid,
                'message': f'Error processing document: {str(e)}'
            })
            
            await self.update_document_status(file_uuid, {
                "status": "failed",
                "end_time": datetime.now(),
                "error": str(e),
                "error_details": str(e)
            })
        
        finally:
            # Clean up
            if 'projdefn' in locals():
                print(f"🧹 Cleaning up ProjDefn instance for {filename}")
                ProjDefn.releaseInstance(f"default_{session_id}")
            print(f"🏁 Document processing function complete for {filename}")
        
    def initialize_projdefn(self, base_path, current_user, session_id=None, tenant_id=None, project_id=None):
        """Initialize the project definition handler"""
        reporter = Reporter()
        helper = ProjDefn_Helper( reporter= reporter,
                                base_path= base_path,
                                session_id= session_id,
                                tenant_id= tenant_id,
                                project_id= project_id
                                )
        configuration = {
            "base_path": base_path,
            "model": "gpt-4o-mini",
            "timeout": 60,
            "chunk_size": 64*1024,
            "cost_tracer": None,
            "reporter": reporter,
            "helpers": helper
        }
        
        # Get or create ProjDefn instance with session
        projdefn = ProjDefn.getInstance(configuration, f"default_{session_id}")
        projdefn.start()
        return projdefn
    
    async def update_document_status(self, file_uuid: str, status_update: dict):
        """Update the document status in the ingested_documents collection"""
        # Get MongoDB handler
        doc_handler = get_mongo_db(
            db_name=settings.MONGO_DB_NAME,
            collection_name='ingested_documents'
        )
        
        # Create the processing_status update
        processing_status = {
            "status": status_update.get("status", "processing"),
            "updated_at": datetime.now()
        }
        
        # Add additional fields if they exist
        if "error" in status_update:
            processing_status["error"] = status_update["error"]
        if "error_details" in status_update:
            processing_status["error_details"] = status_update["error_details"]
        if "progress" in status_update:
            processing_status["progress"] = status_update["progress"]
        if "s3_location" in status_update:
            processing_status["s3_location"] = status_update["s3_location"]
        if "original_s3_path" in status_update:
            processing_status["original_s3_path"] = status_update["original_s3_path"]
        if "end_time" in status_update:
            processing_status["end_time"] = status_update["end_time"]
        if "start_time" in status_update:
            processing_status["start_time"] = status_update["start_time"]

        # First try to find the document
        existing_doc = await doc_handler.get_one(
            {"document_record.file_uuid": file_uuid},
            db=doc_handler.db
        )

        if existing_doc:
            # Update only processing status for existing document
            update_data = {"processing_status": processing_status}
            result = await doc_handler.update_one(
                {"document_record.file_uuid": file_uuid},
                update_data,
                db=doc_handler.db
            )
        else:
            # Create complete document for new entry
            document_record = {
                "project_id": status_update.get("project_id"),
                "file_name": status_update.get("filename", "Unknown"),
                "file_uuid": file_uuid,
                "file_type": status_update.get("file_type", "application/pdf"),
                "file_extension": status_update.get("file_extension", ""),
                "file_size": status_update.get("file_size", 0),
                "uploaded_by": {
                    "user_id": status_update.get("user_id"),
                    "user_name": status_update.get("user_name", "Unknown")
                },
                "tenant_id": status_update.get("tenant_id"),
                "session_id": status_update.get("session_id"),
                "created_at": datetime.now()
            }

            # Add original S3 path if available
            if "original_s3_path" in status_update:
                document_record["original_s3_path"] = status_update["original_s3_path"]

            new_document = {
                "document_record": document_record,
                "processing_status": processing_status
            }

            # Insert new document
            result = await doc_handler.insert(new_document, db=doc_handler.db)