import os
from redis import Redis
from redis.cluster import Redis<PERSON>luster
import pickle
from app.core.Settings import Settings

settings = Settings()

redis_host = settings.REDIS_HOST
redis_pass = settings.REDIS_PASSWORD

local_debug = os.getenv("LOCAL_DEBUG", "false").lower() == "true"

# Global Redis Cluster connection
_redis_cluster_client = None

def get_redis_client():
    """Get or initialize a Redis client (local or cluster based on debug flag)"""
    global _redis_cluster_client

    if _redis_cluster_client is None:
        try:
            if local_debug:
                print("Connecting to local Redis instance...")
                _redis_cluster_client = Redis(
                    host=redis_host,
                    port=6379,
                    decode_responses=False,
                    password=redis_pass
                )
            else:
                print("Connecting to Redis Cluster...")
                _redis_cluster_client = RedisCluster(
                    host=redis_host,
                    port=6379,
                    password=redis_pass,
                    decode_responses=False,
                    ssl=True,
                    ssl_cert_reqs=None
                )
        except Exception as e:
            print(f"Error initializing Redis client: {e}")
            raise

    return _redis_cluster_client

def add_redis_support_to_knowledge(knowledge_instance, redis_config=None):
    """Add Redis support to an existing Knowledge instance"""
    import pickle
    
    if redis_config is None:
        instance_id = str(knowledge_instance.id).split('-')[1] if '-' in str(knowledge_instance.id) else str(knowledge_instance.id)
        
        redis_config = {
            'host': redis_host,
            'port': 6379,
            'password': redis_pass,
            'prefix': f'knowledge:{instance_id}:'
        }
        
        print("Redis configuration:", redis_config)
    
    # Add Redis properties
    knowledge_instance.redis_prefix = redis_config['prefix']
    
    # Use the global Redis client instead of creating a new one
    knowledge_instance.redis_client = get_redis_client()
    
    # Add Redis methods
    def _redis_key(self, key_type, key=None):
        if key:
            return f"{self.redis_prefix}{key_type}:{key}"
        return f"{self.redis_prefix}{key_type}"
    
    def save_to_redis(self):
        with self._lock:
            try:
                self.logger.info("Saving knowledge to Redis...")
                
                # Save metadata and keys
                self.redis_client.set(self._redis_key('keys'), pickle.dumps(self.keys))
                self.redis_client.set(self._redis_key('key_descriptions'), pickle.dumps(self.key_descriptions))
                
                # Save top-level lists
                self.redis_client.set(self._redis_key('other_files'), pickle.dumps(self.other_files))
                self.redis_client.set(self._redis_key('source_files'), pickle.dumps(self.source_files))
                self.redis_client.set(self._redis_key('document_files'), pickle.dumps(self.document_files))
                self.redis_client.set(self._redis_key('source_code_bases'), pickle.dumps(self.source_code_bases))
                self.redis_client.set(self._redis_key('source_languages'), pickle.dumps(self.source_languages))
                self.redis_client.set(self._redis_key('search_terms'), pickle.dumps(self.search_terms))
                
                # Save task information
                self.redis_client.set(self._redis_key('task_keys'), pickle.dumps(self.task_keys))
                self.redis_client.set(self._redis_key('task_knowledge'), pickle.dumps(self.task_knowledge))
                
                # Save descriptions cache
                self.redis_client.set(self._redis_key('descriptions_cache'), pickle.dumps(self.descriptions_cache))
                
                # In cluster mode, we can't use pipeline in the same way
                # We'll use individual SET commands instead of a pipeline
                for filename, file_info in self.source_file_info.items():
                    self.redis_client.set(self._redis_key('file_info', filename), pickle.dumps(file_info))
                
                # Save document information - individual sets instead of pipeline
                for filename, doc_info in self.document_info.items():
                    self.redis_client.set(self._redis_key('doc_info', filename), pickle.dumps(doc_info))
                
                self.logger.info("Knowledge saved to Redis successfully")
                return True
            except Exception as e:
                self.logger.error(f"Error saving to Redis: {str(e)}")
                return False
    
    # Attach methods to the instance
    knowledge_instance._redis_key = _redis_key.__get__(knowledge_instance)
    knowledge_instance.save_to_redis = save_to_redis.__get__(knowledge_instance)
    
    # Log completion
    knowledge_instance.logger.info(f"Redis support added with prefix {knowledge_instance.redis_prefix}")
    
    return knowledge_instance


def getRedisKnowledge(redis_config=None, id="default", verbose=False):
    """
    Retrieve knowledge data directly from Redis without using the Knowledge class.
    Returns False if no keys are found.
    
    Args:
        redis_config: Dictionary with Redis connection parameters (optional)
        id: String or list of strings for Knowledge instance IDs (default: "default")
        verbose: Whether to print progress information (default: False)
        
    Returns:
        Dictionary containing combined knowledge from all specified IDs, or False if no keys found
    """
    import pickle
    from copy import deepcopy

    # Initialize empty combined knowledge structure
    combined_knowledge = {
        'keys': [],
        'key_descriptions': {},  # Changed to dict for better merging
        'other_files': [],
        'source_files': [],
        'document_files': [],
        'source_code_bases': [],
        'source_languages': [],
        'search_terms': [],
        'source_file_info': {},
        'document_info': {},
        'task_keys': {},
        'task_knowledge': {},
        'descriptions_cache': {},
        'errors': []
    }

    # Store original data for debugging
    original_data = {}

    # Flag to track if any data was found
    data_found = False
    
    # Use the global Redis client
    redis_client = get_redis_client()

    # Convert single ID to list for consistent processing
    id_list = [id] if isinstance(id, str) else id

    for current_id in id_list:
        if verbose:
            print(f"\nProcessing knowledge for ID: {current_id}")

        # Configure Redis prefix for current ID
        if redis_config is None:
            prefix = f'knowledge:{current_id}:'
        else:
            prefix = f'knowledge:{current_id}:'
            
        if verbose:
            print(f"Using prefix: {prefix}")
            
        # Direct key mappings for simple lists and dictionaries
        direct_keys = [
            'keys',
            'key_descriptions',
            'other_files',
            'source_files',
            'document_files',
            'source_code_bases',
            'source_languages',
            'search_terms',
            'task_keys',
            'task_knowledge',
            'descriptions_cache'
        ]

        # Retrieve direct keys
        for key_name in direct_keys:
            try:
                data = redis_client.get(f"{prefix}{key_name}")
                if data:
                    data_found = True
                    try:
                        current_data = pickle.loads(data)
                        
                        # Store original data for debugging
                        if current_id not in original_data:
                            original_data[current_id] = {}
                        original_data[current_id][key_name] = current_data
                        
                        # Combine data based on type and key name
                        if key_name == 'keys':
                            # For keys, maintain uniqueness but preserve all keys across builds
                            for key in current_data:
                                if key not in combined_knowledge[key_name]:
                                    combined_knowledge[key_name].append(key)
                        
                        elif key_name == 'source_code_bases':
                            # For source_code_bases, maintain uniqueness but preserve all values
                            if isinstance(current_data, list):
                                for value in current_data:
                                    if value not in combined_knowledge[key_name]:
                                        combined_knowledge[key_name].append(value)
                            else:
                                # If for some reason it's not a list, handle it
                                if current_data not in combined_knowledge[key_name]:
                                    combined_knowledge[key_name].append(current_data)
                            
                            # Debug the source_code_bases merging
                            if verbose:
                                print(f"  From {current_id}, source_code_bases: {current_data}")
                                print(f"  Current combined source_code_bases: {combined_knowledge[key_name]}")
                        
                        elif key_name == 'key_descriptions':
                            # Handle key_descriptions specially - convert to dict if it's a list
                            if isinstance(current_data, list):
                                # Convert list-based key_descriptions to dict for easier merging
                                desc_dict = {}
                                for i, desc in enumerate(current_data):
                                    # If we have corresponding keys, use them as dict keys
                                    if 'keys' in original_data[current_id] and i < len(original_data[current_id]['keys']):
                                        key = original_data[current_id]['keys'][i]
                                        desc_dict[key] = desc
                                    else:
                                        # Fallback if keys aren't available or indices don't match
                                        desc_dict[f"key_{current_id}_{i}"] = desc
                                combined_knowledge[key_name].update(desc_dict)
                            elif isinstance(current_data, dict):
                                combined_knowledge[key_name].update(current_data)
                        
                        elif isinstance(current_data, list):
                            # For most lists, extend and deduplicate
                            combined_knowledge[key_name].extend(current_data)
                            # Remove duplicates while preserving order
                            combined_knowledge[key_name] = list(dict.fromkeys(combined_knowledge[key_name]))
                        
                        elif isinstance(current_data, dict):
                            combined_knowledge[key_name].update(current_data)
                        
                        if verbose:
                            if isinstance(current_data, list):
                                print(f"Retrieved {key_name}: {len(current_data)} items")
                                if (key_name == 'keys' or key_name == 'source_code_bases') and len(current_data) < 10:
                                    print(f"  {key_name} content: {current_data}")
                            elif isinstance(current_data, dict):
                                print(f"Retrieved {key_name}: {len(current_data)} entries")
                    except Exception as e:
                        error_msg = f"Could not unpickle data for {key_name} (ID: {current_id}): {str(e)}"
                        combined_knowledge['errors'].append(error_msg)
                        if verbose:
                            print(error_msg)
            except Exception as e:
                error_msg = f"Error retrieving {key_name} (ID: {current_id}): {str(e)}"
                combined_knowledge['errors'].append(error_msg)
                if verbose:
                    print(error_msg)

        # Retrieve all keys with this prefix using scan_iter
        all_keys = []
        try:
            for key in redis_client.scan_iter(match=f"{prefix}*", count=100):
                all_keys.append(key)
                if len(all_keys) % 1000 == 0 and verbose:
                    print(f"Found {len(all_keys)} keys so far...")
        except Exception as e:
            error_msg = f"Error during scan for ID {current_id}: {str(e)}"
            combined_knowledge['errors'].append(error_msg)
            if verbose:
                print(error_msg)

        if all_keys:
            data_found = True

        if verbose:
            print(f"Found {len(all_keys)} total keys in Redis for ID {current_id}")

        # Process each key for file_info and doc_info
        for key in all_keys:
            try:
                key_str = key.decode('utf-8') if isinstance(key, bytes) else key
                key_type = key_str[len(prefix):]
                
                if key_type in direct_keys:
                    continue
                    
                data = redis_client.get(key)
                if not data:
                    continue

                if key_type.startswith('file_info:'):
                    filename = key_type[len('file_info:'):]
                    try:
                        file_info = pickle.loads(data)
                        combined_knowledge['source_file_info'][filename] = file_info
                    except Exception as e:
                        error_msg = f"Could not unpickle file_info for {filename} (ID: {current_id}): {str(e)}"
                        combined_knowledge['errors'].append(error_msg)
                        if verbose:
                            print(error_msg)
                
                elif key_type.startswith('doc_info:'):
                    filename = key_type[len('doc_info:'):]
                    try:
                        doc_info = pickle.loads(data)
                        combined_knowledge['document_info'][filename] = doc_info
                    except Exception as e:
                        error_msg = f"Could not unpickle doc_info for {filename} (ID: {current_id}): {str(e)}"
                        combined_knowledge['errors'].append(error_msg)
                        if verbose:
                            print(error_msg)
            
            except Exception as e:
                error_msg = f"Error processing key {key} (ID: {current_id}): {str(e)}"
                combined_knowledge['errors'].append(error_msg)
                if verbose:
                    print(error_msg)

    # If no data was found for any ID, return False
    if not data_found:
        if verbose:
            print("\nNo knowledge data found in Redis for any of the provided IDs.")
        return False

    # Convert key_descriptions back to list format if needed
    if isinstance(combined_knowledge['key_descriptions'], dict):
        # Create a list of descriptions that aligns with the combined keys list
        key_desc_list = []
        for key in combined_knowledge['keys']:
            if key in combined_knowledge['key_descriptions']:
                key_desc_list.append(combined_knowledge['key_descriptions'][key])
            else:
                key_desc_list.append("")  # Empty description for keys without one
        combined_knowledge['key_descriptions'] = key_desc_list

    if verbose:
        # Print summary information for combined knowledge
        print("\nCombined Knowledge Summary:")
        print(f"- Keys: {len(combined_knowledge['keys'])}")
        if len(combined_knowledge['keys']) < 10:
            print(f"  Combined keys: {combined_knowledge['keys']}")
        
        print(f"- Key Descriptions: {len(combined_knowledge['key_descriptions'])}")
        print(f"- Source Files: {len(combined_knowledge['source_files'])}")
        print(f"- Source File Info: {len(combined_knowledge['source_file_info'])}")
        print(f"- Document Files: {len(combined_knowledge['document_files'])}")
        print(f"- Document Info: {len(combined_knowledge['document_info'])}")
        print(f"- Other Files: {len(combined_knowledge['other_files'])}")
        print(f"- Source Code Bases: {combined_knowledge['source_code_bases']}")
        
        print(f"- Source Languages: {len(combined_knowledge['source_languages'])}")
        print(f"  Combined source languages: {combined_knowledge['source_languages']}")
        
        print(f"- Search Terms: {len(combined_knowledge['search_terms'])}")
        print(f"- Task Keys: {len(combined_knowledge['task_keys'])}")
        print(f"- Task Knowledge: {len(combined_knowledge['task_knowledge'])}")
        print(f"- Descriptions Cache: {len(combined_knowledge['descriptions_cache'])}")
        
        # Report any errors
        if combined_knowledge['errors']:
            print(f"- Errors: {len(combined_knowledge['errors'])} errors encountered")

    return combined_knowledge

def get_all_knowledge_from_redis(redis_config, verbose=False):
    """
    Retrieve all knowledge from Redis and return it as a structured dictionary.
    
    Args:
        redis_config: Dictionary with Redis connection parameters
        verbose: Whether to print progress information
    
    Returns:
        Dictionary containing all retrieved knowledge
    """
    # Use the global Redis client
    redis_client = get_redis_client()
    
    prefix = redis_config['prefix']
    
    if verbose:
        print(f"Using prefix: {prefix}")
    
    # Dictionary to store all knowledge
    all_knowledge = {}
    
    # Retrieve all keys with this prefix using scan_iter for cluster compatibility
    all_keys = []
    try:
        for key in redis_client.scan_iter(match=f"{prefix}*", count=100):
            all_keys.append(key)
            if len(all_keys) % 1000 == 0 and verbose:
                print(f"Found {len(all_keys)} keys so far...")
    except Exception as e:
        if verbose:
            print(f"Error during scan: {str(e)}")
    
    if verbose:
        print(f"Found {len(all_keys)} keys in Redis")
    
    # Process each key
    for key in all_keys:
        try:
            key_str = key.decode('utf-8') if isinstance(key, bytes) else key
            # Extract the key type (part after prefix)
            key_type = key_str[len(prefix):]
            
            # Get the data
            data = redis_client.get(key)
            
            if key_type.startswith('file_info:'):
                # This is file info, organize by filename
                filename = key_type[len('file_info:'):]
                if 'source_files' not in all_knowledge:
                    all_knowledge['source_files'] = {}
                try:
                    all_knowledge['source_files'][filename] = pickle.loads(data)
                except:
                    all_knowledge['source_files'][filename] = {'error': 'Could not unpickle data'}
            
            elif key_type.startswith('doc_info:'):
                # This is document info, organize by filename
                filename = key_type[len('doc_info:'):]
                if 'document_files' not in all_knowledge:
                    all_knowledge['document_files'] = {}
                try:
                    all_knowledge['document_files'][filename] = pickle.loads(data)
                except:
                    all_knowledge['document_files'][filename] = {'error': 'Could not unpickle data'}
            
            else:
                # General knowledge info
                try:
                    all_knowledge[key_type] = pickle.loads(data)
                except:
                    all_knowledge[key_type] = {'error': 'Could not unpickle data'}
        
        except Exception as e:
            if verbose:
                print(f"Error processing key {key}: {str(e)}")
    
    if verbose:
        # Print summary information
        print("\nKnowledge Summary:")
        
        if 'source_files' in all_knowledge and isinstance(all_knowledge.get('source_files'), list):
            print(f"- Source Files: {len(all_knowledge.get('source_files', []))}")
        
        if 'source_languages' in all_knowledge:
            print(f"- Source Languages: {all_knowledge.get('source_languages', [])}")
        
        if 'search_terms' in all_knowledge:
            terms = all_knowledge.get('search_terms', [])
            print(f"- Search Terms: {len(terms)} terms")
            if verbose and terms:
                print(f"  Example terms: {terms[:5]}")
        
        if 'task_keys' in all_knowledge:
            task_keys = all_knowledge.get('task_keys', {})
            print(f"- Task Keys: {len(task_keys)} keys")
    
    return all_knowledge


# Connection health check and reconnection function
def check_redis_connection():
    """
    Check if the Redis connection is healthy and reconnect if needed.
    Returns True if connection is working, False otherwise.
    """
    global _redis_cluster_client
    
    try:
        if _redis_cluster_client is None:
            # Initialize the connection if it doesn't exist
            _redis_cluster_client = get_redis_client()
            
        # Try a simple ping command to check connection
        _redis_cluster_client.ping()
        return True
        
    except Exception as e:
        print(f"Redis connection error: {e}")
        
        # Try to reconnect
        try:
            _redis_cluster_client = RedisCluster(
                host=redis_host,
                port=6379,
                password=redis_pass,
                decode_responses=False,
                ssl=True,
                ssl_cert_reqs=None
            )
            
            # Verify the new connection
            _redis_cluster_client.ping()
            print("Successfully reconnected to Redis")
            return True
            
        except Exception as reconnect_error:
            print(f"Failed to reconnect to Redis: {reconnect_error}")
            _redis_cluster_client = None
            return False