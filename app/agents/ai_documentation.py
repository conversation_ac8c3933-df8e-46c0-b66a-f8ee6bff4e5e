from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>
from datetime import datetime
import json
import asyncio
from app.agents.agent import Agent
from app.discussions.discussion_util import conduct_discussion
from app.connection.establish_db_connection import get_node_db
from app.telemetry.logger_config import get_logger, set_task_id
import os
from app.utils.datetime_utils import generate_timestamp
import logging

base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
documentation_mapping_path = os.path.join(base_dir, 'discussions', 'types', 'documentation_mapping.json')

class DocumentationAgent(Agent):
    """
    DocumentationAgent is responsible for creating and managing various types of documentation
    for a project, including PRD, SAD, and API documentation.
    
    This agent delegates the actual document creation and configuration to appropriate Discussion classes.
    """
    def __init__(self, name, node_id, node_type, root_node_type, discussion_type, logging_context, levels, semaphore, supervisor=None):
        super().__init__(name)
        self.supervisor = supervisor
        self.node_id = node_id
        self.node_type = node_type
        self.root_node_type = root_node_type
        self.discussion_type = discussion_type
        self.db = get_node_db()
        self.semaphore = semaphore
        self.levels = levels
        self.update_logger_agent = get_logger(__name__)
        self.project_id = None

        # Documentation sequence defines order
        self.doc_sequence = ["PRD", "SAD", "API"]
        self.current_doc_index = 0

        # Load documentation mapping
        try:
            with open(documentation_mapping_path, 'r') as f:
                self.documentation_mapping = json.load(f)
        except Exception as e:
            self.update_logger_agent.error(f"Error loading documentation mapping: {str(e)}")
            self.documentation_mapping = {}

        if logging_context:
            self.logging_context = logging_context
            self.mongo_handler = logging_context.get('mongo_handler')
            self.task_id = logging_context.get('task_id')
            set_task_id(self.task_id)
        else:
            self.logging_context = {}
            self.task_id = None
            self.mongo_handler = None
        
        self.queue = asyncio.Queue()

    async def process_work_item(self, work_item_dict):
        """
        Process a work item from the queue
        """
        try:
            self.update_logger_agent.info(f"Processing WorkItem: {work_item_dict.entry} for node {work_item_dict.node_id}")
            
            if work_item_dict.entry == "generate_documentation":
                await self.generate_documentation(work_item_dict.node_id, work_item_dict.node_type)
            elif work_item_dict.entry == "generate_prd":
                await self.generate_prd(work_item_dict.node_id)
            elif work_item_dict.entry == "generate_sad":
                await self.generate_sad(work_item_dict.node_id)
            elif work_item_dict.entry == "generate_api_docs":
                await self.generate_api_docs(work_item_dict.node_id)
            elif work_item_dict.entry == "update_section":
                await self.update_section(work_item_dict.node_id)
            else:
                self.update_logger_agent.warning(f"Task {work_item_dict.entry} not found for agent {self.name}")
                
            await self.supervisor.notify_complete(work_item_dict)
            
        except Exception as e:
            self.update_logger_agent.error(f"Error processing work item: {str(e)}", exc_info=True)
            await self.supervisor.notify_failure(work_item_dict, str(e))

    async def generate_documentation(self, node_id, node_type):
        """
        Main entry point to generate all documentation for a project
        """
        try:
            self.project_id = node_id
            self.node_id = node_id
            self.node_type = node_type
            
            self.update_logger_agent.info(f"Starting documentation generation for project: {node_id}")
            
            # Initialize documentation tracking
            await self.initialize_documentation_tracking()
            
            # Generate PRD first
            await self.generate_prd(node_id)
            
            # Generate SAD
            await self.generate_sad(node_id)
            
            # Generate API documentation
            await self.generate_api_docs(node_id)
            
            self.update_logger_agent.info(f"Documentation generation complete for project: {node_id}")
            
        except Exception as e:
            self.update_logger_agent.error(f"Error generating documentation for project {node_id}: {str(e)}", exc_info=True)
            raise

    async def initialize_documentation_tracking(self):
        """Initialize documentation tracking in MongoDB"""
        try:
            if not self.mongo_handler:
                return
                
            task_configuration = await self.mongo_handler.get_by_task_id(self.task_id, self.mongo_handler.db)
            
            if not task_configuration:
                task_configuration = {
                    'task_id': self.task_id,
                    'queue_messages': [],
                    'task_status': 'In Progress',
                    'configuration_status': {},
                    'progress': 0
                }
                await self.mongo_handler.insert(task_configuration, self.mongo_handler.db)
                await self.send_update(self.task_id)
                self.update_logger_agent.info(f"Created new task configuration for task {self.task_id}")
                
            self.queue_messages = task_configuration.get('queue_messages', [])
            self.configuration_status = task_configuration.get('configuration_status', {})
            
        except Exception as e:
            self.update_logger_agent.error(f"Error initializing documentation tracking: {str(e)}", exc_info=True)

    async def update_documentation_status(self, doc_type, status, node_id=None):
        """Update documentation status in MongoDB"""
        try:
            if not self.mongo_handler:
                return
                
            if not node_id:
                node_id = self.node_id
                
            node_key = str(node_id)
            
            if not self.configuration_status.get(node_key):
                node = await self.db.get_node_by_id(node_id)
                title = node['properties'].get('Title', 'Unknown') if node and 'properties' in node else 'Unknown'
                self.configuration_status[node_key] = {
                    'node_type': self.node_type,
                    'title': title
                }
                
            self.configuration_status[node_key][f"{doc_type}_status"] = status
            
            await self.mongo_handler.update_by_task_id(
                self.task_id,
                {'configuration_status': self.configuration_status}
            )
            
            await self.send_update(self.task_id)
            
        except Exception as e:
            self.update_logger_agent.error(f"Error updating documentation status: {str(e)}", exc_info=True)

    async def generate_prd(self, project_id):
        """Generate PRD documentation for a project"""
        try:
            self.update_logger_agent.info(f"Starting PRD generation for project: {project_id}")
            await self.update_documentation_status("PRD", "configuring", project_id)
            
            # Check if PRD already exists
            prd_root = await self.get_documentation_root(project_id, "PRD")
            
            if not prd_root:
                # Create PRD root if it doesn't exist
                prd_root = await self.create_documentation_root(project_id, "PRD")
                
            if not prd_root:
                raise Exception("Failed to create PRD documentation root")
                
            # Configure the PRD using DocumentationDiscussion
            await self.configure_documentation(prd_root['id'], "DocumentationRoot", "Project", "documentation_configuration")
            
            # Configure sections
            await self.configure_documentation_sections(prd_root['id'], "DocumentationRoot", "Project", "documentation_section_management")
            
            await self.update_documentation_status("PRD", "configured", project_id)
            self.update_logger_agent.info(f"PRD generation complete for project: {project_id}")
            
            return prd_root
            
        except Exception as e:
            await self.update_documentation_status("PRD", "failed", project_id)
            self.update_logger_agent.error(f"Error generating PRD for project {project_id}: {str(e)}", exc_info=True)
            raise

    async def generate_sad(self, project_id):
        """Generate SAD documentation for a project"""
        try:
            self.update_logger_agent.info(f"Starting SAD generation for project: {project_id}")
            await self.update_documentation_status("SAD", "configuring", project_id)
            
            # Check if SAD already exists
            sad_root = await self.get_documentation_root(project_id, "SAD")
            
            if not sad_root:
                # Create SAD root if it doesn't exist
                sad_root = await self.create_documentation_root(project_id, "SAD")
                
            if not sad_root:
                raise Exception("Failed to create SAD documentation root")
                
            # Configure the SAD using DocumentationDiscussion
            await self.configure_documentation(sad_root['id'], "DocumentationRoot", "Project", "documentation_configuration")
            
            # Configure sections
            await self.configure_documentation_sections(sad_root['id'], "DocumentationRoot", "Project", "documentation_section_management")
            
            await self.update_documentation_status("SAD", "configured", project_id)
            self.update_logger_agent.info(f"SAD generation complete for project: {project_id}")
            
            return sad_root
            
        except Exception as e:
            await self.update_documentation_status("SAD", "failed", project_id)
            self.update_logger_agent.error(f"Error generating SAD for project {project_id}: {str(e)}", exc_info=True)
            raise

    async def generate_api_docs(self, project_id):
        """Generate API documentation for a project"""
        try:
            self.update_logger_agent.info(f"Starting API documentation generation for project: {project_id}")
            await self.update_documentation_status("API", "configuring", project_id)
            
            # Check if API documentation already exists
            api_root = await self.get_documentation_root(project_id, "API")
            
            if not api_root:
                # Create API doc root if it doesn't exist
                api_root = await self.create_documentation_root(project_id, "API")
                
            if not api_root:
                raise Exception("Failed to create API documentation root")
            
            # Get all interfaces in the project
            interfaces = await self.db.get_child_nodes(project_id, "Interface")
            
            if not interfaces:
                self.update_logger_agent.warning(f"No interfaces found for project {project_id}. Skipping API documentation.")
                await self.update_documentation_status("API", "no_interfaces", project_id)
                return None
                
            self.update_logger_agent.info(f"Creating API documentation for {len(interfaces)} interfaces")
            
            # Create API documentation for each interface
            for interface in interfaces:
                try:
                    # Get context for the interface
                    interface_context = await self.get_interface_context(interface['id'])
                    
                    # Create or update API documentation for the interface
                    await self.create_or_update_api_documentation(api_root['id'], interface['id'], interface_context)
                except Exception as e:
                    self.update_logger_agent.error(f"Error creating API documentation for interface {interface['id']}: {str(e)}")
            
            await self.update_documentation_status("API", "configured", project_id)
            self.update_logger_agent.info(f"API documentation generation complete for project: {project_id}")
            
            return api_root
            
        except Exception as e:
            await self.update_documentation_status("API", "failed", project_id)
            self.update_logger_agent.error(f"Error generating API documentation for project {project_id}: {str(e)}", exc_info=True)
            raise

    async def get_documentation_root(self, project_id, doc_type):
        """Get or create a documentation root node for the given type"""
        try:
            # Find all documentation roots for the project
            doc_roots = await self.db.get_child_nodes(project_id, "DocumentationRoot")
            
            # Filter for the specific type
            for root in doc_roots or []:
                if 'properties' in root and root['properties'].get('DocumentationType') == doc_type:
                    return root
                    
            return None
            
        except Exception as e:
            self.update_logger_agent.error(f"Error getting documentation root for {doc_type}: {str(e)}", exc_info=True)
            return None

    async def create_documentation_root(self, project_id, doc_type):
        """Create a documentation root node for the given type"""
        try:
            # Get document type config from mapping
            doc_config = self.documentation_mapping.get(doc_type, {})
            
            if not doc_config:
                self.update_logger_agent.warning(f"No configuration found for document type {doc_type}")
                
            # Create documentation root node
            properties = {
                "Title": doc_config.get('title', f"{doc_type} Documentation"),
                "Description": doc_config.get('description', f"Documentation of type {doc_type}"),
                "DocumentationType": doc_type,
                "configuration_state": "not_configured",
                "CreatedAt": generate_timestamp()
            }
            
            doc_root = await self.db.create_node(["DocumentationRoot"], properties, project_id)
            self.update_logger_agent.info(f"Created new {doc_type} documentation root: {doc_root['id']}")
            
            return doc_root
            
        except Exception as e:
            self.update_logger_agent.error(f"Error creating documentation root for {doc_type}: {str(e)}", exc_info=True)
            return None

    async def configure_documentation(self, node_id, node_type, root_node_type, discussion_type):
        """Configure a documentation node using the appropriate discussion"""
        try:
            self.update_logger_agent.info(f"Configuring documentation node: {node_id}")
            
            # Conduct the documentation discussion
            result = await conduct_discussion(
                node_id=node_id,
                node_type=node_type,
                root_node_type=root_node_type,
                discussion_type=discussion_type,
                logging_context=self.logging_context,
                supervisor=self.supervisor
            )
            
            self.update_logger_agent.info(f"Documentation configuration complete for node {node_id}")
            return result
            
        except Exception as e:
            self.update_logger_agent.error(f"Error configuring documentation for node {node_id}: {str(e)}", exc_info=True)
            raise

    async def configure_documentation_sections(self, doc_root_id, node_type, root_node_type, discussion_type):
        """Configure sections for a documentation root"""
        try:
            self.update_logger_agent.info(f"Configuring documentation sections for: {doc_root_id}")
            
            # Conduct the section management discussion
            result = await conduct_discussion(
                node_id=doc_root_id,
                node_type=node_type,
                root_node_type=root_node_type,
                discussion_type=discussion_type,
                logging_context=self.logging_context,
                supervisor=self.supervisor
            )
            
            # After sections are created, configure each section
            sections = await self.db.get_child_nodes(doc_root_id, "Sub_Section") or []
            
            for section in sections:
                try:
                    # Skip sections that are already configured
                    if 'properties' in section and section['properties'].get('configuration_state') == 'configured':
                        continue
                        
                    self.update_logger_agent.info(f"Configuring section: {section['id']}")
                    
                    # Conduct discussion for each section
                    await conduct_discussion(
                        node_id=section['id'],
                        node_type="Sub_Section",
                        root_node_type=root_node_type,
                        discussion_type="documentation_section_management",
                        logging_context=self.logging_context,
                        supervisor=self.supervisor
                    )
                    
                except Exception as e:
                    self.update_logger_agent.error(f"Error configuring section {section['id']}: {str(e)}")
            
            self.update_logger_agent.info(f"Section configuration complete for documentation root {doc_root_id}")
            return result
            
        except Exception as e:
            self.update_logger_agent.error(f"Error configuring sections for doc root {doc_root_id}: {str(e)}", exc_info=True)
            raise

    async def get_interface_context(self, interface_id):
        """Get context for an interface, including endpoints, data models, etc."""
        try:
            interface = await self.db.get_node_by_id(interface_id)
            if not interface:
                return {}
                
            # Get all endpoints for the interface
            endpoints = await self.db.get_child_nodes(interface_id, "Endpoint") or []
            
            # Get all data models referenced by endpoints
            data_models = []
            for endpoint in endpoints:
                if 'properties' in endpoint and 'RequestModel' in endpoint['properties']:
                    model_id = endpoint['properties']['RequestModel']
                    model = await self.db.get_node_by_id(model_id)
                    if model:
                        data_models.append(model)
                        
                if 'properties' in endpoint and 'ResponseModel' in endpoint['properties']:
                    model_id = endpoint['properties']['ResponseModel']
                    model = await self.db.get_node_by_id(model_id)
                    if model:
                        data_models.append(model)
            
            return {
                'interface': interface,
                'endpoints': endpoints,
                'data_models': data_models
            }
            
        except Exception as e:
            self.update_logger_agent.error(f"Error getting interface context for {interface_id}: {str(e)}", exc_info=True)
            return {}

    async def create_or_update_api_documentation(self, api_root_id, interface_id, interface_context):
        """Create or update API documentation for an interface"""
        try:
            interface = interface_context.get('interface')
            if not interface:
                self.update_logger_agent.warning(f"Interface context missing interface data for {interface_id}")
                return
                
            # Check if documentation already exists for this interface
            existing_docs = await self.db.get_child_nodes(api_root_id, "Sub_Section") or []
            existing_doc = None
            
            for doc in existing_docs:
                if 'properties' in doc and doc['properties'].get('SourceNodeId') == interface_id:
                    existing_doc = doc
                    break
            
            if existing_doc:
                # Update existing documentation
                self.update_logger_agent.info(f"Updating API documentation for interface {interface_id}")
                await self.configure_documentation(existing_doc['id'], "Sub_Section", "Project", "documentation_section_management")
            else:
                # Create new documentation section for the interface
                self.update_logger_agent.info(f"Creating new API documentation for interface {interface_id}")
                
                # Get the next available order
                order = len(existing_docs) + 1
                
                # Create properties for the new section
                section_properties = {
                    "Title": interface['properties'].get('Title', 'API Documentation'),
                    "Description": interface['properties'].get('Description', 'API Documentation'),
                    "SectionType": "API",
                    "Order": order,
                    "SourceNodeId": interface_id,
                    "configuration_state": "not_configured",
                    "CreatedAt": generate_timestamp()
                }
                
                # Create the section
                section = await self.db.create_node(["Sub_Section"], section_properties, api_root_id)
                
                # Configure the new section
                await self.configure_documentation(section['id'], "Sub_Section", "Project", "documentation_section_management")
                
        except Exception as e:
            self.update_logger_agent.error(f"Error creating/updating API documentation for interface {interface_id}: {str(e)}", exc_info=True)

    async def update_section(self, section_id):
        """Update a specific documentation section"""
        try:
            self.update_logger_agent.info(f"Updating documentation section: {section_id}")
            
            # Conduct discussion for the section
            await conduct_discussion(
                node_id=section_id,
                node_type="Sub_Section",
                root_node_type="Project",
                discussion_type="documentation_section_management",
                logging_context=self.logging_context,
                supervisor=self.supervisor
            )
            
            self.update_logger_agent.info(f"Documentation section update complete for {section_id}")
            
        except Exception as e:
            self.update_logger_agent.error(f"Error updating documentation section {section_id}: {str(e)}", exc_info=True)
            raise

    async def run(self):
        """Run the agent, processing items from the queue"""
        while True:
            try:
                work_item = await self.queue.get()
                await self.process_work_item(work_item)
            except Exception as e:
                self.update_logger_agent.error(f"Error in run method: {str(e)}", exc_info=True)
            finally:
                self.queue.task_done()