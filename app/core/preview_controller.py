from datetime import datetime
from typing import Dict, Any, Callable
from app.core.code_generation import TaskExecutionAgent
from app.core.websocket.client import WebSocketClient
from app.utils.datetime_utils import generate_timestamp
from app.core.constants import TASKS_COLLECTION_NAME
from code_generation_core_agent.agents.preview.preview_manager import PreviewManager
from app.utils.project_utils import change_host
from app.models.preview_state_model import PreviewResponse
from app.core.Settings import settings
import os
import threading

def process_url(url) -> str:
    try:
        if os.environ.get("host"):
            print("Host is set in env", os.environ.get("host"))
            if "localhost" in url and not os.environ.get("LOCAL_DEBUG"):
                url = change_host(url, os.environ.get("host"), upgrade_to_https=True)

        if ":3000" in url and settings.STAGE not in ["dev", "develop", "qa","pre_prod"]:
            url = url.replace(":3000", ":3001")
        return url
    except Exception:
        return url

class PreviewController:
    def __init__(self, agent: TaskExecutionAgent, task_id: str, ws_client: WebSocketClient, db):
        self.agent = agent
        self.task_id = task_id
        self.ws_client = ws_client
        self.db = db
        self.preview_manager: PreviewManager = self.agent.preview_manager
        self._command_map = {
            'get_preview_url': self._handle_get_preview_url,
            'restart_preview': self._handle_restart_preview
        }
        
    def handle_command(self, command: str, input_data: Dict[str, Any] = None) -> bool:
        """Handle preview commands"""
        handler = self._command_map.get(command)
        if handler:
            try:
                handler(input_data or {})
                return True
            except Exception as e:
                print(f"Error handling preview command {command}: {e}")
                return False
        return False

    def _handle_get_preview_url(self, input_data: Dict[str, Any]):
        """Get the current preview URL"""
        try:
            if not self.preview_manager:
                self.ws_client.send_message("preview_error", {"message": "Preview manager not initialized"})
                return
                
            url = self.preview_manager.get_preview_url()
            status = self.preview_manager.get_preview_status()
            
            url = process_url(url=url)
            
            response_data = {
                "url": url,
                "status": status.get('status', 'not_started'),
                "timestamp": status.get('timestamp'),
                "available": url is not None
            }
            
            preview_status = PreviewResponse(
                status=response_data["status"],
                url=url,
                metadata=status
            )
            
            if self.ws_client:
                self.ws_client.send_message("preview_status", response_data)
                
            if self.db:
                self.db[TASKS_COLLECTION_NAME].update_one(
                    {"_id": self.task_id},
                    {"$set": {
                        "preview_url": url,
                        "preview_status": preview_status.model_dump()
                    }}
                )
                
        except Exception as e:
            print(f"Error getting preview URL: {e}")
            self.ws_client.send_message("preview_error", {
                "message": f"Error getting preview URL: {str(e)}"
            })

    def _handle_restart_preview(self, input_data: Dict[str, Any]):
        """Start restart operation in daemon thread"""
        if not self.preview_manager:
            self.ws_client.send_message("preview_error", {"message": "Preview manager not initialized"})
            return
            
        # Start restart in daemon thread
        restart_thread = threading.Thread(
            target=self._restart_preview_thread,
            args=(input_data,),
            daemon=True
        )
        restart_thread.start()
        
        # Send immediate response
        self.ws_client.send_message("preview_restart_response", {
            "status": "starting",
            "message": "Preview restart initiated",
            "timestamp": generate_timestamp()
        })

    def _restart_preview_thread(self, input_data: Dict[str, Any]):
        """Restart preview in background thread"""
        try:
            print(f"Starting preview restart in thread {threading.current_thread().name}")
            
            # Run the application restart
            result = self.preview_manager.run_application()
            
            if result:
                status_message = result.get('message', 'Preview restarted')
                
                self.ws_client.send_message("preview_restart_response", {
                    "status": "completed", 
                    "result": result,
                    "message": status_message,
                    "timestamp": generate_timestamp()
                })
                print("Preview restart completed successfully")
            else:
                self.ws_client.send_message("preview_error", {
                    "message": "Failed to restart preview"
                })
                print("Preview restart failed - no result")
                
        except Exception as e:
            print(f"Error in preview restart thread: {e}")
            self.ws_client.send_message("preview_error", {
                "message": f"Error restarting preview: {str(e)}"
            })
        finally:
            print(f"Preview restart thread {threading.current_thread().name} completed")

    def get_current_status(self) -> Dict[str, Any]:
        """Get comprehensive preview status for external queries"""
        try:
            if not self.preview_manager:
                return {"status": "error", "message": "Preview manager not initialized"}
                
            status = self.preview_manager.get_preview_status()
            url = self.preview_manager.get_preview_url()
            url = process_url(url=url)
            
            return {
                "status": status.get('status', 'unknown'),
                "url": url,
                "available": url is not None,
                "timestamp": status.get('timestamp'),
                "build_in_progress": getattr(self.preview_manager, 'build_in_progress', False),
                "framework": getattr(self.preview_manager, 'framework', 'unknown'),
                "platform": getattr(self.preview_manager, 'platform', 'unknown')
            }
        except Exception as e:
            print(f"Error getting current preview status: {e}")
            return {"status": "error", "message": str(e)}