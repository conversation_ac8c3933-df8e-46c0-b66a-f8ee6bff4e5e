from typing import Any, <PERSON><PERSON>
from app.core.git_tools import EnhancedGitTools
from code_generation_core_agent.agents.task_execution_agent import (
    TaskExecutionReporter, 

)
from app.core.websocket.client import WebSocketClient
from app.utils.message_utils import send_agent_message_custom


async def handle_git_lock_errors(reporter: TaskExecutionReporter, error_message: str) -> None:
    """Handle git lock and ref conflicts."""
    error_msg = (
        "Git conflicts detected. Please resolve the following issues:\n\n"
        f"{error_message}\n\n"
        "Recommended steps:\n"
        "1. Use the Git Dashboard to run 'git remote prune origin'\n"
        "2. Resolve any conflicting branches\n"
        "3. Clear any git locks if present"
    )
    send_agent_message_custom(
        ws_client=reporter.ws_client,
        task_id=reporter.task_id,
        message=error_msg,
        db=reporter.db
    )

async def handle_checkout_errors(reporter: TaskExecutionReporter, branch_name: str) -> None:
    """Handle branch checkout errors."""
    error_msg = (
        f"Unable to checkout branch '{branch_name}'.\n\n"
        "Please use the Git Dashboard to:\n"
        "1. Verify the branch exists\n"
        "2. Resolve any git conflicts\n"
        "3. Ensure you have the latest changes"
    )
    send_agent_message_custom(
        ws_client=reporter.ws_client,
        task_id=reporter.task_id,
        message=error_msg,
        db=reporter.db
    )

async def handle_local_changes_conflict(
    git_tool: EnhancedGitTools,
    reporter: TaskExecutionReporter,
    branch_name: str,
    base_path: str
) -> tuple[str, str]:
    """Handle conflicts with local changes during checkout."""
    error_msg = (
        "Local changes would be overwritten by checkout.\n"
        "Please use the Git Dashboard or editor to either:\n"
        "1. Commit your changes\n"
        "2. Stash your changes\n"
        "3. Discard your changes"
    )
    send_agent_message_custom(
        ws_client=reporter.ws_client,
        task_id=reporter.task_id,
        message=error_msg,
        db=reporter.db
    )
    
    # Stay on current branch when there's a conflict
    current_branch = get_current_branch(git_tool, base_path)
    return f"Staying on current branch: {current_branch}", current_branch

def get_current_branch(git_tool: EnhancedGitTools, base_path: str) -> str:
    """Get current git branch name."""
    return git_tool.execute_git_command(
        "rev-parse --abbrev-ref HEAD",
        repository_path=base_path
    ).strip()

async def handle_branch_checkout(
    git_tool: EnhancedGitTools,
    reporter: TaskExecutionReporter,
    ws_client: WebSocketClient,
    task_id: str,
    work_item_details: dict,
    agent_base_path: str,
    db: Any
) -> None:
    """Handle git branch checkout with error handling."""
    try:
        branch_name = work_item_details.get("branch")
        if not branch_name:
            return

        # Fetch latest changes
        fetch_message = git_tool.execute_git_command(
            "fetch --all",
            repository_path=agent_base_path
        )

        # Handle git lock and ref errors
        if "cannot lock ref" in fetch_message or "error: some local refs could not be updated" in fetch_message:
            await handle_git_lock_errors(reporter, fetch_message)
            return

        send_agent_message_custom(
            ws_client=ws_client,
            task_id=task_id,
            message=f"Fetching latest changes: {fetch_message}",
            db=db
        )

        # Attempt checkout
        checkout_message = git_tool.git_checkout(
            branch_or_tag=branch_name,
            repository_path=agent_base_path
        )

        # Handle various checkout scenarios
        if "pathspec" in checkout_message and "did not match any file(s) known to git" in checkout_message:
            await handle_checkout_errors(reporter, branch_name)
            checkout_message = git_tool.git_checkout(
                branch_or_tag=branch_name,
                repository_path=agent_base_path
            )
        elif "would be overwritten by checkout" in checkout_message:
            checkout_message, branch_name = await handle_local_changes_conflict(
                git_tool, reporter, branch_name, agent_base_path
            )
            work_item_details["branch"] = branch_name

        send_agent_message_custom(
            ws_client=ws_client,
            task_id=task_id,
            message=checkout_message,
            db=db
        )

    except Exception as e:
        error_msg = (
            "Git operation failed. Please use the Git Dashboard to resolve any issues.\n"
            f"Error: {str(e)}"
        )
        await reporter.send_message(error_msg)
        
        current_branch = get_current_branch(git_tool, agent_base_path)
        work_item_details["branch"] = current_branch
        
        send_agent_message_custom(
            ws_client=ws_client,
            task_id=task_id,
            message=f"Continuing with current branch: {current_branch}",
            db=db
        )
    finally:
        reporter.clear_states()

