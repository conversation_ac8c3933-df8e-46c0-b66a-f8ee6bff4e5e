from datetime import datetime
from typing import Dict, Any, Callable, Optional, List
from app.models.code_generation_model import Message
from app.core.constants import TASKS_COLLECTION_NAME
from app.core.git_tools import EnhancedGitTools
from app.connection.tenant_middleware import get_tenant_id
from app.core.git_status_monitor import GitStatusMonitor
from app.core.git_llm import GitLLM
from app.utils.code_generation_utils import get_logs_path
from app.utils.datetime_utils import generate_timestamp
from app.utils.task_utils import CheckPoints

class GitController:
    def __init__(self, task_id, git_tool:EnhancedGitTools, directories: List[str], ws_client, db, repository_metadata:List[Dict[str, Any]]=None):
        self.task_id = task_id
        self.git_tool = git_tool
        self.base_paths = directories
        self.current_repo_index = 0
        self.ws_client = ws_client
        self.db = db
        self.repository_metadata = repository_metadata
        self._command_map = self._initialize_command_map()
        self.status_monitor: GitStatusMonitor = None
        self.initialize_git_tool()
        # Reduced limits to 50
        self.MAX_MESSAGE_HISTORY = 50
        self.MAX_COMMIT_LOGS = 50
        self.auto_commit = True
        self.auto_push = False
        self.llm = GitLLM(task_id, logs_path=get_logs_path(task_id))
                
    def initialize_git_tool(self):
        self.git_tool.repository_metadata = self.current_repository_metadata
        self.git_tool.base_path = self.current_base_path
    
    def set_auto_push(self, auto_push: bool):
        self.auto_push = True
        
    def set_status_monitor(self, status_monitor: GitStatusMonitor):
        self.status_monitor = status_monitor
        self.status_monitor.set_current_base_path(self.current_base_path)
    
    @property
    def current_base_path(self):
        return self.base_paths[self.current_repo_index]

    @property
    def current_repository_metadata(self):
        return self.repository_metadata[self.current_repo_index] if self.repository_metadata else None

    def controller_status(self, _):
        status_data ={
            "auto-commit": self.auto_commit,
            "auto-push": self.auto_push,
            "current-repository": self.current_repository_metadata
        }
        self.ws_client.send_message("git_controller_status", status_data)

    def toggle_auto_commit(self, _):
        self.auto_commit = not (self.auto_commit)
        return self.controller_status(_)
        
    def toggle_auto_push(self, _):
        self.auto_push = not (self.auto_push)
        return self.controller_status(_)
            
    def switch_repository(self, command_data: Dict[str, Any]) -> str:
        """Switch to a different repository by index"""
        index = command_data.get('index')

        if index is None:
            return "Index is required to switch repositories."
        
        if 0 <= index < len(self.base_paths):
            self.current_repo_index = index
            repo_name = self.current_repository_metadata.get('repositoryName', f'Repository {index}') if self.current_repository_metadata else f'Repository {index}'
            message = f"Switched to repository: {repo_name}"
            self._send_message(message)
            
            # Send repository update to client
            self.ws_client.send_message("repository_update", {
                "current_repository_index": self.current_repo_index,
                "repository_name": repo_name,
                "repository_id": self.current_repository_metadata.get('repositoryId'),
                "total_repositories": len(self.base_paths),
                "current_branch": self.git_tool.execute_git_command(f"branch --show-current", repository_path=self.current_base_path)
            })
            self.git_tool = EnhancedGitTools(
                callback_functions=None,
                base_path=self.current_base_path,
                logger=None,
                repository_metadata=self.current_repository_metadata,
                tenant_id=get_tenant_id()
            )
            if self.status_monitor:
                self.status_monitor.set_git_tool(self.git_tool)
            
            # Refresh git status after switch
            self._handle_status({})
            return message
        else:
            return f"Invalid repository index. Must be between 0 and {len(self.base_paths) - 1}"
        

    def switch_to_checkpoints(self, checkpoints: CheckPoints):
        for checkpoint in checkpoints.check_points:
            _git_tool = EnhancedGitTools(
                callback_functions=None,
                base_path=checkpoint.path,
                logger=None,
                repository_metadata=self.repository_metadata[self.current_repo_index],
                tenant_id=get_tenant_id()
            )
            try:
                _git_tool.git_stash(repository_path=checkpoint.path, untracked=True)
            except Exception as e:  
                print(f"Error stashing changes: {e}")
            _git_tool.git_checkout(checkpoint.hash, repository_path=checkpoint.path)
            

    def handle_switch_to_message_checkpoints(self, input_data: Dict[str, Any] = None):
        message_id = input_data.get("message_id")
        print(f"Attempting to switch to checkpoints for message ID: {message_id}")
        try:
            messages = self.db[TASKS_COLLECTION_NAME].find_one({"_id": self.task_id}, {"messages": 1})
            if not messages:
                print(f"No messages found for task ID: {self.task_id}")
                return
                
            found = False
            for message in messages.get("messages", []):
                if message.get("id") == message_id:
                    found = True
                    checkpoints = message.get("check_points", [])
                    print(f"Found message with ID {message_id}, checkpoints: {checkpoints}")
                    if checkpoints:
                        print(f"Switching to {len(checkpoints)} checkpoints")
                        self.switch_to_checkpoints(CheckPoints(check_points=checkpoints))
                    else:
                        print("No checkpoints found in the message")
                    break
                    
            if not found:
                print(f"Message with ID {message_id} not found in task")
            if self.ws_client:
                self.ws_client.send_message("switch_to_checkpoints_status", {
                    "status": "completed"
                })
        except Exception as e:
            if self.ws_client:
                self.ws_client.send_message("switch_to_checkpoints_status", {
                    "status": "error"
                })
            print(f"Error switching to message checkpoints: {e}")
            
    def list_repositories(self, _):
        """List all available repositories"""
        print('Debug: Print all class variables')
        print("base_paths", self.base_paths)
        self.base_paths = []
        for data in self.repository_metadata:
            self.base_paths.append(data['current_path'])
            
        print("base_paths after", self.base_paths)
        print("r_m, ",self.repository_metadata)
        print("cri", self.current_repo_index)
        
        try:
            repo_list = []
            for i, path in enumerate(self.base_paths):
                repo_metadata = self.repository_metadata[i] if i < len(self.repository_metadata) else None
                repo_name = repo_metadata.get('repositoryName', f'Repository {i}') if repo_metadata else f'Repository {i}'
                repo_list.append({
                    'index': i,
                    'name': repo_name,
                    'path': path,
                    'service': repo_metadata.get('service'),
                    'organization': repo_metadata.get('organization'),
                    'current_branch': self.git_tool.execute_git_command(f"branch --show-current", repository_path=path),
                    'id': repo_metadata.get('repositoryId'),
                    'is_current': i == self.current_repo_index
                })
            
            # Send repository list to client
            self.ws_client.send_message("repository_list", {
                "repositories": repo_list
            })
            
            # Format message for display
            message = "**Available Repositories:**\n\n"
            for repo in repo_list:
                current_marker = " (current)" if repo['is_current'] else ""
                message += f"- [{repo['index']}] {repo['name']}{current_marker}\n"
            
            self._send_message(message)
            return repo_list
        except Exception as e:
            error_message = f"Error listing repositories: {str(e)}"
            self._send_message(error_message)
            return error_message

    def _initialize_command_map(self) -> Dict[str, Callable]:
        """Initialize mapping of command names to their handler methods"""
        return {
            'commit_code': self._handle_commit,
            'pull_code': self._handle_pull,
            'push_code': self._handle_push,
            'list_branches': self._handle_branch_list,
            'git_status': self._handle_status,
            'git_log': self._handle_log,
            'create_branch': self._handle_create_branch,
            'switch_branch': self._handle_switch_branch,
            'git_revert_to': self._handle_revert,
            'execute_git_command': self._handle_custom_command,
            'create_pr': self._handle_create_pr,
            'merge_to_kavia_main': self._handle_merge_to_kavia_main,
            'switch_to_checkpoints': self.handle_switch_to_message_checkpoints,
            'list_repositories': self.list_repositories,
            'switch_repository': self.switch_repository,
            'controller_status': self.controller_status,
            'auto_commit_toggle': self.toggle_auto_commit,
            'auto_push_toggle': self.toggle_auto_push
        }


    def _handle_create_pr(self, input_data: Dict[str, Any]):
        """Handle PR creation"""
        if not self.current_repository_metadata:
            self._send_message("No repository metadata available. Cannot create PR.")
            return

        source_branch = input_data.get('source_branch')
        target_branch = input_data.get('target_branch')
        title = input_data.get('title')
        description = input_data.get('description', '')

        if not all([source_branch, target_branch, title]):
            self._send_message("Missing required fields for PR creation. Need source_branch, target_branch, and title.")
            return

        try:
            result = self.git_tool.create_pull_request(
                source_branch=source_branch,
                target_branch=target_branch,
                title=title,
                description=description,
                repository_path=self.current_base_path,
                repository_metadata=self.current_repository_metadata
            )
            
            if isinstance(result, dict):
                pr_message = (
                    f"**Pull Request Created Successfully**\n\n"
                    f"- PR Number: {result.get('number')}\n"
                    f"- Status: {result.get('status')}\n"
                    f"- URL: {result.get('url')}\n"
                )
                self._send_message(pr_message)
            else:
                self._send_message(result)  # Send error message
        except Exception as e:
            error_message = f"Error creating pull request: {str(e)}"
            self._send_message(error_message)

    def _handle_merge_to_kavia_main(self, input_data: Dict[str, Any]):
        """Handle merging current branch to kavia-main"""
        try:
            print(f"Starting merge to kavia-main with input: {input_data}")
            # Get current branch name
            current_branch = self.git_tool.execute_git_command(
                "branch --show-current", 
                repository_path=self.current_base_path
            ).strip()
            
            print(f"Current branch detected: {current_branch}")
            
            if not current_branch:
                self._send_message("Error: Could not determine current branch")
                return
                
            if current_branch == "kavia-main":
                self._send_message("Already on kavia-main branch. No merge needed.")
                return
            
            self._send_message(f"Starting merge process from '{current_branch}' to 'kavia-main'...")
            
            # Step 1: Commit all changes to current branch
            print(f"Step 1: Preparing to commit changes to '{current_branch}'")
            self._send_message(f"Step 1: Committing all changes to '{current_branch}'...")
            commit_message = input_data.get("commit_message", f"Auto-commit before merging to kavia-main from {current_branch}")
            
            add_result = self.git_tool.git_add_all(repository_path=self.current_base_path)
            print(f"Git add result: {add_result}")
            self._send_message(add_result)
            
            commit_result = self.git_tool.git_commit(commit_message, repository_path=self.current_base_path)
            print(f"Git commit result: {commit_result}")
            self._send_message(commit_result)
            
            # Step 2: Switch to kavia-main branch
            print("Step 2: Switching to kavia-main branch")
            self._send_message("Step 2: Switching to 'kavia-main' branch...")
            switch_result = self.git_tool.switch_branch(
                branch_name="kavia-main",
                repository_path=self.current_base_path,
                create=False
            )
            print(f"Switch branch result: {switch_result}")
            self._send_message(switch_result)
            
            # Check if switch was successful
            new_branch = self.git_tool.execute_git_command(
                "branch --show-current", 
                repository_path=self.current_base_path
            ).strip()
            
            print(f"Current branch after switch: {new_branch}")
            
            if new_branch != "kavia-main":
                self._send_message(f"Error: Failed to switch to kavia-main. Currently on: {new_branch}")
                return
            
            # Step 3: Merge the original branch into kavia-main
            print(f"Step 3: Merging '{current_branch}' into 'kavia-main'")
            self._send_message(f"Step 3: Merging '{current_branch}' into 'kavia-main'...")
            merge_result = self.git_tool.execute_git_command(
                f"merge {current_branch}",
                repository_path=self.current_base_path
            )
            print(f"Merge result: {merge_result}")
            self._send_message(merge_result)
            
            # Step 4: Push the merged changes to remote
            print("Step 4: Pushing merged changes to remote kavia-main")
            self._send_message("Step 4: Pushing merged changes to remote repository...")
            push_result = self.git_tool.git_push(repository_path=self.current_base_path)
            print(f"Push result: {push_result}")
            self._send_message(push_result)
            
            # Final status check
            print("Merge to kavia-main completed successfully with push")
            self._send_message("Merge to kavia-main completed successfully! Changes have been pushed to remote repository.")
            self._handle_status({})  # Show current status
            
            # Send completion status
            if self.ws_client:
                self.ws_client.send_message("merge_to_kavia_main_completed", {
                    "status": "completed",
                    "message": "Merge to kavia-main operation completed successfully",
                    "original_branch": current_branch,
                    "target_branch": "kavia-main",
                    "timestamp": generate_timestamp()
                })
            
        except Exception as e:
            print(f"Error during merge to kavia-main: {str(e)}")
            error_message = f"Error during merge to kavia-main: {str(e)}"
            self._send_message(error_message)
            
    def handle_command(self, command: str, input_data: Dict[str, Any] = None) -> bool:
        """Dynamically handle git commands"""
        print(f"GitController.handle_command called with: {command} and input: {input_data}")
        print(f"Available commands in command map: {list(self._command_map.keys())}")
        handler = self._command_map.get(command)
        if handler:
            try:
                print(f"Found handler for command: {command}, executing...")
                handler(input_data or {})
                return True
            except Exception as e:
                print(f"Error handling git command {command}: {e}")
                return False
        else:
            print(f"No handler found for command: {command}")
        return False

    def _send_message(self, content: str):
        """Helper method to send messages through websocket and update DB"""
        # Truncate content if it exceeds 2500 characters
        if len(content) > 2500:
            content = content[:2497] + "..."
            
        content = self.llm.remove_sensitive_patterns(content)
        message_obj = Message(
            content=content, 
            sender="Git", 
            timestamp=generate_timestamp()
        )
        
        # Set additional attributes after initialization
        message_obj.parent_id = self.current_repository_metadata.get('repositoryId')
        message_obj.metadata = {
            "repositoryId": self.current_repository_metadata.get('repositoryId'),
            "repositoryName": self.current_repository_metadata.get('repositoryName'),
            "repositoryPath": self.current_base_path
        }
        
        if self.ws_client is not None:
            self.ws_client.send_message("git_command_output", message_obj.to_dict())

        if self.db is not None:
            # Update with limit using $slice
            self.db[TASKS_COLLECTION_NAME].update_one(
                {"_id": self.task_id},
                {
                    "$push": {
                        f"git_commands_outputs.{self.current_repository_metadata.get('repositoryId')}": {
                            "$each": [message_obj.to_dict()],
                            "$slice": -self.MAX_MESSAGE_HISTORY  # Keep only the latest messages
                        }
                    }
                }
            )

    def _handle_commit(self, input_data: Dict[str, Any]):
        commit_message = input_data.get("commit_message", "Automated commit")
        add_result = self.git_tool.git_add_all(repository_path=self.current_base_path)
        self._send_message(add_result)
        commit_result = self.git_tool.git_commit(commit_message, repository_path=self.current_base_path)
        self._send_message(commit_result)

    def _handle_pull(self, _):
        result = self.git_tool.git_pull(repository_path=self.current_base_path)
        self._send_message(result)

    def _handle_push(self, _):
        result = self.git_tool.git_push(repository_path=self.current_base_path)
        self._send_message(result)

    def _handle_branch_list(self, input_data: Dict[str, Any]):
        send_message = input_data.get('send_message', True)  # Default to True for backward compatibility
        raw_branches = self.git_tool.git_branch(repository_path=self.current_base_path, all_branches=True)
        branches = raw_branches.split('\n')
        
        # Always send git update
        self.ws_client.send_message("git_update", {
            "type": "branches",
            "branches": branches
        })
        
        # Only send formatted message if requested
        if send_message:
            formatted_output = self._format_branch_list(raw_branches)
            self._send_message(formatted_output)

    def _handle_status(self, _):
        result = self.git_tool.git_status(repository_path=self.current_base_path)
        self._send_message(result)

    def _handle_log(self, input_data: Dict[str, Any]):
        n = input_data.get('n', self.MAX_COMMIT_LOGS)  # Default to MAX_COMMIT_LOGS if not specified
        n = min(n, self.MAX_COMMIT_LOGS)  # Ensure n doesn't exceed maximum limit
        branch = input_data.get('branch')
        logs = self.git_tool.git_log(repository_path=self.current_base_path, n=n, branch=branch)
        
        if isinstance(logs, list):
            formatted_logs = [
                {
                    'hash': log['hash'],
                    'author': log['author'],
                    'date': log['date'],
                    'message': log['message']
                }
                for log in logs[:self.MAX_COMMIT_LOGS]  # Additional safety check
            ]
            self.ws_client.send_message("git_update", {
                "type": "commit_history",
                "commits": formatted_logs
            })
        else:
            self._send_message(logs)

    def _handle_create_branch(self, input_data: Dict[str, Any]):
        branch_name = input_data.get('branch_name')
        start_point = input_data.get('start_point')
        if branch_name:
            result = self.git_tool.create_branch_(
                branch_name=branch_name,
                repository_path=self.current_base_path,
                start_point=start_point,
                checkout=True
            )
            self._send_message(result)

    def _handle_switch_branch(self, input_data: Dict[str, Any]):
        branch_name = input_data.get('branch_name')
        create = input_data.get('create', False)
        if branch_name:
            result = self.git_tool.switch_branch(
                branch_name=branch_name,
                repository_path=self.current_base_path,
                create=create
            )
            self._send_message(result)

        self.ws_client.send_message("rollback_update", {
            "status":"completed"
        })

    def _handle_revert(self, input_data: Dict[str, Any]):
        commit_hash = input_data.get('commit_hash')
        result = self.git_tool.git_revert(
            commit_hash=commit_hash, 
            repository_path=self.current_base_path
        )
        self._send_message(result)
        self._handle_log({})  # Refresh git logs after revert

    def _handle_custom_command(self, input_data: Dict[str, Any]):
        command = input_data.get('command', '')
        if command.startswith('git '):
            command = command[4:]
        result = self.git_tool.execute_git_command(
            command=command,
            repository_path=self.current_base_path
        )
        self._send_message(result)

    def _format_branch_list(self, raw_branches: str) -> str:
        """Format branch list into markdown"""
        branches = raw_branches.split('\n')
        formatted_output = "**Current Branches:**\n\n"
        
        for branch in branches:
            branch = branch.strip()
            if not branch:
                continue
                
            if branch.startswith('*'):
                branch = branch.replace('*', '').strip()
                formatted_output += f"- **{branch}** (current)\n"
            else:
                formatted_output += f"- {branch}\n"

        return formatted_output