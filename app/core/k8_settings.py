import os
from typing import Dict, Any, Optional

class KubernetesConfig:
    """Simple Kubernetes configuration class"""
    
    def __init__(self):  # Fixed the __init__ method
        # Core settings
        self.PROJECT_ID = os.getenv('PROJECT_ID')
        self.ENVIRONMENT = os.getenv('ENVIRONMENT')
        
        # Kubernetes settings
        self.NAMESPACE = os.getenv('NAMESPACE', 'duploservices-kavia-beta')
        self.SERVICE_ACCOUNT_NAME = os.getenv('SERVICE_ACCOUNT_NAME', 'duploservices-kavia-beta-edit-user')
        
        # ConfigMap and Volume settings
        self.VOLUME_MOUNT = os.getenv('VOLUME_MOUNT', 'codegenservicedeploymentbeta')
        self.INGRESS_NAME = os.getenv('INGRESS_NAME', 'ingressservicebeta')
        self.CONFIG_MAP_NAME = os.getenv('CONFIG_MAP_NAME', 'codegenservicedeploymentbeta')
        
        # Container settings
        self.DOCKER_HOST = os.getenv('DOCKER_HOST', 'unix:///var/run/docker.sock')
        self.KUBECTL_IMAGE = os.getenv('KUBECTL_IMAGE', 'bitnami/kubectl:latest')
        
        # Job settings
        self.JOB_PARALLELISM = int(os.getenv('JOB_PARALLELISM', '1'))
        self.JOB_COMPLETIONS = int(os.getenv('JOB_COMPLETIONS', '1'))
        self.JOB_BACKOFF_LIMIT = int(os.getenv('JOB_BACKOFF_LIMIT', '6'))
        
        # Timeout settings
        self.JOB_COMPLETION_TIMEOUT = int(os.getenv('JOB_COMPLETION_TIMEOUT', '600'))
        self.POLLING_INTERVAL = int(os.getenv('POLLING_INTERVAL', '5'))
        
        # Validate required fields
        if not self.PROJECT_ID or not self.ENVIRONMENT:
            raise ValueError("PROJECT_ID and ENVIRONMENT must be set")
    
    def override(self, **kwargs) -> 'KubernetesConfig':
        """Override configuration values"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                raise AttributeError(f"Unknown configuration key: {key}")
        return self
    
    def get_job_name(self, action: str = "delete") -> str:
        """Generate job name"""
        return f"codegen-{self.PROJECT_ID}-{self.ENVIRONMENT}-{action}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary"""
        return {k: v for k, v in self.__dict__.items() if not k.startswith('_')}

# Global config instance
k8_settings = KubernetesConfig()