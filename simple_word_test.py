#!/usr/bin/env python3
"""Simple test for Word document extraction"""

from app.utils.file_utils.word_utils import extract_text_from_word_document
from docx import Document
from io import BytesIO

# Create a simple test document
doc = Document()
doc.add_paragraph('Hello World! This is a test document.')
doc.add_paragraph('This tests .docx file extraction.')

# Save to bytes
docx_stream = BytesIO()
doc.save(docx_stream)
docx_content = docx_stream.getvalue()

# Test extraction
result = extract_text_from_word_document(docx_content, 'docx')
print("Extraction result:")
print(result)
print("Test completed successfully!" if result else "Test failed!")
